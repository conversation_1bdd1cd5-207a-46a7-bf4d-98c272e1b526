const axios = require('axios');

async function testHandlersAPI() {
    try {
        console.log('🔐 正在登录admin用户...');
        
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:8080/login', {
            username: 'admin',
            password: 'admin123',
            code: '',
            uuid: ''
        });

        if (loginResponse.data.code !== 200) {
            console.error('❌ 登录失败:', loginResponse.data.msg);
            return;
        }

        const token = loginResponse.data.token;
        console.log('✅ admin登录成功');

        // 设置请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        console.log('\n🧪 测试经办人查询API...');
        
        // 2. 测试API调用
        console.log('测试用户: clerk_finance1');
        const handlersResponse = await axios.get('http://localhost:8080/oa/workflow/personnel/handlers/clerk_finance1');
        
        console.log('API响应状态:', handlersResponse.status);
        console.log('API响应数据:', JSON.stringify(handlersResponse.data, null, 2));
        
        if (handlersResponse.data.code === 200) {
            const handlers = handlersResponse.data.data;
            console.log(`✅ API调用成功，返回 ${handlers.length} 个经办人`);
            
            if (handlers.length > 0) {
                handlers.forEach(handler => {
                    console.log(`   - ${handler.nickName} (${handler.userName}) - ${handler.deptName}`);
                });
            } else {
                console.log('   (该科长负责的部门下暂无其他人员)');
            }
        } else {
            console.log(`❌ API调用失败: ${handlersResponse.data.msg}`);
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.response ? error.response.data : error.message);
        if (error.response) {
            console.error('错误状态码:', error.response.status);
            console.error('错误详情:', error.response.data);
        }
    }
}

testHandlersAPI();
