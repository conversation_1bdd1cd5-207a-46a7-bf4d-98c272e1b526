const axios = require('axios');

// 测试经办人查询功能
async function testHandlersAPI() {
    try {
        console.log('🧪 测试经办人查询功能...');
        
        // 1. 登录admin用户
        console.log('🔐 正在登录admin用户...');
        const loginResponse = await axios.post('http://localhost:8080/login', {
            username: 'admin',
            password: 'admin123',
            code: '',
            uuid: ''
        });
        
        if (loginResponse.data.code !== 200) {
            throw new Error('登录失败: ' + loginResponse.data.msg);
        }
        
        const token = loginResponse.data.token;
        console.log('✅ admin登录成功');
        
        // 设置请求头
        const headers = {
            'Authorization': 'Bearer ' + token,
            'Content-Type': 'application/json'
        };
        
        // 2. 测试获取用户信息接口
        console.log('📋 测试获取用户信息接口...');
        const userInfoResponse = await axios.get('http://localhost:8080/getInfo', { headers });
        console.log('用户信息响应:', {
            code: userInfoResponse.data.code,
            userName: userInfoResponse.data.user?.userName,
            nickName: userInfoResponse.data.user?.nickName,
            userId: userInfoResponse.data.user?.userId
        });
        
        // 3. 测试不同用户的经办人查询
        const testUsers = [
            { name: 'admin', desc: '系统管理员' },
            { name: 'liulei', desc: '机构负责人' },
            { name: 'manager_finance', desc: '财务科长' },
            { name: 'clerk_finance1', desc: '财务科员' }
        ];
        
        for (const user of testUsers) {
            console.log(`\n🔍 测试用户: ${user.name} (${user.desc})`);
            try {
                const handlersResponse = await axios.get(`http://localhost:8080/oa/workflow/personnel/handlers/${user.name}`, { headers });
                console.log(`API响应状态: ${handlersResponse.status}`);
                console.log(`API响应数据:`, {
                    code: handlersResponse.data.code,
                    msg: handlersResponse.data.msg,
                    dataLength: handlersResponse.data.data?.length || 0
                });
                
                if (handlersResponse.data.data && handlersResponse.data.data.length > 0) {
                    console.log(`✅ 返回 ${handlersResponse.data.data.length} 个经办人`);
                    console.log('前3个经办人:', handlersResponse.data.data.slice(0, 3).map(h => ({
                        userName: h.userName,
                        nickName: h.nickName,
                        deptName: h.deptName
                    })));
                } else {
                    console.log('✅ 返回 0 个经办人 (该用户不是科室负责人或无下属人员)');
                }
            } catch (error) {
                console.error(`❌ 测试用户 ${user.name} 失败:`, error.message);
            }
        }
        
        console.log('\n🎉 经办人查询功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 运行测试
testHandlersAPI();
