/**
 * 测试收文流程分管领导指派修复效果
 * 验证：
 * 1. 书记审批时，指派分管领导的候选人限定为角色为fgld的用户
 * 2. 书记选择张副主任、李副主任后，流程监控显示具体人名而不是角色组
 * 3. 分管领导审批时展示对科室负责人的选择
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;
const LOGOUT_URL = `${BASE_URL}/logout`;

// 测试用户
const USERS = {
    secretary: { username: 'secretary', password: 'admin123' }, // 办公室小李
    leader_admin: { username: 'leader_admin', password: 'admin123' }, // 张副主任
    leader_operation: { username: 'leader_operation', password: 'admin123' }, // 赵副主任
    manager_hr: { username: 'manager_hr', password: 'admin123' }, // 人事科长
    manager_operation: { username: 'manager_operation', password: 'admin123' } // 运管科长
};

let cookies = {};

// 登录函数
async function login(username, password) {
    try {
        const response = await axios.post(LOGIN_URL, {
            username: username,
            password: password,
            code: '1234',
            uuid: 'test-uuid'
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.code === 200) {
            // 提取cookies
            const setCookieHeader = response.headers['set-cookie'];
            if (setCookieHeader) {
                cookies[username] = setCookieHeader.map(cookie => cookie.split(';')[0]).join('; ');
            }
            console.log(`✅ ${username} 登录成功`);
            return true;
        } else {
            console.log(`❌ ${username} 登录失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${username} 登录异常:`, error.message);
        return false;
    }
}

// 创建收文
async function createReceiveDocument(username) {
    try {
        const response = await axios.post(`${BASE_URL}/oa/document/receive`, {
            title: '测试收文-验证分管领导指派',
            sourceUnit: '上级单位',
            receiveDate: '2025-07-27',
            secretLevel: '1',
            docContent: '这是一份测试收文，用于验证分管领导指派功能的修复效果。',
            remark: '测试用收文'
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            const docId = response.data.data;
            console.log(`✅ ${username} 创建收文成功，文档ID: ${docId}`);
            return docId;
        } else {
            console.log(`❌ ${username} 创建收文失败:`, response.data.msg);
            return null;
        }
    } catch (error) {
        console.log(`❌ ${username} 创建收文异常:`, error.message);
        return null;
    }
}

// 提交审批
async function submitApproval(username, docId, workflowKey) {
    try {
        const response = await axios.post(`${BASE_URL}/oa/workflow/start`, {
            businessKey: `doc_receive_${docId}`,
            processDefinitionKey: workflowKey,
            variables: {
                docId: docId,
                docType: 'receive',
                title: '测试收文-验证分管领导指派'
            }
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            const processInstanceId = response.data.data;
            console.log(`✅ ${username} 提交审批成功，流程实例ID: ${processInstanceId}`);
            return processInstanceId;
        } else {
            console.log(`❌ ${username} 提交审批失败:`, response.data.msg);
            return null;
        }
    } catch (error) {
        console.log(`❌ ${username} 提交审批异常:`, error.message);
        return null;
    }
}

// 获取可选择的分管领导列表
async function getAvailableLeaders(username) {
    try {
        const response = await axios.get(`${BASE_URL}/oa/workflow/process/leaders`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ ${username} 获取分管领导列表成功:`, response.data.data);
            return response.data.data;
        } else {
            console.log(`❌ ${username} 获取分管领导列表失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ ${username} 获取分管领导列表异常:`, error.message);
        return [];
    }
}

// 获取指定分管领导下的科室负责人列表
async function getAvailableManagers(username, leaderId) {
    try {
        const response = await axios.get(`${BASE_URL}/oa/workflow/process/managers/${leaderId}`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ ${username} 获取科室负责人列表成功 (分管领导: ${leaderId}):`, response.data.data);
            return response.data.data;
        } else {
            console.log(`❌ ${username} 获取科室负责人列表失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ ${username} 获取科室负责人列表异常:`, error.message);
        return [];
    }
}

// 查看流程监控
async function getProcessMonitor(username) {
    try {
        const response = await axios.get(`${BASE_URL}/oa/workflow/monitor/list`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ ${username} 获取流程监控成功`);
            return response.data.rows || [];
        } else {
            console.log(`❌ ${username} 获取流程监控失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ ${username} 获取流程监控异常:`, error.message);
        return [];
    }
}

// 主测试函数
async function runTest() {
    console.log('🚀 开始测试收文流程分管领导指派修复效果...\n');
    
    // 1. 登录办公室小李
    console.log('📝 步骤1: 登录办公室小李');
    if (!await login(USERS.secretary.username, USERS.secretary.password)) {
        return;
    }
    
    // 2. 创建收文
    console.log('\n📝 步骤2: 创建收文');
    const docId = await createReceiveDocument(USERS.secretary.username);
    if (!docId) {
        return;
    }
    
    // 3. 提交审批
    console.log('\n📝 步骤3: 提交审批');
    const processInstanceId = await submitApproval(USERS.secretary.username, docId, 'document_receive_approval_v2');
    if (!processInstanceId) {
        return;
    }
    
    // 4. 测试获取分管领导列表（验证只返回fgld角色的用户）
    console.log('\n📝 步骤4: 测试获取分管领导列表');
    const leaders = await getAvailableLeaders(USERS.secretary.username);
    
    // 验证分管领导列表
    console.log('\n🔍 验证分管领导列表:');
    if (leaders.length > 0) {
        console.log('✅ 分管领导列表不为空');
        leaders.forEach(leader => {
            console.log(`   - ${leader.nickName} (${leader.userName})`);
        });
        
        // 检查是否只包含具有fgld角色的用户
        const expectedLeaders = ['leader_admin', 'leader_operation'];
        const actualLeaders = leaders.map(l => l.userName);
        const hasOnlyFgldUsers = expectedLeaders.every(expected => actualLeaders.includes(expected));
        
        if (hasOnlyFgldUsers) {
            console.log('✅ 分管领导列表只包含具有fgld角色的用户');
        } else {
            console.log('❌ 分管领导列表包含非fgld角色的用户');
        }
    } else {
        console.log('❌ 分管领导列表为空');
    }
    
    // 5. 测试获取科室负责人列表
    console.log('\n📝 步骤5: 测试获取科室负责人列表');
    if (leaders.length > 0) {
        for (const leader of leaders) {
            console.log(`\n🔍 测试分管领导 ${leader.nickName} (${leader.userName}) 的科室负责人:`);
            const managers = await getAvailableManagers(USERS.secretary.username, leader.userName);
            
            if (managers.length > 0) {
                console.log('✅ 科室负责人列表不为空');
                managers.forEach(manager => {
                    console.log(`   - ${manager.nickName} (${manager.userName}) - ${manager.deptName}`);
                });
            } else {
                console.log('⚠️  科室负责人列表为空');
            }
        }
    }
    
    // 6. 查看流程监控（验证显示具体人名而不是角色组）
    console.log('\n📝 步骤6: 查看流程监控');
    const processMonitor = await getProcessMonitor(USERS.secretary.username);
    
    console.log('\n🔍 验证流程监控显示:');
    if (processMonitor.length > 0) {
        const currentProcess = processMonitor.find(p => p.processInstanceId === processInstanceId);
        if (currentProcess) {
            console.log('✅ 找到当前流程实例');
            console.log(`   流程名称: ${currentProcess.processName || '未知'}`);
            console.log(`   当前处理人: ${currentProcess.currentAssignee || '未知'}`);
            console.log(`   流程状态: ${currentProcess.status || '未知'}`);
            
            // 检查是否显示具体人名而不是角色组
            if (currentProcess.currentAssignee && !currentProcess.currentAssignee.includes('dynamic_leaders')) {
                console.log('✅ 流程监控显示具体人名，未显示角色组');
            } else {
                console.log('❌ 流程监控仍显示角色组或未显示处理人');
            }
        } else {
            console.log('❌ 未找到当前流程实例');
        }
    } else {
        console.log('❌ 流程监控列表为空');
    }
    
    console.log('\n🎉 测试完成！');
}

// 运行测试
runTest().catch(console.error);
