const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;
const WORKFLOW_HISTORY_URL = `${BASE_URL}/oa/workflow/history`;

// 测试用户
const TEST_USER = {
    username: 'admin',
    password: 'admin123'
};

// 全局token
let authToken = '';

/**
 * 登录获取token
 */
async function login() {
    try {
        const response = await axios.post(LOGIN_URL, TEST_USER);
        if (response.data && response.data.token) {
            authToken = response.data.token;
            console.log('✅ 登录成功，获取到token');
            return true;
        } else {
            console.error('❌ 登录失败，未获取到token');
            return false;
        }
    } catch (error) {
        console.error('❌ 登录请求失败:', error.message);
        return false;
    }
}

/**
 * 创建带认证的axios实例
 */
function createAuthAxios() {
    return axios.create({
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    });
}

/**
 * 测试流程历史查询
 */
async function testWorkflowHistory(processInstanceId) {
    console.log(`\n🔍 测试流程历史查询: ${processInstanceId}`);
    
    try {
        const authAxios = createAuthAxios();
        const response = await authAxios.get(`${WORKFLOW_HISTORY_URL}/${processInstanceId}`);
        
        console.log('📊 API响应状态:', response.status);
        console.log('📊 API响应数据:', {
            code: response.data.code,
            msg: response.data.msg,
            dataLength: response.data.data ? response.data.data.length : 0
        });
        
        if (response.data.code === 200 && response.data.data) {
            const historyTasks = response.data.data;
            console.log(`✅ 查询到 ${historyTasks.length} 个历史任务`);
            
            // 按时间排序显示任务
            historyTasks.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));
            
            console.log('\n📋 历史任务详情:');
            historyTasks.forEach((task, index) => {
                console.log(`${index + 1}. ${task.taskName}`);
                console.log(`   处理人: ${task.assigneeName || task.assignee || '未分配'}`);
                console.log(`   开始时间: ${task.createTime}`);
                console.log(`   完成时间: ${task.completeTime || '未完成'}`);
                console.log(`   处理意见: ${task.comment || '无'}`);
                console.log('');
            });
            
            // 检查是否有并行任务
            const parallelTasks = historyTasks.filter(task => {
                const sameTimeStart = historyTasks.filter(t => 
                    t.createTime === task.createTime && t.taskId !== task.taskId
                );
                return sameTimeStart.length > 0;
            });
            
            if (parallelTasks.length > 0) {
                console.log(`🔀 发现 ${parallelTasks.length} 个并行任务:`);
                parallelTasks.forEach(task => {
                    console.log(`   - ${task.taskName} (${task.assigneeName || task.assignee})`);
                });
            } else {
                console.log('ℹ️ 未发现并行任务');
            }
            
            return historyTasks;
        } else {
            console.error('❌ 查询失败:', response.data.msg);
            return null;
        }
    } catch (error) {
        console.error('❌ 查询流程历史失败:', error.message);
        if (error.response) {
            console.error('错误响应:', error.response.data);
        }
        return null;
    }
}

/**
 * 主测试函数
 */
async function main() {
    console.log('🧪 开始测试流程历史查询功能...\n');
    
    // 1. 登录
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.error('❌ 登录失败，终止测试');
        return;
    }
    
    // 2. 测试不同的流程实例ID
    const testProcessInstanceIds = [
        // 这里需要替换为实际的流程实例ID
        // 可以从流程监控页面获取
        '2501',  // 示例ID，需要替换
        '5001',  // 示例ID，需要替换
        '7501'   // 示例ID，需要替换
    ];
    
    for (const processInstanceId of testProcessInstanceIds) {
        await testWorkflowHistory(processInstanceId);
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
    
    console.log('\n🎉 流程历史查询测试完成！');
}

// 运行测试
main().catch(error => {
    console.error('❌ 测试执行失败:', error);
});
