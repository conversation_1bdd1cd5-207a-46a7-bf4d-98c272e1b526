const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;
const CREATE_RECEIVE_URL = `${BASE_URL}/oa/document/receive`;
const COMPLETE_TASK_URL = `${BASE_URL}/oa/workflow/task/complete`;
const TODO_TASKS_URL = `${BASE_URL}/oa/workflow/task/todo`;
const HISTORY_URL = `${BASE_URL}/oa/workflow/history`;

// 测试用户
const TEST_USERS = {
    secretary: { username: 'secretary', password: 'admin123', name: '王书记' },
    leader_admin: { username: 'leader_admin', password: 'admin123', name: '张副主任' },
    leader_operation: { username: 'leader_operation', password: 'admin123', name: '赵副主任' },
    leader_finance: { username: 'leader_finance', password: 'admin123', name: '李副主任' }
};

// 全局token
let authToken = '';

/**
 * 登录获取token
 */
async function login(user) {
    try {
        const response = await axios.post(LOGIN_URL, user);
        if (response.data && response.data.token) {
            authToken = response.data.token;
            console.log(`✅ ${user.name} 登录成功`);
            return true;
        } else {
            console.error(`❌ ${user.name} 登录失败`);
            return false;
        }
    } catch (error) {
        console.error(`❌ ${user.name} 登录请求失败:`, error.message);
        return false;
    }
}

/**
 * 创建带认证的axios实例
 */
function createAuthAxios() {
    return axios.create({
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    });
}

/**
 * 创建收文并启动审批流程
 */
async function createReceiveDocument() {
    console.log('\n📝 创建收文并启动审批流程...');
    
    try {
        const authAxios = createAuthAxios();
        const documentData = {
            docNumber: 'TEST-' + Date.now(),
            docTitle: '多分管领导并行审批测试收文',
            sourceUnit: '测试单位',
            receiveDate: new Date().toISOString().split('T')[0],
            urgencyLevel: '1',
            securityLevel: '1',
            secretLevel: '1',
            docContent: '这是一个测试多分管领导并行审批的收文文档内容。',
            workflowType: 'document_receive_approval_v2'
        };
        
        const response = await authAxios.post(CREATE_RECEIVE_URL, documentData);
        
        if (response.data.code === 200) {
            console.log('✅ 收文创建成功');
            return response.data.data;
        } else {
            console.error('❌ 收文创建失败:', response.data.msg);
            return null;
        }
    } catch (error) {
        console.error('❌ 创建收文失败:', error.message);
        return null;
    }
}

/**
 * 查询用户的待办任务
 */
async function getUserTodoTasks(userName) {
    try {
        const authAxios = createAuthAxios();
        const response = await authAxios.get(TODO_TASKS_URL);
        
        if (response.data.code === 200) {
            const tasks = response.data.rows || [];
            console.log(`📋 ${userName} 有 ${tasks.length} 个待办任务`);
            return tasks;
        } else {
            console.error(`❌ 查询 ${userName} 待办任务失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.error(`❌ 查询 ${userName} 待办任务失败:`, error.message);
        return [];
    }
}

/**
 * 完成任务
 */
async function completeTask(taskId, comment, variables = {}) {
    try {
        const authAxios = createAuthAxios();
        const response = await authAxios.post(`${COMPLETE_TASK_URL}/${taskId}`, {
            comment: comment,
            variables: variables
        });
        
        if (response.data.code === 200) {
            console.log(`✅ 任务 ${taskId} 完成成功`);
            return true;
        } else {
            console.error(`❌ 任务 ${taskId} 完成失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.error(`❌ 完成任务 ${taskId} 失败:`, error.message);
        return false;
    }
}

/**
 * 测试多分管领导并行审批
 */
async function testMultiLeaderApproval() {
    console.log('🧪 开始测试多分管领导并行审批机制...\n');
    
    // 1. 书记登录并创建收文
    console.log('步骤1: 书记创建收文');
    const loginSuccess = await login(TEST_USERS.secretary);
    if (!loginSuccess) {
        console.error('❌ 书记登录失败，终止测试');
        return;
    }
    
    const document = await createReceiveDocument();
    if (!document) {
        console.error('❌ 创建收文失败，终止测试');
        return;
    }
    
    // 2. 书记审批并选择多个分管领导
    console.log('\n步骤2: 书记审批并选择多个分管领导');
    const secretaryTasks = await getUserTodoTasks('书记');
    if (secretaryTasks.length === 0) {
        console.error('❌ 书记没有待办任务');
        return;
    }
    
    const secretaryTask = secretaryTasks[0];
    const selectedLeaders = 'leader_admin,leader_operation,leader_finance'; // 选择3个分管领导
    
    const secretaryApprovalSuccess = await completeTask(secretaryTask.taskId, '书记审批通过，选择多个分管领导进行并行审批', {
        selectedLeaders: selectedLeaders
    });
    
    if (!secretaryApprovalSuccess) {
        console.error('❌ 书记审批失败');
        return;
    }
    
    console.log(`✅ 书记审批完成，选择了分管领导: ${selectedLeaders}`);
    
    // 3. 验证每个分管领导都收到了任务
    console.log('\n步骤3: 验证每个分管领导都收到了任务');
    
    const leaders = [
        { key: 'leader_admin', user: TEST_USERS.leader_admin },
        { key: 'leader_operation', user: TEST_USERS.leader_operation },
        { key: 'leader_finance', user: TEST_USERS.leader_finance }
    ];
    
    let allLeadersHaveTasks = true;
    const leaderTasks = {};
    
    for (const leader of leaders) {
        await login(leader.user);
        const tasks = await getUserTodoTasks(leader.user.name);
        
        if (tasks.length > 0) {
            console.log(`✅ ${leader.user.name} 收到了审批任务`);
            leaderTasks[leader.key] = tasks[0];
        } else {
            console.log(`❌ ${leader.user.name} 没有收到审批任务`);
            allLeadersHaveTasks = false;
        }
    }
    
    if (!allLeadersHaveTasks) {
        console.error('❌ 不是所有分管领导都收到了任务，并行审批机制可能有问题');
        return;
    }
    
    // 4. 分管领导依次完成审批
    console.log('\n步骤4: 分管领导依次完成审批');
    
    for (const leader of leaders) {
        await login(leader.user);
        const task = leaderTasks[leader.key];
        
        const approvalSuccess = await completeTask(task.taskId, `${leader.user.name}审批通过`, {
            selectedDeptManager: 'manager_finance' // 选择财务科长作为科室负责人
        });
        
        if (approvalSuccess) {
            console.log(`✅ ${leader.user.name} 审批完成`);
        } else {
            console.log(`❌ ${leader.user.name} 审批失败`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
    
    // 5. 验证流程是否正确继续
    console.log('\n步骤5: 验证流程是否正确继续到下一步');
    
    // 登录财务科长查看是否有待办任务
    await login({ username: 'manager_finance', password: 'admin123', name: '财务科长' });
    const managerTasks = await getUserTodoTasks('财务科长');
    
    if (managerTasks.length > 0) {
        console.log('✅ 所有分管领导审批完成后，流程正确继续到科室负责人审批');
    } else {
        console.log('❌ 流程没有正确继续到下一步，可能并行审批机制有问题');
    }
    
    console.log('\n🎉 多分管领导并行审批测试完成！');
}

// 运行测试
testMultiLeaderApproval().catch(error => {
    console.error('❌ 测试执行失败:', error);
});
