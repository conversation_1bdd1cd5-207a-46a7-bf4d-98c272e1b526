/**
 * 测试API访问权限和分管领导相关接口
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;

let cookies = {};

// 登录函数
async function login(username, password) {
    try {
        const response = await axios.post(LOGIN_URL, {
            username: username,
            password: password,
            code: '1234',
            uuid: 'test-uuid'
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.data.code === 200) {
            // 提取cookies
            const setCookieHeader = response.headers['set-cookie'];
            if (setCookieHeader) {
                cookies[username] = setCookieHeader.map(cookie => cookie.split(';')[0]).join('; ');
            }
            console.log(`✅ ${username} 登录成功`);
            return true;
        } else {
            console.log(`❌ ${username} 登录失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${username} 登录异常:`, error.message);
        return false;
    }
}

// 测试获取分管领导列表
async function testGetLeaders(username) {
    try {
        console.log(`\n🔍 测试获取分管领导列表 (${username})`);
        const response = await axios.get(`${BASE_URL}/oa/workflow/process/leaders`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ 获取分管领导列表成功:`, response.data.data);
            return response.data.data;
        } else {
            console.log(`❌ 获取分管领导列表失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ 获取分管领导列表异常:`, error.response?.data || error.message);
        return [];
    }
}

// 测试获取科室负责人列表
async function testGetManagers(username, leaderId) {
    try {
        console.log(`\n🔍 测试获取科室负责人列表 (${username}, 分管领导: ${leaderId})`);
        const response = await axios.get(`${BASE_URL}/oa/workflow/process/managers/${leaderId}`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ 获取科室负责人列表成功:`, response.data.data);
            return response.data.data;
        } else {
            console.log(`❌ 获取科室负责人列表失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ 获取科室负责人列表异常:`, error.response?.data || error.message);
        return [];
    }
}

// 测试流程监控
async function testProcessMonitor(username) {
    try {
        console.log(`\n🔍 测试流程监控 (${username})`);
        const response = await axios.get(`${BASE_URL}/oa/workflow/monitor/list`, {
            headers: {
                'Cookie': cookies[username]
            }
        });
        
        if (response.data.code === 200) {
            console.log(`✅ 获取流程监控成功，共 ${response.data.rows?.length || 0} 条记录`);
            if (response.data.rows && response.data.rows.length > 0) {
                response.data.rows.slice(0, 3).forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.processName || '未知流程'} - 当前处理人: ${item.currentAssignee || '未知'}`);
                });
            }
            return response.data.rows || [];
        } else {
            console.log(`❌ 获取流程监控失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ 获取流程监控异常:`, error.response?.data || error.message);
        return [];
    }
}

// 主测试函数
async function runTest() {
    console.log('🚀 开始测试API访问权限和分管领导相关接口...\n');
    
    // 测试用户列表
    const testUsers = [
        { username: 'admin', password: 'admin123', name: '管理员' },
        { username: 'secretary', password: 'admin123', name: '办公室小李' },
        { username: 'leader_admin', password: 'admin123', name: '张副主任' }
    ];
    
    for (const user of testUsers) {
        console.log(`\n📝 测试用户: ${user.name} (${user.username})`);
        
        // 1. 登录
        if (!await login(user.username, user.password)) {
            continue;
        }
        
        // 2. 测试获取分管领导列表
        const leaders = await testGetLeaders(user.username);
        
        // 3. 如果有分管领导，测试获取科室负责人
        if (leaders.length > 0) {
            const firstLeader = leaders[0];
            await testGetManagers(user.username, firstLeader.userName);
        }
        
        // 4. 测试流程监控
        await testProcessMonitor(user.username);
        
        console.log(`\n✅ ${user.name} 测试完成`);
    }
    
    console.log('\n🎉 所有测试完成！');
}

// 运行测试
runTest().catch(console.error);
