# Build and Release Folders
bin-debug/
bin-release/
[Oo]bj/
[Bb]in/

# Other files and folders
.settings/

# Executables
*.swf
*.air
*.ipa
*.apk

# Project files, i.e. `.project`, `.actionScriptProperties` and `.flexProperties`
# should NOT be excluded as they contain compiler settings and other important
# information for Eclipse / Flash Builder.
/logs/
/.claude/
/target/
/ruoyi-generator/target/
/ruoyi-framework/target/
/ruoyi-common/target/
/ruoyi-admin/target/
/ruoyi-system/target/
/AI编程规范说明文档.md
/.idea/
/assign_roles_to_users.js
/authenticated_verification.js
/check_bpmn_deployment.js
/check_dept_104_manager_role.js
/check_office_roles.js
/complete_approval_workflow_verification.js
/complete_office_director_approval.js
/complete_verification_plan.js
/database_query_tool.class
/debug_dept_assignment.js
/detailed_step_by_step_verification.js
/detailed_verification_log_1753497254173.json
/diagnose_role_assignment.js
/disable_captcha.sql
/enable_and_redeploy_workflow.js
/final_verification.js
/FINAL_VERIFICATION_PLAN.md
/fixed_task_assignment_verification.js
/frontend_verification.js
/initialize_test_users.sql
/LICENSE
/MANUAL_VERIFICATION_GUIDE.md
/oa-hs.iml
/oa_system_verification_plan.js
/package.json
/package-lock.json
