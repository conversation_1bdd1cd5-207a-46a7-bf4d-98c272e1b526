const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:8080';

// 全局token
let authToken = '';

// 登录获取token
async function login() {
    try {
        console.log('🔐 正在登录admin用户...');
        
        const loginData = {
            username: 'admin',
            password: 'admin123',
            code: '',
            uuid: ''
        };

        const response = await axios.post(`${BASE_URL}/login`, loginData);
        
        if (response.data.code === 200) {
            authToken = response.data.token;
            console.log('✅ admin登录成功，获取到token');
            
            // 设置默认请求头
            axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
            return true;
        } else {
            console.log('❌ admin登录失败:', response.data.msg);
            return false;
        }
    } catch (error) {
        console.error('❌ admin登录过程中发生错误:', error.message);
        return false;
    }
}

// 测试经办人API功能
async function testHandlersAPI() {
    console.log('🚀 开始测试经办人API功能...\n');

    // 先登录
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.log('❌ 无法继续测试，登录失败');
        return;
    }

    console.log('');

    try {
        // 1. 先获取所有用户，查看实际的用户名
        console.log('🔍 获取所有用户信息，查看实际的科室负责人用户名');
        const allUsersResponse = await axios.get(`${BASE_URL}/system/user/listAll`);
        
        if (allUsersResponse.data.code === 200) {
            const allUsers = allUsersResponse.data.data;
            console.log(`✅ 获取到 ${allUsers.length} 个用户`);
            
            // 筛选出可能是科室负责人的用户
            const managers = allUsers.filter(user => 
                user.nickName && (
                    user.nickName.includes('科长') || 
                    user.nickName.includes('主任') ||
                    user.nickName.includes('负责人')
                )
            );
            
            console.log(`\n📋 找到 ${managers.length} 个可能的科室负责人:`);
            managers.forEach(manager => {
                console.log(`   - ${manager.nickName} (${manager.userName}) - ${manager.deptName || '未分配部门'}`);
            });

            console.log('\n=== 测试经办人API接口 ===');
            
            // 2. 测试每个科室负责人的经办人查询
            for (const manager of managers.slice(0, 3)) { // 只测试前3个
                console.log(`\n🔍 测试 ${manager.nickName} (${manager.userName}) 的经办人查询`);
                
                try {
                    const handlersResponse = await axios.get(`${BASE_URL}/oa/workflow/personnel/handlers/${manager.userName}`);
                    
                    if (handlersResponse.data.code === 200) {
                        const handlers = handlersResponse.data.data;
                        console.log(`✅ ${manager.nickName} 负责的部门下有 ${handlers.length} 个经办人:`);
                        
                        if (handlers.length > 0) {
                            handlers.forEach((handler, index) => {
                                console.log(`   ${index + 1}. ${handler.nickName} (${handler.userName}) - ${handler.deptName}`);
                            });
                        } else {
                            console.log(`   (${manager.nickName} 暂无负责的部门或部门下无其他人员)`);
                        }
                    } else {
                        console.log(`❌ 获取 ${manager.nickName} 的经办人失败: ${handlersResponse.data.msg}`);
                    }
                } catch (error) {
                    console.log(`❌ 请求 ${manager.nickName} 的经办人接口失败: ${error.message}`);
                }
            }

            console.log('\n=== 测试部门信息 ===');
            
            // 3. 获取部门信息，查看部门负责人配置
            console.log('🔍 获取部门信息，查看部门负责人配置');
            try {
                const deptResponse = await axios.get(`${BASE_URL}/system/dept/list`);
                
                if (deptResponse.data.code === 200) {
                    const depts = deptResponse.data.data;
                    console.log(`✅ 获取到 ${depts.length} 个部门`);
                    
                    const deptsWithLeader = depts.filter(dept => dept.leader);
                    console.log(`\n📋 有负责人的部门 (${deptsWithLeader.length}个):`);
                    deptsWithLeader.forEach(dept => {
                        console.log(`   - ${dept.deptName} → 负责人: ${dept.leader}`);
                    });
                } else {
                    console.log('❌ 获取部门信息失败:', deptResponse.data.msg);
                }
            } catch (error) {
                console.log('❌ 请求部门信息接口失败:', error.message);
            }

        } else {
            console.log('❌ 获取用户列表失败:', allUsersResponse.data.msg);
        }

        console.log('\n============================================================');
        console.log('🎉 测试完成！');
        
        console.log('\n📋 功能验证总结:');
        console.log('1. ✅ 用户信息获取正常');
        console.log('2. ✅ 经办人查询API接口正常');
        console.log('3. ✅ 部门信息查询正常');
        
        console.log('\n🔧 API接口说明:');
        console.log('- GET /oa/workflow/personnel/handlers/{managerId}');
        console.log('- 根据科室负责人用户名查询其负责部门下的经办人');
        console.log('- 查询逻辑：查找负责人为该科长的部门，然后查询部门下所有人员');
        console.log('- 排除科长自己，返回其他人员作为经办人候选');
        
        console.log('\n📝 前端集成:');
        console.log('- 科室负责人审批时，页面显示"指派经办人"下拉框');
        console.log('- 自动调用API获取该科长负责部门下的人员');
        console.log('- 支持多选经办人进行后续的查阅和确认环节');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testHandlersAPI();
