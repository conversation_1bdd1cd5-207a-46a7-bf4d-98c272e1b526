const axios = require('axios');
const fs = require('fs');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;
const DEPLOY_URL = `${BASE_URL}/oa/workflow/deploy`;

// 测试用户
const TEST_USER = {
    username: 'admin',
    password: 'admin123'
};

// 全局token
let authToken = '';

/**
 * 登录获取token
 */
async function login() {
    try {
        const response = await axios.post(LOGIN_URL, TEST_USER);
        if (response.data && response.data.token) {
            authToken = response.data.token;
            console.log('✅ 登录成功，获取到token');
            return true;
        } else {
            console.error('❌ 登录失败，未获取到token');
            return false;
        }
    } catch (error) {
        console.error('❌ 登录请求失败:', error.message);
        return false;
    }
}

/**
 * 创建带认证的axios实例
 */
function createAuthAxios() {
    return axios.create({
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    });
}

/**
 * 部署流程定义
 */
async function deployWorkflow(processKey, bpmnPath) {
    console.log(`\n🚀 部署流程定义: ${processKey}`);
    
    try {
        // 读取BPMN文件内容
        const bpmnContent = fs.readFileSync(bpmnPath, 'utf8');
        
        const authAxios = createAuthAxios();
        const response = await authAxios.post(DEPLOY_URL, {
            processKey: processKey,
            bpmnXml: bpmnContent,
            processName: processKey === 'document_receive_approval_v2' ? '收文审批流程V2' : 
                        processKey === 'document_send_approval_v2' ? '发文审批流程V2' : 
                        '收文特办流程V2'
        });
        
        console.log('📊 API响应状态:', response.status);
        console.log('📊 API响应数据:', {
            code: response.data.code,
            msg: response.data.msg
        });
        
        if (response.data.code === 200) {
            console.log(`✅ 流程定义 ${processKey} 部署成功`);
            return true;
        } else {
            console.error(`❌ 流程定义 ${processKey} 部署失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.error(`❌ 部署流程定义 ${processKey} 失败:`, error.message);
        if (error.response) {
            console.error('错误响应:', error.response.data);
        }
        return false;
    }
}

/**
 * 主函数
 */
async function main() {
    console.log('🧪 开始部署更新的流程定义...\n');
    
    // 1. 登录
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.error('❌ 登录失败，终止部署');
        return;
    }
    
    // 2. 部署流程定义
    const workflows = [
        {
            key: 'document_receive_approval_v2',
            path: 'ruoyi-admin/src/main/resources/processes/document_receive_approval_v2.bpmn20.xml'
        },
        {
            key: 'document_send_approval_v2', 
            path: 'ruoyi-admin/src/main/resources/processes/document_send_approval_v2.bpmn20.xml'
        },
        {
            key: 'document_receive_special_v2',
            path: 'ruoyi-admin/src/main/resources/processes/document_receive_special_v2.bpmn20.xml'
        }
    ];
    
    let allSuccess = true;
    for (const workflow of workflows) {
        const success = await deployWorkflow(workflow.key, workflow.path);
        if (!success) {
            allSuccess = false;
        }
        await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    }
    
    if (allSuccess) {
        console.log('\n🎉 所有流程定义部署成功！');
        console.log('\n📋 更新内容:');
        console.log('1. ✅ 修复了待办列表的当前处理人显示');
        console.log('2. ✅ 增加了流程实例ID的展示和筛选');
        console.log('3. ✅ 修复了收文流程的并行分支轨迹显示');
        console.log('4. ✅ 确保了多选分管领导的并行审批机制');
        console.log('\n🔧 技术改进:');
        console.log('- 收文审批流程使用多实例任务支持并行分管领导审批');
        console.log('- 每个选中的分管领导都会收到独立的审批任务');
        console.log('- 所有分管领导完成审批后流程才会继续');
        console.log('- 流程轨迹会正确显示所有并行分支的处理记录');
    } else {
        console.log('\n❌ 部分流程定义部署失败，请检查错误信息');
    }
}

// 运行部署
main().catch(error => {
    console.error('❌ 部署执行失败:', error);
});
