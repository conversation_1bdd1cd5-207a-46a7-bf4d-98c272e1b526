import { isObject, isString } from 'min-dash';

const DEFAULT_TRANSLATIONS = {
  // Labels
  'Activate the global connect tool': '激活全局连接工具',
  'Append {type}': '追加 {type}',
  'Add Lane above': '添加到通道之上',
  'Divide into two Lanes': '分割成两个通道',
  'Divide into three Lanes': '分割成三个通道',
  'Add Lane below': '添加到通道之下',
  'Append compensation activity': '追加补偿活动',
  'Change type': '更改类型',
  'Connect using Association': '使用关联连接',
  'Connect using Sequence/MessageFlow': '使用顺序/消息流连接',
  'Connect using DataInputAssociation': '使用数据输入关联连接',
  'Remove': '移除',
  'Activate the hand tool': '激活抓手工具',
  'Activate the lasso tool': '激活套索工具',
  'Activate the create/remove space tool': '激活创建/删除空间工具',
  'Create expanded SubProcess': '创建可折叠子流程',
  'Create IntermediateThrowEvent/BoundaryEvent': '创建中间抛出/边界事件',
  'Create Pool/Participant': '创建池/参与者',
  'Parallel Multi Instance': '并行多实例',
  'Sequential Multi Instance': '串行多实例',
  'Loop': '循环',
  'Ad-hoc': '即席',
  'Create {type}': '创建 {type}',
  'Task': '任务',
  'Send Task': '发送任务',
  'Receive Task': '接收任务',
  'User Task': '用户任务',
  'Manual Task': '手动任务',
  'Business Rule Task': '业务规则任务',
  'Service Task': '服务任务',
  'Script Task': '脚本任务',
  'Call Activity': '调用活动',
  'Sub Process (collapsed)': '子流程（折叠）',
  'Sub Process (expanded)': '子流程（展开）',
  'Start Event': '开始事件',
  'StartEvent': '开始事件',
  'Intermediate Throw Event': '中间抛出事件',
  'End Event': '结束事件',
  'EndEvent': '结束事件',
  'Create StartEvent': '创建开始事件',
  'Create EndEvent': '创建结束事件',
  'Create Task': '创建任务',
  'Create User Task': '创建用户任务',
  'Create Gateway': '创建网关',
  'Create DataObjectReference': '创建数据对象',
  'Create DataStoreReference': '创建数据存储',
  'Create SubProcess': '创建子流程',
  'Create Pool/Participant': '创建池/参与者',
  'Create Group': '创建分组',
  'Gateway': '网关',
  'Exclusive Gateway': '排他网关',
  'Parallel Gateway': '并行网关',
  'Inclusive Gateway': '包容网关',
  'Complex Gateway': '复杂网关',
  'Event based Gateway': '事件网关',
  'Transaction': '事务',
  'Sub Process': '子流程',
  'Event Sub Process': '事件子流程',
  'Collapsed Pool': '折叠池',
  'Expanded Pool': '展开池',

  // Errors
  'no parent for {element} in {parent}': '在{parent}中没有{element}的父元素',
  'no shape type specified': '未指定形状类型',
  'flow elements must be children of a process': '流程元素必须是流程的子元素',
  'out of bounds release': '越界释放',
  'more than {count} child lanes': '超过{count}个子通道',
  'element required': '需要元素',
  'diagram not part of bpmn:Definitions': '图表不是bpmn:Definitions的一部分',
  'no diagram to display': '没有可显示的图表',
  'no process or collaboration to display': '没有可显示的流程或协作',
  'element {element} referenced by {referenced}#{property} not yet drawn': '元素{element}的引用{referenced}#{property}尚未绘制',
  'already rendered {element}': '已渲染{element}',
  'failed to import {element}': '导入{element}失败',

  // Properties Framework
  'Id': 'ID',
  'Name': '名称',
  'General': '常规',
  'Details': '详情',
  'Message Name': '消息名称',
  'Message': '消息',
  'Initiator': '发起人',
  'Correlation Key': '关联键',
  'Topic': '主题',
  'Condition': '条件',
  'Variable Name': '变量名',
  'Variable Event': '变量事件',
  'Specify more than one variable change event as a comma separated list.': '多个变量事件以逗号隔开',
  'Wait for Completion': '等待完成',
  'Activity Ref': '活动引用',
  'Version Tag': '版本标签',
  'Executable': '可执行',
  'External': '外部',
  'Implementation': '实现',
  'Assignee': '处理人',
  'Candidate Users': '候选用户',
  'Candidate Groups': '候选组',
  'Due Date': '到期时间',
  'Follow Up Date': '跟进日期',
  'Priority': '优先级',
  'The due date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)': '可以是EL表达式（如 ${someDate}）或ISO日期（如 2015-06-26T09:54:00）',
  'The follow up date as an EL expression (e.g. ${someDate} or an ISO date (e.g. 2015-06-26T09:54:00)': '可以是EL表达式（如 ${someDate}）或ISO日期（如 2015-06-26T09:54:00）',
  'Forms': '表单',
  'Form Key': '表单Key',
  'Form Fields': '表单字段',
  'Business Key': '业务Key',
  'Type': '类型',
  'Label': '标签',
  'Default Value': '默认值',
  'Validation': '校验',
  'Add Constraint': '添加约束',
  'Parameters': '参数',
  'Input/Output': '输入/输出',
  'Input Parameters': '输入参数',
  'Output Parameters': '输出参数',
  'Text': '文本',
  'must be a valid email address': '必须是有效的邮件地址',
  'must not be empty': '不能为空',
  'must be a number': '必须是数字',
  'must be greater than ${min}': '必须大于 ${min}',
  'must be lower than ${max}': '必须小于 ${max}',
  'must be between ${min} and ${max}': '必须在 ${min} 和 ${max} 之间',
  'must be a date in the format YYYY-MM-DD': '必须是YYYY-MM-DD格式的日期',
  'must match pattern ${pattern}': '必须匹配模式 ${pattern}',
  'must not contain spaces': '不能包含空格',
};


/**
 * A simple translation function.
 *
 * @param {Object<string, string>} [translations]
 *
 * @return {Function}
 */
export default function customTranslate(translations) {

  translations = translations || {};

  translations = {
    ...DEFAULT_TRANSLATIONS,
    ...translations
  };

  return function(template, replacements) {

    replacements = replacements || {};

    // Translate
    template = translations[template] || template;

    // Replace
    return template.replace(/{([^}]+)}/g, function(_, key) {

      let str = replacements[key];

      if (isObject(str) && isString(str.name)) {
        str = str.name;
      }

      if (translations[str]) {
        return translations[str];
      }

      return str || '{' + key + '}';
    });
  };
} 