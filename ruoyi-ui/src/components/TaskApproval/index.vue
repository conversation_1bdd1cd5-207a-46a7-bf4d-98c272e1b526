<template>
  <div class="task-approval">
    <!-- 任务审批对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      width="800px"
      append-to-body
      @close="handleClose"
    >
      <div class="approval-content">
        <!-- 任务信息 -->
        <div class="task-info">
          <h4>任务信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务名称">{{ taskInfo.taskName }}</el-descriptions-item>
            <el-descriptions-item label="流程名称">{{ taskInfo.processName }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ taskInfo.createTime }}</el-descriptions-item>
            <el-descriptions-item label="到期时间">{{ taskInfo.dueDate || '无' }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 审批操作 -->
        <div class="approval-actions">
          <h4>审批操作</h4>
          <el-radio-group v-model="approvalAction" @change="handleActionChange">
            <el-radio label="approve">同意</el-radio>
            <el-radio label="reject">拒绝</el-radio>
            <el-radio label="return">回退</el-radio>
          </el-radio-group>
        </div>

        <!-- 人员选择（书记选择分管领导、分管领导选择科室负责人等） -->
        <div v-if="needPersonnelSelection" class="personnel-selection">
          <h4>{{ personnelSelectionTitle }}</h4>
          <div class="selection-area">
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="openPersonnelSelector"
            >
              选择{{ personnelTypeText }}
            </el-button>

            <!-- 已选择人员显示 -->
            <div v-if="selectedPersonnel.length > 0" class="selected-personnel">
              <div class="selected-title">已选择{{ personnelTypeText }} ({{ selectedPersonnel.length }})</div>
              <div class="selected-list">
                <el-tag
                  v-for="person in selectedPersonnel"
                  :key="person.userId"
                  closable
                  @close="removeSelectedPerson(person)"
                  style="margin-right: 10px; margin-bottom: 5px;"
                >
                  {{ person.nickName }} ({{ person.deptName }})
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 审批意见 -->
        <div class="approval-comment">
          <h4>审批意见</h4>
          <el-input
            v-model="approvalComment"
            type="textarea"
            :rows="4"
            :placeholder="commentPlaceholder"
            maxlength="500"
            show-word-limit
          />
        </div>

        <!-- 催办功能 -->
        <div v-if="showUrgeButton" class="urge-section">
          <h4>催办功能</h4>
          <el-button
            type="warning"
            icon="el-icon-bell"
            @click="handleUrge"
            :loading="urging"
          >
            催办此任务
          </el-button>
          <span class="urge-tip">点击催办将通知相关人员及时处理</span>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="submitting"
        >
          提 交
        </el-button>
      </div>
    </el-dialog>

    <!-- 人员选择器 -->
    <PersonnelSelector
      v-model="showPersonnelSelector"
      :title="personnelSelectorTitle"
      :multiple="true"
      :personnel-type="personnelType"
      :related-id="relatedId"
      :selected-data="selectedPersonnel"
      @confirm="handlePersonnelConfirm"
    />
  </div>
</template>

<script>
import PersonnelSelector from '@/components/PersonnelSelector'
import { completeTask, rejectTask, returnTask, urgeTask } from "@/api/oa/workflow"

export default {
  name: "TaskApproval",
  components: {
    PersonnelSelector
  },
  props: {
    // 是否显示对话框
    value: {
      type: Boolean,
      default: false
    },
    // 任务信息
    taskData: {
      type: Object,
      default: () => ({})
    },
    // 是否显示催办按钮
    showUrgeButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      submitting: false,
      urging: false,
      approvalAction: 'approve',
      approvalComment: '',
      selectedPersonnel: [],
      showPersonnelSelector: false,
      personnelType: 'all',
      relatedId: ''
    };
  },
  computed: {
    dialogTitle() {
      return `任务审批 - ${this.taskInfo.taskName || ''}`;
    },
    taskInfo() {
      return this.taskData || {};
    },
    needPersonnelSelection() {
      // 根据任务名称判断是否需要人员选择
      const taskName = this.taskInfo.taskName || '';
      return taskName.includes('书记审批') ||
             taskName.includes('分管领导审批') ||
             taskName.includes('科室负责人审批');
    },
    personnelSelectionTitle() {
      const taskName = this.taskInfo.taskName || '';
      if (taskName.includes('书记审批')) {
        return '选择参与审批的分管领导';
      } else if (taskName.includes('分管领导审批')) {
        return '选择参与审批的科室负责人';
      } else if (taskName.includes('科室负责人审批')) {
        return '选择参与查阅的经办人';
      }
      return '选择人员';
    },
    personnelTypeText() {
      const taskName = this.taskInfo.taskName || '';
      if (taskName.includes('书记审批')) {
        return '分管领导';
      } else if (taskName.includes('分管领导审批')) {
        return '科室负责人';
      } else if (taskName.includes('科室负责人审批')) {
        return '经办人';
      }
      return '人员';
    },
    personnelSelectorTitle() {
      return `选择${this.personnelTypeText}`;
    },
    commentPlaceholder() {
      switch (this.approvalAction) {
        case 'approve':
          return '请输入审批意见（同意）...';
        case 'reject':
          return '请输入拒绝原因...';
        case 'return':
          return '请输入回退原因...';
        default:
          return '请输入审批意见...';
      }
    }
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.resetForm();
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.approvalAction = 'approve';
      this.approvalComment = '';
      this.selectedPersonnel = [];
      this.submitting = false;
      this.urging = false;
      this.updatePersonnelType();
    },

    // 更新人员类型
    updatePersonnelType() {
      const taskName = this.taskInfo.taskName || '';
      if (taskName.includes('书记审批')) {
        this.personnelType = 'leaders';
        this.relatedId = '';
      } else if (taskName.includes('分管领导审批')) {
        this.personnelType = 'managers';
        this.relatedId = this.taskInfo.assignee || ''; // 当前分管领导ID
      } else if (taskName.includes('科室负责人审批')) {
        this.personnelType = 'handlers';
        this.relatedId = this.taskInfo.deptId || ''; // 当前科室ID
      }
    },

    // 审批操作变化
    handleActionChange(action) {
      // 如果选择拒绝或回退，清空人员选择
      if (action === 'reject' || action === 'return') {
        this.selectedPersonnel = [];
      }
    },

    // 打开人员选择器
    openPersonnelSelector() {
      this.showPersonnelSelector = true;
    },

    // 人员选择确认
    handlePersonnelConfirm(personnel) {
      this.selectedPersonnel = personnel;
    },

    // 移除选中人员
    removeSelectedPerson(person) {
      const index = this.selectedPersonnel.findIndex(p => p.userId === person.userId);
      if (index > -1) {
        this.selectedPersonnel.splice(index, 1);
      }
    },

    // 催办处理
    async handleUrge() {
      if (!this.approvalComment.trim()) {
        this.$message.warning('请输入催办消息');
        return;
      }

      this.urging = true;
      try {
        const response = await urgeTask(this.taskInfo.taskId, {
          message: this.approvalComment
        });

        if (response.code === 200) {
          this.$message.success('催办成功');
          this.approvalComment = '';
        } else {
          this.$message.error('催办失败：' + response.msg);
        }
      } catch (error) {
        console.error('催办失败:', error);
        this.$message.error('催办失败');
      } finally {
        this.urging = false;
      }
    },

    // 提交审批
    async handleSubmit() {
      if (!this.approvalComment.trim()) {
        this.$message.warning('请输入审批意见');
        return;
      }

      // 如果需要人员选择且是同意操作，检查是否已选择人员
      if (this.needPersonnelSelection && this.approvalAction === 'approve' && this.selectedPersonnel.length === 0) {
        this.$message.warning(`请选择${this.personnelTypeText}`);
        return;
      }

      this.submitting = true;
      try {
        let response;
        const requestData = {
          comment: this.approvalComment
        };

        // 根据审批操作调用不同的API
        switch (this.approvalAction) {
          case 'approve':
            // 添加人员选择变量
            if (this.selectedPersonnel.length > 0) {
              const selectedIds = this.selectedPersonnel.map(p => p.userId);
              const taskName = this.taskInfo.taskName || '';

              if (taskName.includes('书记审批')) {
                requestData.variables = { selectedLeaders: selectedIds };
              } else if (taskName.includes('分管领导审批')) {
                requestData.variables = { selectedManagers: selectedIds };
              } else if (taskName.includes('科室负责人审批')) {
                requestData.variables = { selectedHandlers: selectedIds };
              }
            }
            response = await completeTask(this.taskInfo.taskId, requestData);
            break;
          case 'reject':
            response = await rejectTask(this.taskInfo.taskId, requestData);
            break;
          case 'return':
            response = await returnTask(this.taskInfo.taskId, requestData);
            break;
        }

        if (response.code === 200) {
          this.$message.success('操作成功');
          this.$emit('success');
          this.handleClose();
        } else {
          this.$message.error('操作失败：' + response.msg);
        }
      } catch (error) {
        console.error('审批操作失败:', error);
        this.$message.error('操作失败');
      } finally {
        this.submitting = false;
      }
    },

    // 关闭对话框
    handleClose() {
      this.visible = false;
      this.$emit('input', false);
    }
  }
};
</script>

<style scoped>
.task-approval {
  /* 组件样式 */
}

.approval-content {
  padding: 10px 0;
}

.task-info,
.approval-actions,
.personnel-selection,
.approval-comment,
.urge-section {
  margin-bottom: 20px;
}

.task-info h4,
.approval-actions h4,
.personnel-selection h4,
.approval-comment h4,
.urge-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-size: 14px;
  font-weight: bold;
}

.selection-area {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  padding: 15px;
  text-align: center;
}

.selected-personnel {
  margin-top: 15px;
  text-align: left;
}

.selected-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
  font-size: 13px;
}

.selected-list {
  max-height: 100px;
  overflow-y: auto;
}

.urge-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

.dialog-footer {
  text-align: right;
}
</style>
