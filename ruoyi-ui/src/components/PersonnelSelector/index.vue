<template>
  <div class="personnel-selector">
    <!-- 人员选择对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      width="800px"
      append-to-body
      @close="handleClose"
    >
      <div class="selector-content">
        <!-- 搜索框 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索人员姓名或部门"
            prefix-icon="el-icon-search"
            clearable
            @input="handleSearch"
          />
        </div>

        <!-- 人员列表 -->
        <div class="personnel-list">
          <el-table
            ref="personnelTable"
            :data="filteredPersonnelList"
            @selection-change="handleSelectionChange"
            height="400"
          >
            <el-table-column
              v-if="multiple"
              type="selection"
              width="55"
            />
            <el-table-column
              v-else
              width="55"
            >
              <template slot-scope="scope">
                <el-radio
                  v-model="selectedSingle"
                  :label="scope.row.userId"
                  @change="handleSingleSelect(scope.row)"
                >&nbsp;</el-radio>
              </template>
            </el-table-column>

            <el-table-column prop="nickName" label="姓名" width="120" />
            <el-table-column prop="userName" label="用户名" width="120" />
            <el-table-column prop="deptName" label="部门" width="150" />
            <el-table-column prop="roles" label="角色" min-width="200">
              <template slot-scope="scope">
                <el-tag
                  v-for="role in scope.row.roles"
                  :key="role"
                  size="mini"
                  style="margin-right: 5px;"
                >
                  {{ role }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 已选择人员 -->
        <div v-if="multiple && selectedPersonnel.length > 0" class="selected-personnel">
          <div class="selected-title">已选择人员 ({{ selectedPersonnel.length }})</div>
          <div class="selected-list">
            <el-tag
              v-for="person in selectedPersonnel"
              :key="person.userId"
              closable
              @close="removeSelected(person)"
              style="margin-right: 10px; margin-bottom: 5px;"
            >
              {{ person.nickName }} ({{ person.deptName }})
            </el-tag>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAvailableLeaders, getAvailableManagers, getAvailableHandlers, getAllAvailablePersonnel } from "@/api/oa/workflow";

export default {
  name: "PersonnelSelector",
  props: {
    // 是否显示对话框
    value: {
      type: Boolean,
      default: false
    },
    // 对话框标题
    title: {
      type: String,
      default: "选择人员"
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 人员类型：leaders(分管领导), managers(科室负责人), handlers(经办人), all(所有人员)
    personnelType: {
      type: String,
      default: "all"
    },
    // 关联ID（如分管领导ID、科室ID等）
    relatedId: {
      type: String,
      default: ""
    },
    // 已选择的人员
    selectedData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      searchKeyword: "",
      personnelList: [],
      filteredPersonnelList: [],
      selectedPersonnel: [],
      selectedSingle: null
    };
  },
  computed: {
    dialogTitle() {
      return this.title;
    }
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.loadPersonnelData();
        this.selectedPersonnel = [...this.selectedData];
      }
    },
    personnelType() {
      if (this.visible) {
        this.loadPersonnelData();
      }
    },
    relatedId() {
      if (this.visible) {
        this.loadPersonnelData();
      }
    }
  },
  methods: {
    // 加载人员数据
    async loadPersonnelData() {
      try {
        let response;
        switch (this.personnelType) {
          case "leaders":
            response = await getAvailableLeaders();
            break;
          case "managers":
            if (this.relatedId) {
              response = await getAvailableManagers(this.relatedId);
            } else {
              response = { data: [] };
            }
            break;
          case "handlers":
            if (this.relatedId) {
              response = await getAvailableHandlers(this.relatedId);
            } else {
              response = { data: [] };
            }
            break;
          case "all":
          default:
            response = await getAllAvailablePersonnel();
            break;
        }

        if (response.code === 200) {
          this.personnelList = response.data || [];
          this.filteredPersonnelList = [...this.personnelList];
        } else {
          this.$message.error("获取人员列表失败：" + response.msg);
        }
      } catch (error) {
        console.error("加载人员数据失败:", error);
        this.$message.error("获取人员列表失败");
      }
    },

    // 搜索处理
    handleSearch() {
      if (!this.searchKeyword) {
        this.filteredPersonnelList = [...this.personnelList];
      } else {
        const keyword = this.searchKeyword.toLowerCase();
        this.filteredPersonnelList = this.personnelList.filter(person =>
          person.nickName.toLowerCase().includes(keyword) ||
          person.userName.toLowerCase().includes(keyword) ||
          (person.deptName && person.deptName.toLowerCase().includes(keyword))
        );
      }
    },

    // 多选处理
    handleSelectionChange(selection) {
      this.selectedPersonnel = selection;
    },

    // 单选处理
    handleSingleSelect(person) {
      this.selectedPersonnel = [person];
    },

    // 移除已选择人员
    removeSelected(person) {
      const index = this.selectedPersonnel.findIndex(p => p.userId === person.userId);
      if (index > -1) {
        this.selectedPersonnel.splice(index, 1);
        // 更新表格选择状态
        this.$refs.personnelTable.toggleRowSelection(person, false);
      }
    },

    // 确认选择
    handleConfirm() {
      if (this.selectedPersonnel.length === 0) {
        this.$message.warning("请选择至少一个人员");
        return;
      }

      this.$emit("confirm", this.selectedPersonnel);
      this.handleClose();
    },

    // 关闭对话框
    handleClose() {
      this.visible = false;
      this.$emit("input", false);
      this.searchKeyword = "";
      this.selectedPersonnel = [];
      this.selectedSingle = null;
    }
  }
};
</script>

<style scoped>
.personnel-selector {
  /* 组件样式 */
}

.selector-content {
  padding: 10px 0;
}

.search-bar {
  margin-bottom: 15px;
}

.personnel-list {
  margin-bottom: 15px;
}

.selected-personnel {
  border-top: 1px solid #ebeef5;
  padding-top: 15px;
}

.selected-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.selected-list {
  max-height: 100px;
  overflow-y: auto;
}

.dialog-footer {
  text-align: right;
}
</style>
