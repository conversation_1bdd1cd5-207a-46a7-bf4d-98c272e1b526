// TinyMCE 全局配置
// 注意：这些导入在开发环境中是必需的
require('tinymce/tinymce')
require('tinymce/themes/silver')
require('tinymce/plugins/lists')
require('tinymce/plugins/link')
require('tinymce/plugins/image')
require('tinymce/plugins/table')
require('tinymce/plugins/paste')
require('tinymce/plugins/fullscreen')
require('tinymce/icons/default')
require('tinymce-i18n/langs/zh_CN')

// 全局配置
const TINYMCE_CONFIG = {
  language: 'zh_CN',
  theme: 'silver',
  skin: 'oxide',
  branding: false, // 隐藏品牌标识
  promotion: false, // 隐藏推广信息

  // 中文界面配置
  language_url: '/static/tinymce/langs/zh_CN.js',

  // 状态栏配置
  statusbar: true,
  elementpath: true,

  // 内容样式
  content_css: false,

  // 粘贴配置
  paste_data_images: true,
  paste_webkit_styles: 'none',
  paste_merge_formats: true,

  // 图片上传配置
  automatic_uploads: true,
  file_picker_types: 'image',

  // 表格配置
  table_default_attributes: {
    border: '1'
  },
  table_default_styles: {
    'border-collapse': 'collapse'
  },

  // 菜单栏中文配置 - 精简版
  menubar: 'edit insert format',
  menu: {
    edit: { title: '编辑', items: 'undo redo | cut copy paste pastetext | selectall | searchreplace' },
    insert: { title: '插入', items: 'image link inserttable | hr | pagebreak | insertdatetime' },
    format: { title: '格式', items: 'bold italic underline strikethrough superscript subscript | fontformats fontsizes | forecolor backcolor | removeformat | align | outdent indent' }
  },

  // 基础配置 - 精简插件
  plugins: [
    'lists link image table',
    'paste fullscreen searchreplace',
    'insertdatetime pagebreak'
  ],

  // 工具栏配置 - 精简2行常用工具
  toolbar_mode: 'wrap',
  toolbar: [
    'undo redo | fontselect fontsizeselect | bold italic underline | forecolor backcolor | alignleft aligncenter alignright alignjustify',
    'bullist numlist | outdent indent | link image table | applyredheader insertseal | fullscreen'
  ],

  // 字体配置
  font_family_formats: [
    '仿宋_GB2312=仿宋_GB2312,仿宋,STFangsong,serif',
    '方正小标宋简体=方正小标宋简体,黑体,STHeiti,sans-serif',
    '楷体_GB2312=楷体_GB2312,楷体,STKaiti,serif',
    '宋体=宋体,SimSun,serif',
    '微软雅黑=微软雅黑,Microsoft YaHei,sans-serif'
  ].join('; '),

  fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 26px 28px 30px 32px 36px',

  // 内容样式
  content_style: `
    body {
      font-family: '仿宋_GB2312', '仿宋', 'STFangsong', serif;
      font-size: 16px;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      max-width: 210mm;
      margin: 0 auto;
      background: white;
      position: relative;
    }
    h1, h2, h3, h4, h5, h6 {
      font-family: '方正小标宋简体', '黑体', 'STHeiti', sans-serif;
      font-weight: bold;
    }
    .red-header {
      color: #FF0000;
      font-weight: bold;
      text-align: center;
      font-size: 24px;
      margin-bottom: 20px;
    }
    .doc-number {
      text-align: right;
      margin-bottom: 10px;
      font-size: 14px;
    }
    .seal-container {
      position: absolute !important;
      z-index: 1000 !important;
      pointer-events: none !important;
    }
    .seal-image {
      width: 120px;
      height: 120px;
      opacity: 0.8;
      border: none !important;
      pointer-events: none !important;
      user-select: none !important;
    }
  `,

  // 根块配置
  forced_root_block: 'div',

  // 自定义设置函数
  setup: function (editor) {
    // 添加套红按钮
    editor.ui.registry.addButton('applyredheader', {
      text: '套红',
      icon: 'template',
      tooltip: '应用红头模板',
      onAction: function () {
        console.log('套红按钮被点击');

        // 检查是否为最后审批人
        const docId = getDocumentIdFromEditor(editor);
        if (!docId) {
          editor.windowManager.alert('无法获取文档ID');
          return;
        }

        // 触发父组件的套红功能
        if (window.parent && window.parent.showTemplateDialog) {
          window.parent.showTemplateDialog();
        } else {
          editor.windowManager.alert('套红功能仅限最后审批人使用');
        }
      }
    });

    // 添加印章按钮
    editor.ui.registry.addButton('insertseal', {
      text: '印章',
      icon: 'bookmark',
      tooltip: '插入印章',
      onAction: function () {
        console.log('印章按钮被点击');

        // 检查是否为最后审批人
        const docId = getDocumentIdFromEditor(editor);
        if (!docId) {
          editor.windowManager.alert('无法获取文档ID');
          return;
        }

        // 从数据库获取可用印章列表
        loadAvailableSeals().then(seals => {
          console.log('获取到的印章列表:', seals);

          if (seals.length === 0) {
            editor.windowManager.alert('暂无可用印章');
            return;
          }

          // 构建印章选项
          const sealItems = seals.map(seal => ({
            text: seal.sealName,
            value: seal.sealId.toString()
          }));

          console.log('构建的印章选项:', sealItems);

          // 打开印章选择对话框
          editor.windowManager.open({
            title: '选择印章',
            body: {
              type: 'panel',
              items: [
                {
                  type: 'selectbox',
                  name: 'sealId',
                  label: '选择印章',
                  items: sealItems
                },
                {
                  type: 'selectbox',
                  name: 'sealPosition',
                  label: '印章位置',
                  items: [
                    { text: '右下角', value: 'bottom-right' },
                    { text: '居中', value: 'center' },
                    { text: '左下角', value: 'bottom-left' }
                  ]
                }
              ]
            },
            buttons: [
              {
                type: 'cancel',
                text: '取消'
              },
              {
                type: 'submit',
                text: '插入',
                primary: true
              }
            ],
            onSubmit: function (api) {
              const data = api.getData();
              console.log('用户选择的数据:', data);

              const selectedSeal = seals.find(seal => seal.sealId.toString() === data.sealId);
              console.log('找到的印章:', selectedSeal);

              insertRealSeal(editor, selectedSeal, data.sealPosition);
              api.close();
            }
          });
        }).catch(error => {
          console.error('获取印章列表失败:', error);
          editor.windowManager.alert('获取印章列表失败，请稍后重试');
        });
      }
    });

    // 自定义中文工具提示
    editor.on('init', function() {
      // 重写工具栏按钮的中文提示
      const chineseTooltips = {
        'undo': '撤销',
        'redo': '重做',
        'cut': '剪切',
        'copy': '复制',
        'paste': '粘贴',
        'bold': '粗体',
        'italic': '斜体',
        'underline': '下划线',
        'strikethrough': '删除线',
        'superscript': '上标',
        'subscript': '下标',
        'forecolor': '字体颜色',
        'backcolor': '背景颜色',
        'alignleft': '左对齐',
        'aligncenter': '居中对齐',
        'alignright': '右对齐',
        'alignjustify': '两端对齐',
        'outdent': '减少缩进',
        'indent': '增加缩进',
        'bullist': '项目符号',
        'numlist': '编号列表',
        'link': '插入链接',
        'unlink': '取消链接',
        'image': '插入图片',
        'media': '插入媒体',
        'table': '插入表格',
        'hr': '插入分隔线',
        'pagebreak': '插入分页符',
        'searchreplace': '查找替换',
        'code': '源代码',
        'preview': '预览',
        'print': '打印',
        'fullscreen': '全屏',
        'help': '帮助'
      };

      // 应用中文提示
      Object.keys(chineseTooltips).forEach(key => {
        const button = editor.ui.registry.getAll().buttons[key];
        if (button && button.tooltip) {
          button.tooltip = chineseTooltips[key];
        }
      });
    });
  },
  forced_root_block_attrs: {
    style: 'font-family: "仿宋_GB2312", "仿宋", "STFangsong", serif; font-size: 16px; line-height: 1.6;'
  }
}

// 加载可用印章列表
async function loadAvailableSeals() {
  try {
    // 动态导入API函数
    const { getAvailableSeals } = await import('@/api/oa/seal');
    const response = await getAvailableSeals();
    return response.data || [];
  } catch (error) {
    console.error('加载印章列表失败:', error);
    return [];
  }
}

// 获取文档ID的辅助函数
function getDocumentIdFromEditor(editor) {
  try {
    // 从URL参数获取
    const urlParams = new URLSearchParams(window.location.search);
    const docId = urlParams.get('docId');
    if (docId) return docId;

    // 从路由参数获取
    if (window.parent && window.parent.$route && window.parent.$route.params) {
      return window.parent.$route.params.docId;
    }

    // 从全局变量获取
    if (window.currentDocId) {
      return window.currentDocId;
    }

    return null;
  } catch (error) {
    console.error('获取文档ID失败:', error);
    return null;
  }
}

// 插入真实印章
async function insertRealSeal(editor, seal, position) {
  console.log('开始插入印章:', seal, position);

  try {
    // 动态导入API函数
    const { getSealImage } = await import('@/api/oa/seal');

    console.log('正在获取印章图片, sealId:', seal.sealId);

    // 获取印章图片
    const response = await getSealImage(seal.sealId);
    console.log('印章图片API响应:', response);

    // 修复数据获取逻辑 - 检查response.data.data或response.data
    let imageBase64;
    if (response.data && typeof response.data === 'object' && response.data.data) {
      imageBase64 = response.data.data;
    } else if (response.data && typeof response.data === 'string') {
      imageBase64 = response.data;
    } else {
      throw new Error('无法获取印章图片数据');
    }

    console.log('获取到的图片数据长度:', imageBase64 ? imageBase64.length : 0);
    console.log('图片数据前50个字符:', imageBase64 ? imageBase64.substring(0, 50) : 'null');

    const positionStyles = {
      'bottom-right': 'position: absolute; right: 50px; bottom: 100px; z-index: 1000;',
      'center': 'position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); z-index: 1000;',
      'bottom-left': 'position: absolute; left: 50px; bottom: 100px; z-index: 1000;'
    };

    // 验证图片数据格式
    if (!imageBase64 || !imageBase64.startsWith('data:image/')) {
      throw new Error('印章图片数据格式不正确');
    }

    const sealHtml = `
      <div class="seal-container" style="${positionStyles[position]} pointer-events: none;">
        <img src="${imageBase64}"
             alt="${seal.sealName}"
             class="seal-image"
             style="width: 120px; height: 120px; opacity: 0.8; border: none; pointer-events: none;"
             title="${seal.sealName}"
             data-seal-id="${seal.sealId}" />
      </div>
    `;

    console.log('准备插入的HTML:', sealHtml);
    editor.insertContent(sealHtml);
    console.log('印章插入完成');
  } catch (error) {
    console.error('插入印章失败:', error);
    editor.windowManager.alert('插入印章失败，请稍后重试');
  }
}

// 创建配置函数
export function createTinyMCEConfig(options = {}) {
  return {
    ...TINYMCE_CONFIG,
    ...options
  }
}

// 政府文档专用配置
export function createGovDocConfig(options = {}) {
  return createTinyMCEConfig({
    height: 600,
    ...options
  })
}

// 简单编辑器配置
export function createSimpleConfig(options = {}) {
  return createTinyMCEConfig({
    height: 300,
    plugins: [
      'advlist autolink lists link image charmap preview',
      'searchreplace visualblocks code fullscreen',
      'insertdatetime media table wordcount help'
    ],
    toolbar: [
      'undo redo | bold italic underline | alignleft aligncenter alignright',
      'bullist numlist | link image | code preview fullscreen'
    ],
    ...options
  })
}

export default TINYMCE_CONFIG
