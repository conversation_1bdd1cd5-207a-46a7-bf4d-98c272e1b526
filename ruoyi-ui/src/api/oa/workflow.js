import request from '@/utils/request'

// 查询工作流定义列表
export function listWorkflowDefinition(query) {
  return request({
    url: '/oa/workflow/definition/list',
    method: 'get',
    params: query
  })
}

// 查询工作流定义详细
export function getWorkflowDefinition(workflowId) {
  return request({
    url: '/oa/workflow/definition/' + workflowId,
    method: 'get'
  })
}

// 新增工作流定义
export function addWorkflowDefinition(data) {
  return request({
    url: '/oa/workflow/definition',
    method: 'post',
    data: data
  })
}

// 修改工作流定义
export function updateWorkflowDefinition(data) {
  return request({
    url: '/oa/workflow/definition',
    method: 'put',
    data: data
  })
}

// 删除工作流定义
export function delWorkflowDefinition(workflowId) {
  return request({
    url: '/oa/workflow/definition/' + workflowId,
    method: 'delete'
  })
}

// 启动工作流程
export function startProcess(data) {
  return request({
    url: '/oa/workflow/process/start',
    method: 'post',
    data: data
  })
}

// 完成任务
export function completeTask(taskId, data) {
  return request({
    url: '/oa/workflow/task/complete/' + taskId,
    method: 'post',
    data: data
  })
}

// 查询待办任务列表
export function getTodoTasks(query) {
  return request({
    url: '/oa/workflow/task/todo',
    method: 'get',
    params: query
  })
}

// 查询已办任务列表
export function getDoneTasks(query) {
  return request({
    url: '/oa/workflow/task/done',
    method: 'get',
    params: query
  })
}

// 获取任务详情
export function getTaskDetail(taskId) {
  return request({
    url: '/oa/workflow/task/' + taskId,
    method: 'get'
  })
}

// 查询流程实例列表
export function listWorkflowInstance(query) {
  return request({
    url: '/oa/workflow/instance/list',
    method: 'get',
    params: query
  })
}

// 获取流程实例列表（用于监控页面）
export function getInstanceListForMonitor(query) {
  return request({
    url: '/oa/workflow/instance/list',
    method: 'get',
    params: query
  })
}

// 查询流程实例详细
export function getWorkflowInstance(instanceId) {
  return request({
    url: '/oa/workflow/instance/' + instanceId,
    method: 'get'
  })
}

// 终止流程实例
export function terminateProcess(instanceId, data) {
  return request({
    url: '/oa/workflow/instance/terminate/' + instanceId,
    method: 'post',
    data: data
  })
}

// 转办任务
export function delegateTask(taskId, data) {
  return request({
    url: '/oa/workflow/task/delegate/' + taskId,
    method: 'post',
    data: data
  })
}

// 获取流程图
export function getProcessDiagram(processInstanceId) {
  return request({
    url: '/oa/workflow/diagram/' + processInstanceId,
    method: 'get'
  })
}

// 查询流程历史
export function getProcessHistory(processInstanceId) {
  return request({
    url: '/oa/workflow/history/' + processInstanceId,
    method: 'get'
  })
}

// 获取流程审批信息
export function getProcessApprovalInfo(processInstanceId) {
  return request({
    url: '/oa/workflow/approval/' + processInstanceId,
    method: 'get'
  })
}

// 获取可退回的节点列表
export function getReturnNodes(taskId) {
  return request({
    url: '/oa/workflow/task/returnNodes/' + taskId,
    method: 'get'
  })
}

// 获取下一步处理人列表
export function getNextAssignees(taskId) {
  return request({
    url: '/oa/workflow/task/nextAssignees/' + taskId,
    method: 'get'
  })
}

// ==================== 新的基于部门角色的工作流API ====================

// 启动收文审批流程
export function startReceiveApprovalProcess(data) {
  return request({
    url: '/oa/workflow/process/start/receive/approval',
    method: 'post',
    data: data
  })
}

// 启动收文特办流程
export function startReceiveSpecialProcess(data) {
  return request({
    url: '/oa/workflow/process/start/receive/special',
    method: 'post',
    data: data
  })
}

// 启动发文审批流程
export function startSendApprovalProcess(data) {
  return request({
    url: '/oa/workflow/process/start/send/approval',
    method: 'post',
    data: data
  })
}

// 根据候选组查询候选人
export function getCandidateUsers(candidateGroup) {
  return request({
    url: '/oa/workflow/process/candidates/' + candidateGroup,
    method: 'get'
  })
}

// 根据角色查询用户
export function getUsersByRole(roleKey) {
  return request({
    url: '/oa/workflow/process/users/role/' + roleKey,
    method: 'get'
  })
}

// 根据部门和角色查询用户
export function getUsersByDeptAndRole(deptId, roleKey) {
  return request({
    url: '/oa/workflow/process/users/dept/' + deptId + '/role/' + roleKey,
    method: 'get'
  })
}

// 查询部门负责人
export function getDeptLeader(deptId) {
  return request({
    url: '/oa/workflow/process/dept/' + deptId + '/leader',
    method: 'get'
  })
}

// 获取可用的流程定义
export function getAvailableProcessDefinitions() {
  return request({
    url: '/oa/workflow/process/definitions/available',
    method: 'get'
  })
}

// 手动部署流程定义
export function deployProcess(data) {
  return request({
    url: '/oa/workflow/process/deploy',
    method: 'post',
    data: data
  })
}

// ==================== 测试API ====================

// 测试收文审批流程
export function testReceiveApprovalProcess() {
  return request({
    url: '/oa/workflow/test/receive/approval',
    method: 'post'
  })
}

// 测试收文特办流程
export function testReceiveSpecialProcess() {
  return request({
    url: '/oa/workflow/test/receive/special',
    method: 'post'
  })
}

// 测试发文审批流程
export function testSendApprovalProcess() {
  return request({
    url: '/oa/workflow/test/send/approval',
    method: 'post'
  })
}

// 批量测试所有流程
export function testAllProcesses() {
  return request({
    url: '/oa/workflow/test/all',
    method: 'post'
  })
}

// 检查流程状态
export function checkProcessStatus(processKey) {
  return request({
    url: '/oa/workflow/test/check/' + processKey,
    method: 'get'
  })
}

// 部署流程定义（旧接口）
export function deployProcessLegacy(data) {
  return request({
    url: '/oa/workflow/deploy',
    method: 'post',
    data: data
  })
}

// ==================== 新增的人员选择和流程控制API ====================

// 获取可用的工作流列表
export function getAvailableWorkflows(category) {
  return request({
    url: '/oa/workflow/definition/available',
    method: 'get',
    params: { category }
  })
}

// 拒绝任务
export function rejectTask(taskId, data) {
  return request({
    url: `/oa/workflow/task/reject/${taskId}`,
    method: 'post',
    data: data
  })
}

// 回退任务
export function returnTask(taskId, data) {
  return request({
    url: `/oa/workflow/task/return/${taskId}`,
    method: 'post',
    data: data
  })
}

// 撤回流程
export function withdrawProcess(businessKey, data) {
  return request({
    url: `/oa/workflow/withdraw/${businessKey}`,
    method: 'post',
    data: data
  })
}

// 催办任务
export function urgeTask(taskId, data) {
  return request({
    url: `/oa/workflow/task/urge/${taskId}`,
    method: 'post',
    data: data
  })
}

// 获取可选择的分管领导列表
export function getAvailableLeaders() {
  return request({
    url: '/oa/workflow/personnel/leaders',
    method: 'get'
  })
}

// 获取指定分管领导下的科室负责人列表
export function getAvailableManagers(leaderId) {
  return request({
    url: `/oa/workflow/personnel/managers/${leaderId}`,
    method: 'get'
  })
}

// 获取指定科室负责人下的经办人列表
export function getAvailableHandlers(managerId) {
  return request({
    url: `/oa/workflow/personnel/handlers/${managerId}`,
    method: 'get'
  })
}

// 获取所有可分配的人员列表（用于特办流程）
export function getAllAvailablePersonnel() {
  return request({
    url: '/oa/workflow/personnel/all',
    method: 'get'
  })
}
