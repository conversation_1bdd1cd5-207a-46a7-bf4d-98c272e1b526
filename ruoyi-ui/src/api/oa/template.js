import request from '@/utils/request'

// 查询公文模板列表
export function listTemplate(query) {
  return request({
    url: '/oa/document/template/list',
    method: 'get',
    params: query
  })
}

// 查询公文模板详细
export function getTemplate(templateId) {
  return request({
    url: '/oa/document/template/' + templateId,
    method: 'get'
  })
}

// 新增公文模板
export function addTemplate(data) {
  return request({
    url: '/oa/document/template',
    method: 'post',
    data: data
  })
}

// 修改公文模板
export function updateTemplate(data) {
  return request({
    url: '/oa/document/template',
    method: 'put',
    data: data
  })
}

// 删除公文模板
export function delTemplate(templateId) {
  return request({
    url: '/oa/document/template/' + templateId,
    method: 'delete'
  })
}

// 获取可用的红头模板列表
export function getAvailableTemplates(templateType) {
  return request({
    url: '/oa/document/template/available',
    method: 'get',
    params: { templateType }
  })
}

// 获取默认红头模板
export function getDefaultTemplate(templateType, issuingOrgan) {
  return request({
    url: '/oa/document/template/default',
    method: 'get',
    params: { templateType, issuingOrgan }
  })
}

// 根据机关和文种获取适用模板
export function getTemplatesByOrganAndGenre(issuingOrgan, docGenre) {
  return request({
    url: '/oa/document/template/by-organ-genre',
    method: 'get',
    params: { issuingOrgan, docGenre }
  })
}

// 设置默认模板
export function setDefaultTemplate(templateId, templateType, issuingOrgan) {
  return request({
    url: '/oa/document/template/default/' + templateId,
    method: 'put',
    params: { templateType, issuingOrgan }
  })
}

// 生成公文HTML内容预览
export function generateDocumentHtml(data) {
  return request({
    url: '/oa/document/template/generate-html',
    method: 'post',
    data: data
  })
}
