import request from '@/utils/request'

// 查询来文单位列表
export function getSourceUnits(query) {
  return request({
    url: '/oa/sourceUnit/list',
    method: 'get',
    params: query
  })
}

// 查询来文单位详细
export function getSourceUnit(id) {
  return request({
    url: '/oa/sourceUnit/' + id,
    method: 'get'
  })
}

// 新增来文单位
export function addSourceUnit(data) {
  return request({
    url: '/oa/sourceUnit',
    method: 'post',
    data: data
  })
}

// 修改来文单位
export function updateSourceUnit(data) {
  return request({
    url: '/oa/sourceUnit',
    method: 'put',
    data: data
  })
}

// 删除来文单位
export function delSourceUnit(id) {
  return request({
    url: '/oa/sourceUnit/' + id,
    method: 'delete'
  })
}

// 批量删除来文单位
export function delSourceUnits(ids) {
  return request({
    url: '/oa/sourceUnit/' + ids,
    method: 'delete'
  })
}

// 导出来文单位
export function exportSourceUnit(query) {
  return request({
    url: '/oa/sourceUnit/export',
    method: 'get',
    params: query
  })
}

// 获取来文单位建议（用于自动完成）
export function getSourceUnitSuggestions(query) {
  return request({
    url: '/oa/sourceUnit/suggestions',
    method: 'get',
    params: { query }
  })
}