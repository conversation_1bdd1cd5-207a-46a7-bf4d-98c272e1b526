import request from '@/utils/request'

// 查询个人日程列表
export function listSchedule(query) {
  return request({
    url: '/oa/personal/schedule/list',
    method: 'get',
    params: query
  })
}

// 查询个人日程详细
export function getSchedule(scheduleId) {
  return request({
    url: '/oa/personal/schedule/' + scheduleId,
    method: 'get'
  })
}

// 新增个人日程
export function addSchedule(data) {
  return request({
    url: '/oa/personal/schedule',
    method: 'post',
    data: data
  })
}

// 修改个人日程
export function updateSchedule(data) {
  return request({
    url: '/oa/personal/schedule',
    method: 'put',
    data: data
  })
}

// 删除个人日程
export function delSchedule(scheduleId) {
  return request({
    url: '/oa/personal/schedule/' + scheduleId,
    method: 'delete'
  })
}

// 查询今日日程
export function getTodaySchedules() {
  return request({
    url: '/oa/personal/schedule/today',
    method: 'get'
  })
}

// 查询本周日程
export function getWeekSchedules() {
  return request({
    url: '/oa/personal/schedule/week',
    method: 'get'
  })
}

// 查询本月日程
export function getMonthSchedules() {
  return request({
    url: '/oa/personal/schedule/month',
    method: 'get'
  })
}
