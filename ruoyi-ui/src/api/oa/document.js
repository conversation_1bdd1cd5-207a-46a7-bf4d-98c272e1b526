import request from '@/utils/request'

// ==================== 收文管理API ====================

// 查询收文列表
export function listReceiveDocument(query) {
  return request({
    url: '/oa/document/receive/list',
    method: 'get',
    params: query
  })
}

// 查询收文详细
export function getReceiveDocument(docId) {
  return request({
    url: '/oa/document/receive/' + docId,
    method: 'get'
  })
}

// 新增收文
export function addReceiveDocument(data) {
  return request({
    url: '/oa/document/receive',
    method: 'post',
    data: data
  })
}

// 修改收文
export function updateReceiveDocument(data) {
  return request({
    url: '/oa/document/receive',
    method: 'put',
    data: data
  })
}

// 删除收文
export function delReceiveDocument(docId) {
  return request({
    url: '/oa/document/receive/' + docId,
    method: 'delete'
  })
}

// ==================== 发文管理API ====================

// 查询发文列表
export function listSendDocument(query) {
  return request({
    url: '/oa/document/send/list',
    method: 'get',
    params: query
  })
}

// 查询发文详细
export function getSendDocument(docId) {
  return request({
    url: '/oa/document/send/' + docId,
    method: 'get'
  })
}

// 新增发文
export function addSendDocument(data) {
  return request({
    url: '/oa/document/send',
    method: 'post',
    data: data
  })
}

// 修改发文
export function updateSendDocument(data) {
  return request({
    url: '/oa/document/send',
    method: 'put',
    data: data
  })
}

// 删除发文
export function delSendDocument(docId) {
  return request({
    url: '/oa/document/send/' + docId,
    method: 'delete'
  })
}

// ==================== 收文审批API ====================

// 提交收文审批
export function submitReceiveApproval(docId, data) {
  return request({
    url: '/oa/document/receive/submit/' + docId,
    method: 'post',
    data: data
  })
}

// 审核收文
export function approveReceiveDocument(docId) {
  return request({
    url: '/oa/document/receive/approve/' + docId,
    method: 'post'
  })
}

// 归档收文
export function archiveReceiveDocument(docId) {
  return request({
    url: '/oa/document/receive/archive/' + docId,
    method: 'post'
  })
}

// 获取可用的工作流列表
export function getAvailableWorkflows(category) {
  return request({
    url: '/oa/workflow/definition/available',
    method: 'get',
    params: { category }
  })
}

// ==================== 发文审批API ====================

// 提交发文审批
export function submitSendDocument(docId, data) {
  return request({
    url: '/oa/document/send/submit/' + docId,
    method: 'post',
    data: data
  })
}

// 发布发文
export function publishSendDocument(docId) {
  return request({
    url: '/oa/document/send/publish/' + docId,
    method: 'post'
  })
}



// ==================== PDF导出API ====================

// 导出收文PDF
export function exportReceiveDocumentPdf(docId) {
  return request({
    url: '/oa/document/receive/exportPdf/' + docId,
    method: 'post',
    responseType: 'blob'
  })
}

// 导出发文PDF
export function exportSendDocumentPdf(docId) {
  return request({
    url: '/oa/document/send/exportPdf/' + docId,
    method: 'post',
    responseType: 'blob'
  })
}

// 合并导出发文PDF（审批单+红头文件+底稿+附件）
export function exportMergedSendDocumentPdf(docId) {
  return request({
    url: '/oa/document/send/exportMergedPdf/' + docId,
    method: 'post',
    responseType: 'blob'
  })
}

// 批量导出PDF
export function batchExportDocumentPdf(data) {
  return request({
    url: '/oa/document/batchExportPdf',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// 导出公文PDF（向后兼容）
export function exportDocumentPdf(docId) {
  return exportReceiveDocumentPdf(docId)
}

// ==================== 发文编辑权限和套红印章API ====================

// 检查发文编辑权限
export function checkSendEditPermission(docId) {
  return request({
    url: '/oa/document/send/check-edit-permission/' + docId,
    method: 'get'
  })
}

// 应用红头模板
export function applySendTemplate(docId, data) {
  return request({
    url: '/oa/document/send/apply-template/' + docId,
    method: 'post',
    data: data
  })
}

// 添加印章
export function addSendSeal(docId, data) {
  return request({
    url: '/oa/document/send/add-seal/' + docId,
    method: 'post',
    data: data
  })
}
