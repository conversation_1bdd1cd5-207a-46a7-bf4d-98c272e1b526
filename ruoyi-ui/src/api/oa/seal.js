import request from '@/utils/request'

// ==================== 印章管理 ====================

// 查询印章列表
export function listSeal(query) {
  return request({
    url: '/oa/seal/list',
    method: 'get',
    params: query
  })
}

// 查询印章详细
export function getSeal(sealId) {
  return request({
    url: '/oa/seal/' + sealId,
    method: 'get'
  })
}

// 新增印章
export function addSeal(data) {
  return request({
    url: '/oa/seal',
    method: 'post',
    data: data
  })
}

// 修改印章
export function updateSeal(data) {
  return request({
    url: '/oa/seal',
    method: 'put',
    data: data
  })
}

// 删除印章
export function delSeal(sealId) {
  return request({
    url: '/oa/seal/' + sealId,
    method: 'delete'
  })
}

// 修改印章状态
export function updateSealStatus(sealId, status, reason) {
  const data = {
    sealId,
    status,
    reason
  }
  return request({
    url: '/oa/seal/updateStatus',
    method: 'post',
    data: data
  })
}

// ==================== 印章申请管理 ====================

// 查询印章申请列表
export function listSealApplication(query) {
  return request({
    url: '/oa/seal/application/list',
    method: 'get',
    params: query
  })
}

// 查询印章申请详细
export function getSealApplication(applicationId) {
  return request({
    url: '/oa/seal/application/' + applicationId,
    method: 'get'
  })
}

// 新增印章申请
export function addSealApplication(data) {
  return request({
    url: '/oa/seal/application',
    method: 'post',
    data: data
  })
}

// 修改印章申请
export function updateSealApplication(data) {
  return request({
    url: '/oa/seal/application',
    method: 'put',
    data: data
  })
}

// 删除印章申请
export function delSealApplication(applicationId) {
  return request({
    url: '/oa/seal/application/' + applicationId,
    method: 'delete'
  })
}

// 提交印章申请
export function submitSealApplication(applicationId) {
  return request({
    url: '/oa/seal/application/submit/' + applicationId,
    method: 'post'
  })
}

// 撤回印章申请
export function withdrawSealApplication(applicationId) {
  return request({
    url: '/oa/seal/application/withdraw/' + applicationId,
    method: 'post'
  })
}

// 审批印章申请
export function approveSealApplication(applicationId, approvalResult, approvalComment) {
  return request({
    url: '/oa/seal/application/approve/' + applicationId,
    method: 'post',
    data: {
      approvalResult,
      approvalComment
    }
  })
}

// ==================== 印章证书管理 ====================

// 查询印章证书列表
export function listSealCertificate(query) {
  return request({
    url: '/oa/seal/certificate/list',
    method: 'get',
    params: query
  })
}

// 查询印章证书详细
export function getSealCertificate(certificateId) {
  return request({
    url: '/oa/seal/certificate/' + certificateId,
    method: 'get'
  })
}

// 新增印章证书
export function addSealCertificate(data) {
  return request({
    url: '/oa/seal/certificate',
    method: 'post',
    data: data
  })
}

// 修改印章证书
export function updateSealCertificate(data) {
  return request({
    url: '/oa/seal/certificate',
    method: 'put',
    data: data
  })
}

// 删除印章证书
export function delSealCertificate(certificateId) {
  return request({
    url: '/oa/seal/certificate/' + certificateId,
    method: 'delete'
  })
}

// 证书续期
export function renewCertificate(data) {
  return request({
    url: '/oa/seal/certificate/renew',
    method: 'post',
    data: data
  })
}

// 撤销证书
export function revokeCertificate(certificateId) {
  return request({
    url: '/oa/seal/certificate/revoke/' + certificateId,
    method: 'post'
  })
}

// 激活证书
export function activateCertificate(certificateId) {
  return request({
    url: '/oa/seal/certificate/activate/' + certificateId,
    method: 'post'
  })
}

// ==================== 印章使用记录 ====================

// 查询印章使用记录列表
export function listSealUsage(query) {
  return request({
    url: '/oa/seal/usage/list',
    method: 'get',
    params: query
  })
}

// 查询印章使用记录详细
export function getSealUsage(usageId) {
  return request({
    url: '/oa/seal/usage/' + usageId,
    method: 'get'
  })
}

// 新增印章使用记录
export function addSealUsage(data) {
  return request({
    url: '/oa/seal/usage',
    method: 'post',
    data: data
  })
}

// 修改印章使用记录
export function updateSealUsage(data) {
  return request({
    url: '/oa/seal/usage',
    method: 'put',
    data: data
  })
}

// 删除印章使用记录
export function delSealUsage(usageId) {
  return request({
    url: '/oa/seal/usage/' + usageId,
    method: 'delete'
  })
}

// 印章归还
export function returnSeal(usageId, returnComment) {
  return request({
    url: '/oa/seal/usage/return/' + usageId,
    method: 'post',
    data: {
      returnComment
    }
  })
}

// ==================== 印章统计 ====================

// 查询印章使用统计
export function getSealStatistics(params) {
  return request({
    url: '/oa/seal/statistics',
    method: 'get',
    params: params
  })
}

// 查询印章申请统计
export function getSealApplicationStatistics(params) {
  return request({
    url: '/oa/seal/application/statistics',
    method: 'get',
    params: params
  })
}

// 查询印章使用频率统计
export function getSealUsageFrequency(params) {
  return request({
    url: '/oa/seal/usage/frequency',
    method: 'get',
    params: params
  })
}

// ==================== 印章审批流程 ====================

// 查询印章审批流程
export function getSealApprovalProcess(applicationId) {
  return request({
    url: '/oa/seal/approval/process/' + applicationId,
    method: 'get'
  })
}

// 查询待我审批的印章申请
export function getMyPendingApprovals(query) {
  return request({
    url: '/oa/seal/approval/pending',
    method: 'get',
    params: query
  })
}

// 查询我已审批的印章申请
export function getMyApprovedApplications(query) {
  return request({
    url: '/oa/seal/approval/approved',
    method: 'get',
    params: query
  })
}

// ==================== 印章编辑器相关 ====================

// 获取印章列表（用于编辑器）
export function getSealList(query) {
  return request({
    url: '/oa/seal/list',
    method: 'get',
    params: query
  })
}

// 获取印章详情（用于编辑器）
export function getSealDetail(sealId) {
  return request({
    url: '/oa/seal/' + sealId,
    method: 'get'
  })
}

// 上传印章图片（用于编辑器）
export function uploadSealImage(data) {
  return request({
    url: '/oa/seal/upload-image',
    method: 'post',
    data: data
  })
}

// ==================== 编辑器印章功能 ====================

// 获取可用印章列表（用于编辑器）
export function getAvailableSeals() {
  return request({
    url: '/oa/seal/available',
    method: 'get'
  })
}

// 获取印章图片（Base64格式）
export function getSealImage(sealId) {
  return request({
    url: '/oa/seal/image/' + sealId,
    method: 'get'
  })
}
