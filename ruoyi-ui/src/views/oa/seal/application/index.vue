<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="申请标题" prop="applicationTitle">
        <el-input
          v-model="queryParams.applicationTitle"
          placeholder="请输入申请标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="印章名称" prop="sealName">
        <el-input
          v-model="queryParams.sealName"
          placeholder="请输入印章名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="申请状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择申请状态" clearable>
          <el-option label="待审批" value="0" />
          <el-option label="已通过" value="1" />
          <el-option label="已拒绝" value="2" />
          <el-option label="已撤回" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:seal:application:add']"
        >新增申请</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:seal:application:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:seal:application:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:seal:application:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请标题" align="center" prop="applicationTitle" />
      <el-table-column label="印章名称" align="center" prop="sealName" />
      <el-table-column label="申请人" align="center" prop="applicantName" />
      <el-table-column label="申请时间" align="center" prop="applicationTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.applicationTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预计使用时间" align="center" prop="expectedUseTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.expectedUseTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '0'" type="warning">待审批</el-tag>
          <el-tag v-else-if="scope.row.status == '1'" type="success">已通过</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="danger">已拒绝</el-tag>
          <el-tag v-else-if="scope.row.status == '3'" type="info">已撤回</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:seal:application:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:seal:application:edit']"
            v-if="scope.row.status == '0'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-upload"
            @click="handleSubmit(scope.row)"
            v-hasPermi="['oa:seal:application:submit']"
            v-if="scope.row.status == '0'"
          >提交</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh-left"
            @click="handleWithdraw(scope.row)"
            v-hasPermi="['oa:seal:application:withdraw']"
            v-if="scope.row.status == '0'"
          >撤回</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['oa:seal:application:remove']"
            v-if="scope.row.status == '0' || scope.row.status == '3'"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改印章申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="申请标题" prop="applicationTitle">
              <el-input v-model="form.applicationTitle" placeholder="请输入申请标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请印章" prop="sealId">
              <el-select v-model="form.sealId" placeholder="请选择印章">
                <el-option
                  v-for="seal in sealList"
                  :key="seal.sealId"
                  :label="seal.sealName"
                  :value="seal.sealId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="预计使用时间" prop="expectedUseTime">
              <el-date-picker
                v-model="form.expectedUseTime"
                type="datetime"
                placeholder="选择预计使用时间"
                value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用期限" prop="useDuration">
              <el-input-number v-model="form.useDuration" :min="1" :max="30" placeholder="使用天数" />
              <span style="margin-left: 10px;">天</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="申请事由" prop="applicationReason">
          <el-input v-model="form.applicationReason" type="textarea" :rows="4" placeholder="请输入申请事由" />
        </el-form-item>
        <el-form-item label="使用说明" prop="useDescription">
          <el-input v-model="form.useDescription" type="textarea" :rows="3" placeholder="请输入使用说明" />
        </el-form-item>
        <el-form-item label="相关文件">
          <el-upload
            class="upload-demo"
            :action="upload.url"
            :headers="upload.headers"
            :file-list="fileList"
            :on-success="handleFileSuccess"
            :on-remove="handleFileRemove"
            :before-upload="beforeFileUpload"
            multiple>
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件，且不超过10MB</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看印章申请详情对话框 -->
    <el-dialog title="印章申请详情" :visible.sync="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="申请标题">{{ viewForm.applicationTitle }}</el-descriptions-item>
        <el-descriptions-item label="申请印章">{{ viewForm.sealName }}</el-descriptions-item>
        <el-descriptions-item label="申请人">{{ viewForm.applicantName }}</el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ parseTime(viewForm.applicationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="预计使用时间">{{ parseTime(viewForm.expectedUseTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="使用期限">{{ viewForm.useDuration }}天</el-descriptions-item>
        <el-descriptions-item label="申请状态">
          <el-tag v-if="viewForm.status == '0'" type="warning">待审批</el-tag>
          <el-tag v-else-if="viewForm.status == '1'" type="success">已通过</el-tag>
          <el-tag v-else-if="viewForm.status == '2'" type="danger">已拒绝</el-tag>
          <el-tag v-else-if="viewForm.status == '3'" type="info">已撤回</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="审批人" v-if="viewForm.approverName">{{ viewForm.approverName }}</el-descriptions-item>
        <el-descriptions-item label="申请事由" :span="2">{{ viewForm.applicationReason }}</el-descriptions-item>
        <el-descriptions-item label="使用说明" :span="2">{{ viewForm.useDescription }}</el-descriptions-item>
        <el-descriptions-item label="审批意见" :span="2" v-if="viewForm.approvalComment">{{ viewForm.approvalComment }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listSealApplication, getSealApplication, delSealApplication, addSealApplication, updateSealApplication, submitSealApplication, withdrawSealApplication } from "@/api/oa/seal";
import { listSeal } from "@/api/oa/seal";
import { getToken } from "@/utils/auth";

export default {
  name: "SealApplication",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 印章申请表格数据
      applicationList: [],
      // 印章列表
      sealList: [],
      // 文件列表
      fileList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/oa/seal/application/upload"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        applicationTitle: null,
        sealName: null,
        status: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        applicationTitle: [
          { required: true, message: "申请标题不能为空", trigger: "blur" }
        ],
        sealId: [
          { required: true, message: "申请印章不能为空", trigger: "change" }
        ],
        expectedUseTime: [
          { required: true, message: "预计使用时间不能为空", trigger: "blur" }
        ],
        useDuration: [
          { required: true, message: "使用期限不能为空", trigger: "blur" }
        ],
        applicationReason: [
          { required: true, message: "申请事由不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadSealList();
  },
  methods: {
    /** 查询印章申请列表 */
    getList() {
      this.loading = true;
      listSealApplication(this.queryParams).then(response => {
        this.applicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 加载印章列表 */
    loadSealList() {
      listSeal({ status: '1' }).then(response => {
        this.sealList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applicationId: null,
        applicationTitle: null,
        sealId: null,
        expectedUseTime: null,
        useDuration: 1,
        applicationReason: null,
        useDescription: null,
        attachmentUrls: null,
        status: "0",
        remark: null
      };
      this.fileList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applicationId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加印章申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const applicationId = row.applicationId || this.ids
      getSealApplication(applicationId).then(response => {
        this.form = response.data;
        // 处理附件列表
        if (this.form.attachmentUrls) {
          this.fileList = this.form.attachmentUrls.split(',').map((url, index) => ({
            name: `附件${index + 1}`,
            url: url
          }));
        }
        this.open = true;
        this.title = "修改印章申请";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getSealApplication(row.applicationId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理附件URL
          this.form.attachmentUrls = this.fileList.map(file => file.url).join(',');
          
          if (this.form.applicationId != null) {
            updateSealApplication(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSealApplication(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const applicationIds = row.applicationId || this.ids;
      this.$modal.confirm('是否确认删除印章申请编号为"' + applicationIds + '"的数据项？').then(function() {
        return delSealApplication(applicationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 提交申请 */
    handleSubmit(row) {
      this.$modal.confirm('是否确认提交该印章申请？提交后将无法修改。').then(function() {
        return submitSealApplication(row.applicationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("提交成功");
      }).catch(() => {});
    },
    /** 撤回申请 */
    handleWithdraw(row) {
      this.$modal.confirm('是否确认撤回该印章申请？').then(function() {
        return withdrawSealApplication(row.applicationId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("撤回成功");
      }).catch(() => {});
    },
    // 文件上传成功处理
    handleFileSuccess(res, file) {
      if (res.code === 200) {
        this.fileList.push({
          name: file.name,
          url: res.data.filePath
        });
        this.$modal.msgSuccess("文件上传成功");
      } else {
        this.$modal.msgError(res.msg);
      }
    },
    // 文件删除处理
    handleFileRemove(file, fileList) {
      this.fileList = fileList;
    },
    // 文件上传前处理
    beforeFileUpload(file) {
      const isValidType = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'application/pdf';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        this.$modal.msgError('上传文件只能是 JPG/PNG/PDF 格式!');
      }
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
      }
      return isValidType && isLt10M;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/seal/application/export', {
        ...this.queryParams
      }, `seal_application_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
