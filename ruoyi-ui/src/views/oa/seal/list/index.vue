<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="印章名称" prop="sealName">
        <el-input
          v-model="queryParams.sealName"
          placeholder="请输入印章名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="印章类型" prop="sealType">
        <el-select v-model="queryParams.sealType" placeholder="请选择印章类型" clearable>
          <el-option label="公章" value="official" />
          <el-option label="财务章" value="financial" />
          <el-option label="合同章" value="contract" />
          <el-option label="法人章" value="legal" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="正常" value="1" />
          <el-option label="停用" value="0" />
          <el-option label="损坏" value="2" />
          <el-option label="遗失" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:seal:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:seal:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:seal:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:seal:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sealList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="印章名称" align="center" prop="sealName" />
      <el-table-column label="印章类型" align="center" prop="sealType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_seal_type" :value="scope.row.sealType"/>
        </template>
      </el-table-column>
      <el-table-column label="印章编号" align="center" prop="sealCode" />
      <el-table-column label="保管人" align="center" prop="keeperName" />
      <el-table-column label="制作日期" align="center" prop="makeDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.makeDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '1'" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.status == '0'" type="danger">停用</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="warning">损坏</el-tag>
          <el-tag v-else-if="scope.row.status == '3'" type="danger">遗失</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:seal:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:seal:edit']"
          >修改</el-button>
          <!-- 移除申请记录按钮 -->
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" v-hasPermi="['oa:seal:manage']">
            <el-button size="mini" type="text">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="disable" icon="el-icon-close" v-if="scope.row.status == '1'">停用</el-dropdown-item>
              <el-dropdown-item command="enable" icon="el-icon-check" v-if="scope.row.status == '0'">启用</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改印章对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="印章名称" prop="sealName">
              <el-input v-model="form.sealName" placeholder="请输入印章名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="印章类型" prop="sealType">
              <el-select v-model="form.sealType" placeholder="请选择印章类型">
                <el-option label="公章" value="official" />
                <el-option label="财务章" value="financial" />
                <el-option label="合同章" value="contract" />
                <el-option label="法人章" value="legal" />
                <el-option label="其他" value="other" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="印章编号" prop="sealCode">
              <el-input v-model="form.sealCode" placeholder="请输入印章编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保管人" prop="keeperId">
              <el-select v-model="form.keeperId" placeholder="请选择保管人" @change="handleKeeperChange">
                <el-option
                  v-for="user in userList"
                  :key="user.userId"
                  :label="user.nickName"
                  :value="user.userId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="制作日期" prop="makeDate">
              <el-date-picker
                v-model="form.makeDate"
                type="date"
                placeholder="选择制作日期"
                value-format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="1">正常</el-radio>
                <el-radio label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="印章图片" prop="sealImage">
          <el-upload
            class="seal-uploader"
            :action="upload.url"
            :headers="upload.headers"
            :show-file-list="false"
            :on-success="handleSealImageSuccess"
            :before-upload="beforeSealImageUpload">
            <img v-if="form.sealImage" :src="form.sealImage" class="seal-image">
            <i v-else class="el-icon-plus seal-uploader-icon"></i>
          </el-upload>
          <div class="el-upload__tip" slot="tip">只能上传jpg/png文件，且不超过2MB，图片将直接存储到数据库</div>
        </el-form-item>
        <el-form-item label="印章描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入印章描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看印章详情对话框 -->
    <el-dialog title="印章详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="印章名称">{{ viewForm.sealName }}</el-descriptions-item>
        <el-descriptions-item label="印章类型">
          <dict-tag :options="dict.type.oa_seal_type" :value="viewForm.sealType"/>
        </el-descriptions-item>
        <el-descriptions-item label="印章编号">{{ viewForm.sealCode }}</el-descriptions-item>
        <el-descriptions-item label="保管人">{{ viewForm.keeperName }}</el-descriptions-item>
        <el-descriptions-item label="制作日期">{{ parseTime(viewForm.makeDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '1'" type="success">正常</el-tag>
          <el-tag v-else-if="viewForm.status == '0'" type="danger">停用</el-tag>
          <el-tag v-else-if="viewForm.status == '2'" type="warning">损坏</el-tag>
          <el-tag v-else-if="viewForm.status == '3'" type="danger">遗失</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="印章图片" :span="2">
          <img v-if="viewForm.sealImage" :src="viewForm.sealImage" class="seal-image-view">
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="印章描述" :span="2">{{ viewForm.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { listSeal, getSeal, delSeal, addSeal, updateSeal, updateSealStatus } from "@/api/oa/seal";
import { listUser } from "@/api/system/user";
import { getToken } from "@/utils/auth";

export default {
  name: "SealList",
  dicts: ['oa_seal_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 印章表格数据
      sealList: [],
      // 用户列表
      userList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 上传参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/oa/seal/upload"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        sealName: null,
        sealType: null,
        status: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        sealName: [
          { required: true, message: "印章名称不能为空", trigger: "blur" }
        ],
        sealType: [
          { required: true, message: "印章类型不能为空", trigger: "change" }
        ],
        sealCode: [
          { required: true, message: "印章编号不能为空", trigger: "blur" }
        ],
        keeperId: [
          { required: true, message: "保管人不能为空", trigger: "change" }
        ],
        makeDate: [
          { required: true, message: "制作日期不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.loadUserList();
  },
  methods: {
    /** 查询印章列表 */
    getList() {
      this.loading = true;
      listSeal(this.queryParams).then(response => {
        this.sealList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 加载用户列表 */
    loadUserList() {
      listUser().then(response => {
        this.userList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        sealId: null,
        sealName: null,
        sealType: null,
        sealCode: null,
        keeperId: null,
        makeDate: null,
        sealImage: null,
        description: null,
        status: "1",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.sealId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加印章";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const sealId = row.sealId || this.ids
      getSeal(sealId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改印章";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getSeal(row.sealId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.sealId != null) {
            updateSeal(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSeal(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const sealIds = row.sealId || this.ids;
      this.$modal.confirm('是否确认删除印章编号为"' + sealIds + '"的数据项？').then(function() {
        return delSeal(sealIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 下拉菜单操作 */
    handleCommand(command, row) {
      switch (command) {
        case 'disable':
          this.handleStatusChange(row, '0');
          break;
        case 'enable':
          this.handleStatusChange(row, '1');
          break;
        case 'damage':
          this.handleStatusChange(row, '2');
          break;
        case 'lost':
          this.handleStatusChange(row, '3');
          break;
      }
    },
    /** 状态修改 */
    handleStatusChange(row, status) {
      const statusText = {
        '0': '停用',
        '1': '启用',
        '2': '标记为损坏',
        '3': '标记为遗失'
      };
      this.$modal.confirm('确认要' + statusText[status] + '印章"' + row.sealName + '"吗？').then(function() {
        return updateSealStatus(row.sealId, status);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess(statusText[status] + "成功");
      }).catch(() => {});
    },
    // 印章图片上传成功处理
    handleSealImageSuccess(res, file) {
      if (res.code === 200) {
        // 根据后端返回结构访问base64Data
        this.form.sealImage = res.base64Data;
        this.$modal.msgSuccess("印章图片上传成功");
      } else {
        this.$modal.msgError(res.msg || "上传失败");
      }
    },
    // 印章图片上传前处理
    beforeSealImageUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$modal.msgError('上传印章图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$modal.msgError('上传印章图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/seal/export', {
        ...this.queryParams
      }, `seal_${new Date().getTime()}.xlsx`)
    },
    /** 保管人选择变化 */
    handleKeeperChange(value) {
      const keeper = this.userList.find(user => user.userId === value);
      if (keeper) {
        this.form.keeperName = keeper.nickName;
      } else {
        this.form.keeperName = null;
      }
    }
  }
};
</script>

<style scoped>
.seal-image {
  width: 100px;
  height: 100px;
  display: block;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.seal-image-view {
  width: 150px;
  height: 150px;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.seal-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.seal-uploader .el-upload:hover {
  border-color: #409EFF;
}

.seal-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
</style>
