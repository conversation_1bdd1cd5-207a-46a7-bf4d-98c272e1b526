<template>
  <div class="app-container">
    <el-card shadow="never">
      <div slot="header" class="clearfix">
        <span class="document-title">{{ document.docTitle }}</span>
        <div style="float: right;">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit"
            v-if="document.status === '1' && canEdit"
          >编辑</el-button>
          <el-button
            size="small"
            type="success"
            icon="el-icon-s-promotion"
            @click="handleSubmit"
            v-if="document.status === '1' && canSubmit"
          >提交审批</el-button>
          <el-button
            size="small"
            type="warning"
            icon="el-icon-download"
            @click="handleExportPdf"
            v-if="document.status !== '1'"
          >导出PDF</el-button>
          <el-button size="small" @click="handleBack">返回</el-button>
        </div>
      </div>

      <el-row :gutter="20">
        <!-- Left Column: Document Details -->
        <el-col :span="8">
          <el-card header="公文信息">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="文档编号">{{ document.docNumber }}</el-descriptions-item>

              <el-descriptions-item label="紧急程度">
                <dict-tag :options="dict.type.oa_urgency_level" :value="document.urgencyLevel"/>
              </el-descriptions-item>
              <el-descriptions-item label="密级">
                <dict-tag :options="dict.type.oa_security_level" :value="document.securityLevel"/>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <dict-tag :options="dict.type.oa_doc_status" :value="document.status"/>
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ parseTime(document.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>

              <!-- 收文特有字段 -->
              <el-descriptions-item label="来文单位" v-if="document.docType === '1'">{{ document.sourceUnit }}</el-descriptions-item>
              <el-descriptions-item label="收文日期" v-if="document.docType === '1'">{{ parseTime(document.receiveDate, '{y}-{m}-{d}') }}</el-descriptions-item>

              <!-- 发文特有字段 -->
              <el-descriptions-item label="收文单位" v-if="document.docType === '2'">{{ document.receiverUnit }}</el-descriptions-item>
              <el-descriptions-item label="收文联系人" v-if="document.docType === '2'">{{ document.receiverContact }}</el-descriptions-item>
              <el-descriptions-item label="发文日期" v-if="document.docType === '2'">{{ parseTime(document.sendDate, '{y}-{m}-{d}') }}</el-descriptions-item>
              <el-descriptions-item label="发文人" v-if="document.docType === '2'">{{ document.senderName }}</el-descriptions-item>
              <el-descriptions-item label="发文时间" v-if="document.docType === '2'">{{ parseTime(document.sendTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <el-card header="附件列表" v-if="document.attachments && document.attachments.length > 0" style="margin-top: 20px;">
            <el-table :data="document.attachments" size="small">
              <el-table-column label="文件名" prop="fileName" show-overflow-tooltip />
              <el-table-column label="文件大小" prop="fileSize" width="120">
                <template slot-scope="scope">
                  {{ formatFileSize(scope.row.fileSize) }}
                </template>
              </el-table-column>
              <el-table-column label="上传时间" prop="uploadTime" width="180">
                <template slot-scope="scope">
                  {{ parseTime(scope.row.uploadTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    icon="el-icon-download"
                    @click="handleDownload(scope.row)"
                  >下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>

          <el-card header="审批历史" v-if="workflowHistory && workflowHistory.length > 0" style="margin-top: 20px;">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in workflowHistory"
                :key="index"
                :timestamp="parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}')"
                placement="top"
              >
                <el-card>
                  <h4>{{ item.taskName }}</h4>
                  <p>处理人：{{ item.assigneeName }}</p>
                  <p v-if="item.comment">处理意见：{{ item.comment }}</p>
                  <p v-if="item.completeTime">完成时间：{{ parseTime(item.completeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </el-card>
        </el-col>

        <!-- Right Column: Document Content -->
        <el-col :span="16">
          <el-card header="文档内容">
            <tiny-editor
              ref="docEditor"
              v-model="document.docContent"
              :disabled="true"
              height="600px"
            />
          </el-card>
        </el-col>
      </el-row>

      <div class="document-remarks" v-if="document.remark">
        <h3>备注</h3>
        <div class="remark-text">{{ document.remark }}</div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDocument, getSendDocument, getReceiveDocument, submitDocument, exportDocumentPdf, downloadAttachment } from "@/api/oa/document";
import { getProcessHistory } from "@/api/oa/workflow";
import TinyEditor from "@/components/TinyEditor";

export default {
  name: "DocumentView",
  dicts: ['oa_doc_status', 'oa_urgency_level', 'oa_security_level'],
  components: {
    TinyEditor
  },
  data() {
    return {
      // 文档信息
      document: {},
      // 工作流历史
      workflowHistory: [],
      // 文档ID
      docId: null,
      // 权限控制
      canEdit: false,
      canSubmit: false
    };
  },
  created() {
    this.docId = this.$route.query.docId;
    if (this.docId) {
      this.getDocumentInfo();
      this.getWorkflowHistory();
    }
  },
  methods: {
    /** 获取文档信息 */
    getDocumentInfo() {
      // 根据路由路径判断是收文还是发文
      const isReceiveDocument = this.$route.path.includes('/receive/') || this.$route.query.type === 'receive';

      if (isReceiveDocument) {
        // 收文
        getReceiveDocument(this.docId).then(response => {
          this.document = response.data;
          this.checkPermissions();
        });
      } else {
        // 发文
        getSendDocument(this.docId).then(response => {
          this.document = response.data;
          this.checkPermissions();
        });
      }
    },



    /** 获取工作流历史 */
    getWorkflowHistory() {
      // 如果文档有流程实例ID，获取审批历史
      if (this.document.processInstanceId) {
        getProcessHistory(this.document.processInstanceId).then(response => {
          this.workflowHistory = response.data;
        }).catch(() => {
          // 如果获取失败，可能是还没有启动流程
          this.workflowHistory = [];
        });
      }
    },

    /** 检查权限 */
    checkPermissions() {
      // 简单权限检查，实际应该从后端获取
      const userInfo = this.$store.state.user.userInfo;
      // 当前用户是创建人、拥有编辑权限、或是当前审批人
      this.canEdit =
        this.document.createBy === userInfo.userName ||
        this.checkPermission(['oa:document:edit']) ||
        (this.document.currentAssignee && this.document.currentAssignee === userInfo.userName);
      this.canSubmit =
        this.document.createBy === userInfo.userName ||
        this.checkPermission(['oa:document:submit']);
    },

    /** 编辑文档 */
    handleEdit() {
      // 根据路由路径判断是收文还是发文
      const isReceiveDocument = this.$route.path.includes('/receive/') || this.$route.query.type === 'receive';

      if (isReceiveDocument) {
        // 收文编辑（如果有收文编辑页面的话）
        this.$router.push({
          path: '/oa/document/edit',
          query: { docId: this.docId, type: 'receive' }
        });
      } else {
        // 发文编辑
        this.$router.push({
          path: '/oa/document/edit',
          query: { docId: this.docId, type: 'send' }
        });
      }
    },

    /** 提交审批 */
    handleSubmit() {
      this.$modal.confirm('是否确认提交审批文档"' + this.document.docTitle + '"？').then(() => {
        return submitDocument(this.docId, {});
      }).then(() => {
        this.$modal.msgSuccess("提交成功");
        this.getDocumentInfo();
        this.getWorkflowHistory();
      }).catch(() => {});
    },

    /** 导出PDF */
    handleExportPdf() {
      // 根据路由路径判断是收文还是发文
      const isReceiveDocument = this.$route.path.includes('/receive/') || this.$route.query.type === 'receive';
      let exportFunction;

      if (isReceiveDocument) {
        exportFunction = exportReceiveDocumentPdf;
      } else {
        exportFunction = exportSendDocumentPdf;
      }

      exportFunction(this.docId).then(response => {
        this.downloadPdfFile(response, this.document.docTitle + '.pdf');
      }).catch(error => {
        this.$modal.msgError('导出PDF失败：' + (error.message || '未知错误'));
      });
    },

    /** 下载PDF文件 */
    downloadPdfFile(data, fileName) {
      try {
        const blob = new Blob([data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('下载文件失败:', error);
        this.$modal.msgError('下载文件失败，请联系管理员！');
      }
    },

    /** 下载附件 */
    handleDownload(file) {
      downloadAttachment(file.fileId).then(response => {
        this.download(response, file.fileName);
      });
    },

    /** 返回 */
    handleBack() {
      this.$router.go(-1);
    },

    /** 格式化文件大小 */
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /** 格式化文档内容显示 */
    formatDocumentContent(content) {
      if (!content) return '';

      // 确保内容以HTML格式正确显示
      // 如果内容不包含HTML标签，则添加基本的段落标签
      if (!content.includes('<') && !content.includes('>')) {
        // 纯文本内容，转换换行符为<br>标签
        return content.replace(/\n/g, '<br>');
      }

      // 已经是HTML格式的内容，直接返回
      return content;
    }
  }
};
</script>

<style lang="scss" scoped>
.document-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.document-content {
  padding: 20px 0;
}

.document-body {
  margin: 30px 0;

  h3 {
    margin-bottom: 15px;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }

  .content-text {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    line-height: 1.8;
    min-height: 200px;
    white-space: pre-wrap;
  }

  .document-content-display {
    font-family: "Microsoft YaHei", "SimSun", serif;
    font-size: 14px;
    line-height: 1.8;
    color: #333;

    // 保持HTML格式
    white-space: normal;

    ::v-deep .el-card__body {
      overflow: visible;
    }

    // 段落样式
    p {
      margin: 10px 0;
      text-indent: 2em; // 首行缩进
    }

    // 标题样式
    h1, h2, h3, h4, h5, h6 {
      margin: 15px 0 10px 0;
      font-weight: bold;
    }

    // 列表样式
    ul, ol {
      margin: 10px 0;
      padding-left: 2em;
    }

    // 表格样式
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 10px 0;

      th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
        font-weight: bold;
      }
    }
  }
}

.document-attachments {
  margin: 30px 0;

  h3 {
    margin-bottom: 15px;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }
}

.document-workflow {
  margin: 30px 0;

  h3 {
    margin-bottom: 15px;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }
}

.document-remarks {
  margin: 30px 0;

  h3 {
    margin-bottom: 15px;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
  }

  .remark-text {
    background: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    line-height: 1.6;
  }
}

.el-timeline-item__timestamp {
  color: #909399;
  line-height: 1;
  font-size: 13px;
}
</style>
