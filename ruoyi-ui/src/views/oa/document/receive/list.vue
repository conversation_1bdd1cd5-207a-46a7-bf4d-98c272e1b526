<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文档标题" prop="docTitle">
        <el-input
          v-model="queryParams.docTitle"
          placeholder="请输入文档标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文档编号" prop="docNumber">
        <el-input
          v-model="queryParams.docNumber"
          placeholder="请输入文档编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="来文单位" prop="sourceUnit">
        <el-input
          v-model="queryParams.sourceUnit"
          placeholder="请输入来文单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.oa_doc_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:document:receive:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:document:receive:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:document:receive:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:document:receive:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编号" align="center" prop="docNumber" />
      <el-table-column label="公文标题" align="center" prop="docTitle" show-overflow-tooltip />
      <el-table-column label="来文单位" align="center" prop="sourceUnit" show-overflow-tooltip />
      <el-table-column label="收文日期" align="center" prop="receiveDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.receiveDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_urgency_level" :value="scope.row.urgencyLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="密级" align="center" prop="securityLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_security_level" :value="scope.row.securityLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_doc_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['oa:document:receive:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:document:receive:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-upload"
            @click="handleSubmit(scope.row)"
            v-hasPermi="['oa:document:receive:submit']"
            v-if="scope.row.status === '0' || scope.row.status === 0"
          >提交审批</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleApprove(scope.row)"
            v-hasPermi="['oa:document:receive:approve']"
            v-if="scope.row.status == '1' || scope.row.status == 1"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-folder"
            @click="handleArchive(scope.row)"
            v-hasPermi="['oa:document:receive:archive']"
            v-if="scope.row.status == '2' || scope.row.status == 2"
          >归档</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['oa:document:receive:remove']"
          >删除</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 特批流程人员选择弹窗 -->
    <el-dialog
      title="选择处理人员"
      :visible.sync="showSpecialAssigneeDialog"
      width="500px"
      append-to-body
    >
      <div class="special-assignee-content">
        <p style="margin-bottom: 20px; color: #666;">
          <i class="el-icon-info"></i>
          请选择处理此收文的人员，选择后将直接流转给该人员处理。
        </p>

        <!-- 人员选择下拉框 -->
        <div style="margin-bottom: 20px;">
          <label style="display: block; margin-bottom: 8px; font-weight: bold;">选择处理人员：</label>
          <el-select
            v-model="selectedAssignee"
            placeholder="请选择处理人员"
            style="width: 100%;"
            filterable
            clearable
            @change="handleAssigneeChange"
            @visible-change="handleSelectVisibleChange"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.value"
              :label="user.label"
              :value="user.value"
            >
              <span style="float: left">{{ user.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ user.deptName }}</span>
            </el-option>
          </el-select>
        </div>

        <!-- 显示当前选择的值 -->
        <div v-if="selectedAssignee" style="margin-bottom: 10px; color: #409EFF;">
          已选择：{{ getAssigneeName(selectedAssignee) }}
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSpecialSubmit">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmSpecialSubmit"
          :disabled="!selectedAssignee"
        >
          确认提交
        </el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listReceiveDocument, getReceiveDocument, delReceiveDocument, exportReceiveDocumentPdf, submitReceiveApproval, approveReceiveDocument, archiveReceiveDocument } from "@/api/oa/document";
import { listAllActiveUsers } from "@/api/system/user";

export default {
  name: "ReceiveDocumentList",
  dicts: ['oa_urgency_level', 'oa_security_level', 'oa_doc_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收文表格数据
      documentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docTitle: null,
        docNumber: null,
        sourceUnit: null,
        status: null
      },

      // 特批流程相关
      showSpecialAssigneeDialog: false,
      currentSubmitRow: null,
      selectedAssignee: null,
      availableUsers: []

    };
  },
  created() {
    this.getList();
  },
  watch: {
    // 监听路由参数变化，当从编辑页面返回时刷新列表
    '$route'(to, from) {
      if (to.path === '/oa/document/receive/list' && to.query._t) {
        this.getList();
      }
    }
  },
  methods: {
    /** 查询收文列表 */
    getList() {
      this.loading = true;
      listReceiveDocument(this.queryParams).then(response => {
        this.documentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/oa/document/receive/add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const docId = row.docId || this.ids
      this.$router.push('/oa/document/receive/edit/' + docId);
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push('/oa/document/receive/detail/' + row.docId);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docIds = row.docId || this.ids;
      this.$modal.confirm('是否确认删除收文编号为"' + docIds + '"的数据项？').then(() => {
        return delReceiveDocument(docIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/document/receive/export', {
        ...this.queryParams
      }, `receive_document_${new Date().getTime()}.xlsx`)
    },

    /** 提交审批操作 */
    handleSubmit(row) {
      // 检查当前用户是否是书记，如果是书记则显示人员选择弹窗
      if (this.isSecretary()) {
        this.currentSubmitRow = row;
        this.showSpecialAssigneeDialog = true;
      } else {
        // 普通用户直接提交审批
        this.$modal.confirm('确认提交审批吗？提交后将无法修改。').then(() => {
          submitReceiveApproval(row.docId, {}).then(response => {
            this.$modal.msgSuccess("提交审批成功");
            this.getList();
          }).catch(error => {
            console.error('提交审批失败:', error);
            this.$modal.msgError("提交审批失败：" + (error.message || "请检查网络连接"));
          });
        });
      }
    },
    /** 审核操作 */
    handleApprove(row) {
      this.$modal.confirm('是否确认审核通过收文"' + row.docTitle + '"？').then(() => {
        return approveReceiveDocument(row.docId);
      }).then(() => {
        this.$modal.msgSuccess("审核成功");
        this.getList();
      }).catch(() => {});
    },
    /** 归档操作 */
    handleArchive(row) {
      this.$modal.confirm('是否确认归档收文"' + row.docTitle + '"？').then(() => {
        return archiveReceiveDocument(row.docId);
      }).then(() => {
        this.$modal.msgSuccess("归档成功");
        this.getList();
      }).catch(() => {});
    },

    // 检查当前用户是否是书记
    isSecretary() {
      const userInfo = this.$store.state.user;
      if (!userInfo) {
        return false;
      }

      // 检查用户名或角色
      const isSecretaryByName = userInfo.name === 'secretary';
      const isSecretaryByRole = userInfo.roles && userInfo.roles.includes('sj');

      return isSecretaryByName || isSecretaryByRole;
    },

    // 取消特批提交
    cancelSpecialSubmit() {
      this.showSpecialAssigneeDialog = false;
      this.selectedAssignee = null;
      this.currentSubmitRow = null;
    },

    // 处理人员选择变化
    handleAssigneeChange(value) {
      console.log('选择的处理人员:', value);
    },

    // 处理下拉框显示/隐藏
    handleSelectVisibleChange(visible) {
      if (visible && this.availableUsers.length === 0) {
        this.loadAvailableUsers();
      }
    },

    // 加载可用用户列表
    async loadAvailableUsers() {
      try {
        // 调用系统所有启用用户接口（专门用于转办和审批）
        const response = await listAllActiveUsers();

        if (response.code === 200) {
          this.availableUsers = response.data.map(user => ({
            value: user.userName,
            label: `${user.nickName}（${user.userName}）`,
            deptName: user.dept ? user.dept.deptName : '未分配部门',
            nickName: user.nickName,
            userName: user.userName
          }));
          console.log('成功加载用户列表，共', this.availableUsers.length, '个用户');
        }
      } catch (error) {
        console.error('获取用户列表失败:', error);
        // 如果接口调用失败，使用默认的用户列表
        this.availableUsers = [
          { value: 'leader_admin', label: '张副主任（行政）', deptName: '领导班子' },
          { value: 'leader_finance', label: '李副主任（财务）', deptName: '领导班子' },
          { value: 'leader_operation', label: '赵副主任（运管）', deptName: '领导班子' },
          { value: 'manager_hr', label: '人事科长', deptName: '人事科' },
          { value: 'manager_finance', label: '财务科长', deptName: '财务科' },
          { value: 'manager_operation', label: '运管科长', deptName: '运管科' },
          { value: 'manager_hydro', label: '水调科长', deptName: '水调科' },
          { value: 'clerk_hr1', label: '人事科小赵', deptName: '人事科' },
          { value: 'clerk_operation1', label: '运管科小钱', deptName: '运管科' },
          { value: 'clerk_hydro1', label: '水调科小孙', deptName: '水调科' },
          { value: 'clerk_finance1', label: '财务科小周', deptName: '财务科' }
        ];
        console.log('使用默认用户列表，共', this.availableUsers.length, '个用户');
      }
    },

    // 获取人员名称
    getAssigneeName(value) {
      const user = this.availableUsers.find(u => u.value === value);
      return user ? user.label : value;
    },

    // 确认特批提交
    confirmSpecialSubmit() {
      if (!this.selectedAssignee) {
        this.$modal.msgWarning("请选择处理人员");
        return;
      }

      this.$modal.confirm('确认提交特批流程吗？将直接流转给选择的人员处理。').then(() => {
        const submitData = {
          selectedAssignee: this.selectedAssignee
        };

        submitReceiveApproval(this.currentSubmitRow.docId, submitData).then(response => {
          this.$modal.msgSuccess("特批流程提交成功");
          this.showSpecialAssigneeDialog = false;
          this.selectedAssignee = null;
          this.currentSubmitRow = null;
          this.getList();
        }).catch(error => {
          console.error('特批流程提交失败:', error);
          this.$modal.msgError("特批流程提交失败：" + (error.message || "请检查网络连接"));
        });
      });
    }

  }
};
</script>


