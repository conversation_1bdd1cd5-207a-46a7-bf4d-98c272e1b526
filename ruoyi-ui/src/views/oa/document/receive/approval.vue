<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文档标题" prop="docTitle">
        <el-input
          v-model="queryParams.docTitle"
          placeholder="请输入文档标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="审批中" value="审批中"></el-option>
          <el-option label="特批申请中" value="特批申请中"></el-option>
          <el-option label="已审批" value="已审批"></el-option>
          <el-option label="退回" value="退回"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:document:receive:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编号" align="center" prop="docNumber" />
      <el-table-column label="文档标题" align="center" prop="docTitle" show-overflow-tooltip />
      <el-table-column label="来文单位" align="center" prop="sourceUnit" show-overflow-tooltip />
      <el-table-column label="当前步骤" align="center" prop="currentStep" />
      <el-table-column label="当前处理人" align="center" prop="currentAssignee" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === '审批中'" type="primary">{{ scope.row.status }}</el-tag>
          <el-tag v-else-if="scope.row.status === '特批申请中'" type="warning">{{ scope.row.status }}</el-tag>
          <el-tag v-else-if="scope.row.status === '已审批'" type="success">{{ scope.row.status }}</el-tag>
          <el-tag v-else-if="scope.row.status === '退回'" type="danger">{{ scope.row.status }}</el-tag>
          <el-tag v-else type="info">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:document:receive:query']"
          >查看详情</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看详情对话框 -->
    <el-dialog title="收文详情" :visible.sync="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="文档编号">{{ documentDetail.docNumber }}</el-descriptions-item>
        <el-descriptions-item label="文档标题">{{ documentDetail.docTitle }}</el-descriptions-item>
        <el-descriptions-item label="来文单位">{{ documentDetail.sourceUnit }}</el-descriptions-item>
        <el-descriptions-item label="收文日期">{{ parseTime(documentDetail.receiveDate, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="紧急程度">
          <dict-tag :options="dict.type.oa_urgency_level" :value="documentDetail.urgencyLevel"/>
        </el-descriptions-item>
        <el-descriptions-item label="密级">
          <dict-tag :options="dict.type.oa_security_level" :value="documentDetail.securityLevel"/>
        </el-descriptions-item>
        <el-descriptions-item label="当前步骤">{{ documentDetail.currentStep }}</el-descriptions-item>
        <el-descriptions-item label="当前处理人">{{ documentDetail.currentAssignee }}</el-descriptions-item>
        <el-descriptions-item label="状态" :span="2">
          <el-tag v-if="documentDetail.status === '审批中'" type="primary">{{ documentDetail.status }}</el-tag>
          <el-tag v-else-if="documentDetail.status === '特批申请中'" type="warning">{{ documentDetail.status }}</el-tag>
          <el-tag v-else-if="documentDetail.status === '已审批'" type="success">{{ documentDetail.status }}</el-tag>
          <el-tag v-else-if="documentDetail.status === '退回'" type="danger">{{ documentDetail.status }}</el-tag>
          <el-tag v-else type="info">{{ documentDetail.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="文档内容" :span="2">
          <div v-html="documentDetail.docContent" style="max-height: 200px; overflow-y: auto;"></div>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getApprovingReceiveDocuments, getReceiveDocument } from "@/api/oa/document";

export default {
  name: "ReceiveApproval",
  dicts: ['oa_urgency_level', 'oa_security_level'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收文表格数据
      documentList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docTitle: null,
        status: null
      },
      // 详情对话框
      detailOpen: false,
      documentDetail: {}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询收文审批列表 */
    getList() {
      this.loading = true;
      getApprovingReceiveDocuments(this.queryParams).then(response => {
        this.documentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 查看详情 */
    handleView(row) {
      getReceiveDocument(row.docId).then(response => {
        this.documentDetail = response.data;
        this.detailOpen = true;
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/document/receive/approving/export', {
        ...this.queryParams
      }, `收文审批_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
