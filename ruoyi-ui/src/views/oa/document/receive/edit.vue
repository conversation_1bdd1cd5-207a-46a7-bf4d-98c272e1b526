<template>
  <div class="app-container">
    <el-card class="document-form-card">
      <div slot="header" class="clearfix">
        <span class="form-title">{{ isEdit ? '编辑收文' : '新增收文' }}</span>
        <div style="float: right;">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
        </div>
      </div>

      <el-row :gutter="20">
        <!-- Left Column: Document Information -->
        <el-col :span="8">
          <el-card header="文档信息" class="document-info-card">
            <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
              <el-form-item label="文档编号" prop="docNumber">
                <el-input v-model="form.docNumber" placeholder="请输入文档编号" />
              </el-form-item>

              <el-form-item label="公文标题" prop="docTitle">
                <el-input v-model="form.docTitle" placeholder="请输入公文标题" />
              </el-form-item>

              <el-form-item label="来文单位" prop="sourceUnit">
                <el-input v-model="form.sourceUnit" placeholder="请输入来文单位" />
              </el-form-item>

              <el-form-item label="发文联系人" prop="senderContact">
                <el-input v-model="form.senderContact" placeholder="请输入发文联系人" />
              </el-form-item>

              <el-form-item label="收文日期" prop="receiveDate">
                <el-date-picker
                  v-model="form.receiveDate"
                  type="date"
                  placeholder="选择收文日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="紧急程度" prop="urgencyLevel">
                <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.oa_urgency_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="密级" prop="securityLevel">
                <el-select v-model="form.securityLevel" placeholder="请选择密级" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.oa_security_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="完成期限" prop="completionDeadline">
                <el-date-picker
                  v-model="form.completionDeadline"
                  type="datetime"
                  placeholder="选择完成期限"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="处理意见" prop="handleOpinion">
                <el-input
                  v-model="form.handleOpinion"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入处理意见"
                />
              </el-form-item>

              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="2"
                  placeholder="请输入备注"
                />
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 附件信息 -->
          <el-card header="附件信息" style="margin-top: 20px;" class="attachment-card">
            <div class="attachment-list">
              <div v-if="attachmentList.length === 0" class="no-attachments">
                <el-empty description="暂无附件" :image-size="60"></el-empty>
              </div>
              <div v-else>
                <el-table :data="attachmentList" border size="small">
                  <el-table-column label="附件名称" prop="fileName" />
                  <el-table-column label="文件大小" width="100">
                    <template slot-scope="scope">
                      <span>{{ formatFileSize(scope.row.fileSize) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="150">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        @click="previewAttachment(scope.row)"
                        v-if="scope.row.fileName && scope.row.fileName.toLowerCase().endsWith('.pdf')"
                      >预览</el-button>
                      <el-button
                        size="mini"
                        type="text"
                        @click="downloadAttachment(scope.row)"
                      >下载</el-button>
                      <el-button
                        size="mini"
                        type="text"
                        style="color: #f56c6c"
                        @click="removeAttachment(scope.row, scope.$index)"
                      >删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>


          </el-card>
        </el-col>

        <!-- Right Column: PDF Preview -->
        <el-col :span="16">
          <el-card class="pdf-card">
            <div class="pdf-preview-container">
              <div v-if="pdfUrl" class="pdf-viewer">
                <div class="pdf-toolbar">
                  <span class="pdf-title">{{ currentPdfName }}</span>
                  <div class="pdf-actions">
                    <el-upload
                      :action="uploadUrl"
                      :headers="headers"
                      :on-success="handlePdfUploadSuccess"
                      :before-upload="beforePdfUpload"
                      :show-file-list="false"
                      accept=".pdf"
                      style="display: inline-block; margin-right: 10px;"
                    >
                      <el-button size="mini">添加PDF</el-button>
                    </el-upload>
                    <el-button size="mini" @click="downloadCurrentPdf">下载</el-button>
                  </div>
                </div>
                <iframe
                  :src="pdfUrl"
                  width="100%"
                  height="860px"
                  frameborder="0"
                  class="pdf-iframe">
                </iframe>
              </div>
              <div v-else class="no-pdf-content">
                <el-empty description="暂无PDF文件" :image-size="100">
                  <template slot="description">
                    <p>收文管理主要处理PDF格式的公文</p>
                    <p>请上传PDF文件以便预览和管理</p>
                  </template>
                  <el-upload
                    :action="uploadUrl"
                    :headers="headers"
                    :on-success="handlePdfUploadSuccess"
                    :before-upload="beforePdfUpload"
                    :show-file-list="false"
                    accept=".pdf"
                  >
                    <el-button type="primary">上传PDF文件</el-button>
                  </el-upload>
                </el-empty>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>



    <!-- 工作流选择对话框 -->
    <el-dialog title="选择审批流程" :visible.sync="showWorkflowDialog" width="500px" append-to-body>
      <el-form :model="workflowForm" label-width="100px">
        <el-form-item label="审批流程" required>
          <el-select v-model="selectedWorkflow" placeholder="请选择审批流程" style="width: 100%">
            <el-option
              v-for="workflow in workflowList"
              :key="workflow.key"
              :label="workflow.name"
              :value="workflow.key"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showWorkflowDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmSubmitApproval">确认提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getReceiveDocument, addReceiveDocument, updateReceiveDocument, submitReceiveApproval, getAvailableWorkflows } from "@/api/oa/document";
import { getToken } from "@/utils/auth";

export default {
  name: "ReceiveDocumentEdit",
  dicts: ['oa_urgency_level', 'oa_security_level', 'oa_secret_level'],
  data() {
    return {
      // 表单数据
      form: {
        docId: null,
        docNumber: '',
        docTitle: '',
        sourceUnit: '',
        senderContact: '',
        receiveDate: '',
        urgencyLevel: '2', // 默认普通
        securityLevel: '1', // 默认公开
        secretLevel: '',
        completionDeadline: '',
        handleOpinion: '',
        remark: '',
        attachments: '',
        status: '0'
      },
      // 是否为编辑模式
      isEdit: false,
      // 表单验证规则
      rules: {
        docNumber: [
          { required: true, message: "文档编号不能为空", trigger: "blur" }
        ],
        docTitle: [
          { required: true, message: "公文标题不能为空", trigger: "blur" }
        ],
        sourceUnit: [
          { required: true, message: "来文单位不能为空", trigger: "blur" }
        ],
        receiveDate: [
          { required: true, message: "收文日期不能为空", trigger: "change" }
        ]
      },
      // 附件相关
      attachmentList: [],
      fileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      // PDF相关
      pdfUrl: '',
      currentPdfName: '',
      // 工作流相关
      showWorkflowDialog: false,
      workflowList: [],
      selectedWorkflow: '',
      workflowForm: {},
      // 来源信息
      fromApproval: false,
      approvalInfo: null
    };
  },
  created() {
    const docId = this.$route.params.docId;
    if (docId) {
      this.isEdit = true;
      this.form.docId = docId;
      this.getDocumentInfo();
    } else {
      this.isEdit = false;
      // 新增时设置默认值
      this.form.receiveDate = new Date().toISOString().split('T')[0];
    }

    // 检查是否来自审批页面
    this.fromApproval = this.$route.query.fromApproval === 'true';
    if (this.fromApproval) {
      // 保存审批页面信息，用于返回
      this.approvalInfo = {
        taskId: this.$route.query.taskId,
        taskName: this.$route.query.taskName,
        processInstanceId: this.$route.query.processInstanceId,
        businessKey: this.$route.query.businessKey
      };
    }
  },
  methods: {
    /** 获取文档信息 */
    getDocumentInfo() {
      getReceiveDocument(this.form.docId).then(response => {
        this.form = { ...this.form, ...response.data };

        // 解析附件信息
        if (response.data.attachments) {
          try {
            let attachments;
            if (typeof response.data.attachments === 'string') {
              attachments = JSON.parse(response.data.attachments);
            } else {
              attachments = response.data.attachments;
            }

            if (Array.isArray(attachments)) {
              this.attachmentList = attachments.map(file => ({
                ...file,
                fileName: file.fileName || file.name,
                url: file.url,
                fileSize: file.fileSize || file.size
              }));

              // 自动展示最新的PDF文件
              this.loadLatestPdf();
            }
          } catch (e) {
            console.error('解析附件信息失败:', e);
            this.attachmentList = [];
          }
        }
      });
    },

    /** 加载最新的PDF文件 */
    loadLatestPdf() {
      const pdfFiles = this.attachmentList.filter(attachment =>
        attachment.fileName && attachment.fileName.toLowerCase().endsWith('.pdf')
      );

      if (pdfFiles.length > 0) {
        const latestPdf = pdfFiles[pdfFiles.length - 1];
        this.pdfUrl = this.getFullUrl(latestPdf.url);
        this.currentPdfName = latestPdf.fileName;
      } else {
        this.pdfUrl = '';
        this.currentPdfName = '';
      }
    },

    /** 获取完整URL */
    getFullUrl(url) {
      if (!url) return '';
      if (url.startsWith('http')) return url;
      return process.env.VUE_APP_BASE_API + url;
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '0 Bytes';
      const i = Math.floor(Math.log(size) / Math.log(1024));
      return (size / Math.pow(1024, i)).toFixed(2) + ' ' + ['B', 'KB', 'MB', 'GB', 'TB'][i];
    },

    /** 预览附件 */
    previewAttachment(attachment) {
      if (attachment.fileName && attachment.fileName.toLowerCase().endsWith('.pdf')) {
        this.pdfUrl = this.getFullUrl(attachment.url);
        this.currentPdfName = attachment.fileName;
      }
    },

    /** 下载附件 */
    downloadAttachment(attachment) {
      const link = document.createElement('a');
      link.href = this.getFullUrl(attachment.url);
      link.download = attachment.fileName;
      link.click();
    },

    /** 下载当前PDF */
    downloadCurrentPdf() {
      if (this.pdfUrl && this.currentPdfName) {
        const link = document.createElement('a');
        link.href = this.pdfUrl;
        link.download = this.currentPdfName;
        link.click();
      }
    },

    /** 删除附件 */
    removeAttachment(attachment, index) {
      this.$confirm('确认删除该附件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 从附件列表中移除
        this.attachmentList.splice(index, 1);
        this.form.attachments = JSON.stringify(this.attachmentList);

        // 如果删除的是当前预览的PDF，重新加载
        if (attachment.fileName === this.currentPdfName) {
          this.loadLatestPdf();
        }

        this.$modal.msgSuccess('附件删除成功');
      }).catch(() => {});
    },

    /** PDF上传前验证 */
    beforePdfUpload(file) {
      const isPDF = file.type === 'application/pdf';
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isPDF) {
        this.$modal.msgError('只能上传PDF文件!');
        return false;
      }
      if (!isLt10M) {
        this.$modal.msgError('上传文件大小不能超过 10MB!');
        return false;
      }
      return true;
    },

    /** PDF上传成功 */
    handlePdfUploadSuccess(response) {
      if (response.code === 200) {
        const newAttachment = {
          fileName: response.fileName,
          url: response.url,
          fileSize: response.size || 0
        };

        this.attachmentList.push(newAttachment);
        this.form.attachments = JSON.stringify(this.attachmentList);

        // 自动预览新上传的PDF
        this.pdfUrl = this.getFullUrl(newAttachment.url);
        this.currentPdfName = newAttachment.fileName;

        this.$modal.msgSuccess('PDF文件上传成功');
      } else {
        this.$modal.msgError('文件上传失败：' + response.msg);
      }
    },



    /** 附件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        const newAttachment = {
          fileName: response.fileName,
          url: response.url,
          fileSize: response.size || 0
        };

        this.attachmentList.push(newAttachment);
        this.form.attachments = JSON.stringify(this.attachmentList);

        // 如果是PDF文件，自动预览
        if (response.fileName && response.fileName.toLowerCase().endsWith('.pdf')) {
          this.loadLatestPdf();
        }

        this.$modal.msgSuccess('文件上传成功');
      } else {
        this.$modal.msgError('文件上传失败：' + response.msg);
      }
    },

    /** 移除附件 */
    handleRemove(file, fileList) {
      // 从附件列表中移除
      this.attachmentList = this.attachmentList.filter(item => item.fileName !== file.name);
      this.form.attachments = JSON.stringify(this.attachmentList);

      // 如果移除的是当前预览的PDF，重新加载
      if (file.name === this.currentPdfName) {
        this.loadLatestPdf();
      }
    },

    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 编辑模式
            updateReceiveDocument(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.handleSaveSuccess();
            }).catch(error => {
              console.error('修改收文失败:', error);
              this.$modal.msgError("修改失败：" + (error.message || "请检查网络连接"));
            });
          } else {
            // 新增模式
            addReceiveDocument(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.handleSaveSuccess();
            }).catch(error => {
              console.error('新增收文失败:', error);
              this.$modal.msgError("新增失败：" + (error.message || "请检查网络连接"));
            });
          }
        }
      });
    },

    /** 提交审批 */
    submitApproval() {
      // 先保存表单
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.isEdit) {
            // 编辑模式，直接更新
            updateReceiveDocument(this.form).then(response => {
              // 获取可用工作流
              this.getAvailableWorkflows();
            });
          } else {
            // 新增模式，先保存再提交审批
            addReceiveDocument(this.form).then(response => {
              if (response.data && response.data.docId) {
                this.form.docId = response.data.docId;
                this.isEdit = true;
                // 获取可用工作流
                this.getAvailableWorkflows();
              }
            }).catch(error => {
              console.error('保存收文失败:', error);
              this.$modal.msgError("保存失败：" + (error.message || "请检查网络连接"));
            });
          }
        }
      });
    },

    /** 获取可用工作流 */
    getAvailableWorkflows() {
      getAvailableWorkflows().then(response => {
        this.workflowList = response.data || [];
        this.showWorkflowDialog = true;
      });
    },

    /** 确认提交审批 */
    confirmSubmitApproval() {
      if (!this.selectedWorkflow) {
        this.$modal.msgError('请选择审批流程');
        return;
      }

      submitReceiveApproval(this.form.docId, {
        workflowKey: this.selectedWorkflow
      }).then(response => {
        this.$modal.msgSuccess("提交审批成功");
        this.showWorkflowDialog = false;
        this.goBack();
      });
    },

    /** 处理保存成功后的跳转 */
    handleSaveSuccess() {
      if (this.fromApproval && this.approvalInfo) {
        // 来自审批页面，返回审批页面
        this.$router.push({
          path: '/oa/workflow/task/approval',
          query: {
            taskId: this.approvalInfo.taskId,
            taskName: this.approvalInfo.taskName,
            processInstanceId: this.approvalInfo.processInstanceId,
            businessKey: this.approvalInfo.businessKey
          }
        });
      } else {
        // 来自列表页面，返回列表页面
        this.$tab.closeOpenPage("/oa/document/receive/list?_t=" + Date.now());
      }
    },

    /** 返回 */
    goBack() {
      if (this.fromApproval && this.approvalInfo) {
        // 来自审批页面，返回审批页面
        this.$router.push({
          path: '/oa/workflow/task/approval',
          query: {
            taskId: this.approvalInfo.taskId,
            taskName: this.approvalInfo.taskName,
            processInstanceId: this.approvalInfo.processInstanceId,
            businessKey: this.approvalInfo.businessKey
          }
        });
      } else {
        // 来自列表页面，返回列表页面
        this.$tab.closeOpenPage("/oa/document/receive/list?_t=" + Date.now());
      }
    }
  }
};
</script>

<style scoped>
.document-form-card {
  margin-bottom: 20px;
}

.form-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.document-info-card {
  height: fit-content;
}

.pdf-card {
  height: 850px;
}

.pdf-preview-container {
  height: 800px;
}

.pdf-viewer {
  height: 100%;
}

.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
}

.pdf-title {
  font-weight: bold;
  color: #303133;
}

.pdf-actions {
  display: flex;
  gap: 10px;
}

.pdf-iframe {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.no-pdf-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-card {
  max-height: 400px;
}

.attachment-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-attachments {
  text-align: center;
  padding: 20px;
}

.upload-demo {
  width: 100%;
}

.pdf-uploader {
  width: 100%;
}

.pdf-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.pdf-uploader .el-upload:hover {
  border-color: #409EFF;
}

.pdf-uploader .el-icon-upload {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.pdf-uploader .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.pdf-uploader .el-upload__text em {
  color: #409EFF;
}
</style>
