<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="文档标题" prop="docTitle">
        <el-input
          v-model="queryParams.docTitle"
          placeholder="请输入文档标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="文档编号" prop="docNumber">
        <el-input
          v-model="queryParams.docNumber"
          placeholder="请输入文档编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="收文单位" prop="receiverUnit">
        <el-input
          v-model="queryParams.receiverUnit"
          placeholder="请输入收文单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.oa_doc_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:document:send:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:document:send:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:document:send:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:document:send:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编号" align="center" prop="docNumber" />
      <el-table-column label="文档标题" align="center" prop="docTitle" show-overflow-tooltip />
      <el-table-column label="收文单位" align="center" prop="receiverUnit" show-overflow-tooltip />
      <el-table-column label="发文日期" align="center" prop="sendDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.sendDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="紧急程度" align="center" prop="urgencyLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_urgency_level" :value="scope.row.urgencyLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="密级" align="center" prop="securityLevel">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_security_level" :value="scope.row.securityLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_doc_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="保管期限" align="center" prop="retentionPeriod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.oa_retention_period" :value="scope.row.retentionPeriod"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:document:send:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:document:send:edit']"
            v-if="scope.row.status === '0'"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-promotion"
            @click="handleSubmit(scope.row)"
            v-hasPermi="['oa:document:send:submit']"
            v-if="scope.row.status === '0'"
          >提交审批</el-button>

          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-release"
            @click="handlePublish(scope.row)"
            v-hasPermi="['oa:document:send:publish']"
            v-if="scope.row.status === '2'"
          >发布</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleExportPdf(scope.row)"
            v-hasPermi="['oa:document:send:export']"
            v-if="scope.row.status !== '0'"
          >导出PDF</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-folder-opened"
            @click="handleExportMergedPdf(scope.row)"
            v-hasPermi="['oa:document:send:export']"
            v-if="scope.row.status === '2'"
          >合并导出</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['oa:document:send:remove']"
            v-if="scope.row.status === '0'"
            style="color: #f56c6c;"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改发文对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="文档标题" prop="docTitle">
              <el-input v-model="form.docTitle" placeholder="请输入文档标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档编号" prop="docNumber">
              <el-input v-model="form.docNumber" placeholder="请输入文档编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="收文单位" prop="receiverUnit">
              <el-input v-model="form.receiverUnit" placeholder="请输入收文单位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收文联系人" prop="receiverContact">
              <el-input v-model="form.receiverContact" placeholder="请输入收文联系人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发文日期" prop="sendDate">
              <el-date-picker clearable
                v-model="form.sendDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择发文日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档类型" prop="docType">
              <el-select v-model="form.docType" placeholder="请选择文档类型">
                <el-option label="收文" value="1" />
                <el-option label="发文" value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgencyLevel">
              <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度">
                <el-option
                  v-for="dict in dict.type.oa_urgency_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密级" prop="securityLevel">
              <el-select v-model="form.securityLevel" placeholder="请选择密级">
                <el-option
                  v-for="dict in dict.type.oa_security_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发文字号" prop="docCode">
              <el-input v-model="form.docCode" placeholder="请输入发文字号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="签发人" prop="signatory">
              <el-input v-model="form.signatory" placeholder="请输入签发人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="份数" prop="copies">
              <el-input-number v-model="form.copies" :min="1" :max="999" placeholder="请输入份数" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="主送" prop="mainSend">
              <el-input v-model="form.mainSend" placeholder="请输入主送单位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="保管期限" prop="retentionPeriod">
              <el-select v-model="form.retentionPeriod" placeholder="请选择保管期限">
                <el-option
                  v-for="dict in dict.type.oa_retention_period"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="抄送" prop="copySend">
          <el-input v-model="form.copySend" placeholder="请输入抄送单位" />
        </el-form-item>
        <el-form-item label="文档内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入文档内容" />
        </el-form-item>
        <el-form-item label="附件上传">
          <file-upload v-model="form.attachments" :limit="5" :file-size="50" :file-type="['pdf', 'doc', 'docx', 'jpg', 'png']"></file-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 工作流选择对话框 -->
    <el-dialog title="选择审批流程" :visible.sync="showWorkflowDialog" width="500px" append-to-body>
      <el-form label-width="100px">
        <el-form-item label="审批流程" required>
          <el-select v-model="selectedWorkflow" placeholder="请选择审批流程" style="width: 100%">
            <el-option
              v-for="workflow in workflowList"
              :key="workflow.key"
              :label="workflow.name"
              :value="workflow.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="流程说明">
          <div v-if="selectedWorkflow === 'send_document_approval'" class="workflow-description">
            <p><strong>发文普通审批流程：</strong></p>
            <p>1. 起草人提交</p>
            <p>2. 部门负责人审批</p>
            <p>3. 办公室审核</p>
            <p>4. 领导审批</p>
          </div>
          <div v-else-if="selectedWorkflow === 'send_document_special'" class="workflow-description">
            <p><strong>发文特殊审批流程：</strong></p>
            <p>1. 起草人提交</p>
            <p>2. 部门负责人审批</p>
            <p>3. 办公室审核</p>
            <p>4. 分管领导审批</p>
            <p>5. 主要领导审批</p>
          </div>
          <div v-else class="workflow-description">
            <p>请选择审批流程查看详细说明</p>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showWorkflowDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmSubmitApproval">确认提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSendDocument, getSendDocument, delSendDocument, addSendDocument, updateSendDocument, submitSendDocument, publishSendDocument, exportSendDocumentPdf, exportMergedSendDocumentPdf, getAvailableWorkflows } from "@/api/oa/document";

export default {
  name: "SendDocument",
  dicts: ['oa_doc_status', 'oa_urgency_level', 'oa_security_level', 'oa_retention_period'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 发文表格数据
      documentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        docTitle: null,
        docNumber: null,
        receiverUnit: null,
        status: null,

      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        docTitle: [
          { required: true, message: "文档标题不能为空", trigger: "blur" }
        ],
        receiverUnit: [
          { required: true, message: "收文单位不能为空", trigger: "blur" }
        ],
        sendDate: [
          { required: true, message: "发文日期不能为空", trigger: "blur" }
        ],
        mainSend: [
          { required: true, message: "主送单位不能为空", trigger: "blur" }
        ]
      },
      // 工作流选择相关
      showWorkflowDialog: false,
      selectedWorkflow: '',
      workflowList: [],
      currentSubmitRow: null
    };
  },
  created() {
    this.getList();
  },
  activated() {
    // 当从缓存中激活时重新查询数据，确保显示最新数据
    this.getList();
  },
  methods: {
    /** 查询发文列表 */
    getList() {
      this.loading = true;
      listSendDocument(this.queryParams).then(response => {
        this.documentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        docId: null,
        docTitle: null,
        docNumber: null,
        docType: '2',
        receiverUnit: null,
        receiverContact: null,
        sendDate: new Date(), // 新建时自动设置为当前日期
        urgencyLevel: '2',
        securityLevel: '1',
        docCode: null,
        signatory: null,
        copies: 1,
        mainSend: null,
        copySend: null,
        retentionPeriod: '0', // 默认选择"无"
        docContent: null,
        attachments: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.docId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.$router.push('/oa/document/send/add');
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const docId = row.docId || this.ids[0];
      this.$router.push('/oa/document/send/edit/' + docId);
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.$router.push('/oa/document/send/detail/' + row.docId);
    },

    /** 查看按钮操作 */
    handleView(row) {
      this.$router.push('/oa/document/send/detail/' + row.docId);
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.docId != null) {
            updateSendDocument(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSendDocument(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const docIds = row ? row.docId : this.ids;
      if (!docIds || (Array.isArray(docIds) && docIds.length === 0)) {
        this.$modal.msgWarning("请选择要删除的数据");
        return;
      }

      const docTitles = row ? row.docTitle : this.documentList
        .filter(doc => this.ids.includes(doc.docId))
        .map(doc => doc.docTitle)
        .join('、');

      this.$modal.confirm('是否确认删除发文"' + docTitles + '"？').then(() => {
        return delSendDocument(docIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        console.error('删除失败:', error);
        this.$modal.msgError("删除失败：" + (error.message || "请检查网络连接"));
      });
    },
    /** 提交审批操作 */
    handleSubmit(row) {
      this.$modal.confirm('确认提交审批吗？提交后将进入发文审批流程。').then(() => {
        submitSendDocument(row.docId, {}).then(response => {
          this.$modal.msgSuccess("提交审批成功");
          this.getList();
        }).catch(error => {
          console.error('提交审批失败:', error);
          this.$modal.msgError("提交审批失败：" + (error.message || "请检查网络连接"));
        });
      });
    },
    /** 发布操作 */
    handlePublish(row) {
      this.$modal.confirm('是否确认发布发文"' + row.docTitle + '"？').then(function() {
        return publishSendDocument(row.docId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("发布成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/document/send/export', {
        ...this.queryParams
      }, `send_document_${new Date().getTime()}.xlsx`)
    },

    /** 确认提交审批 */
    confirmSubmitApproval() {
      if (!this.selectedWorkflow) {
        this.$modal.msgWarning("请选择审批流程");
        return;
      }

      this.$modal.confirm('确认提交审批吗？提交后将无法修改。').then(() => {
        submitSendDocument(this.currentSubmitRow.docId, {
          workflowKey: this.selectedWorkflow
        }).then(response => {
          this.$modal.msgSuccess("提交审批成功");
          this.showWorkflowDialog = false;
          this.getList();
        }).catch(error => {
          console.error('提交审批失败:', error);
          this.$modal.msgError("提交审批失败：" + (error.message || "请检查网络连接"));
        });
      });
    },

    /** 加载工作流列表 */
    loadWorkflowList() {
      console.log('开始加载工作流列表...');
      getAvailableWorkflows('send').then(response => {
        console.log('工作流API响应:', response);
        if (response.data && Array.isArray(response.data)) {
          this.workflowList = response.data.map(workflow => ({
            key: workflow.workflowKey,
            name: workflow.workflowName,
            description: workflow.description || '暂无描述'
          }));
          console.log('工作流列表加载成功:', this.workflowList);
        } else {
          console.warn('API返回数据格式异常:', response);
          this.useDefaultWorkflows();
        }
      }).catch(error => {
        console.error('获取工作流列表失败:', error);
        this.useDefaultWorkflows();
      });
    },

    /** 使用默认工作流列表 */
    useDefaultWorkflows() {
      console.log('使用默认工作流列表');
      this.workflowList = [
        {
          key: 'send_document_approval',
          name: '发文普通审批流程',
          description: '起草人提交 -> 部门负责人审批 -> 办公室审核 -> 领导审批'
        },
        {
          key: 'send_document_special',
          name: '发文特殊审批流程',
          description: '起草人提交 -> 部门负责人审批 -> 办公室审核 -> 分管领导审批 -> 主要领导审批'
        }
      ];
    },
    /** 导出PDF操作 */
    handleExportPdf(row) {
      exportSendDocumentPdf(row.docId).then(response => {
        this.downloadPdfFile(response, row.docTitle + '_发文.pdf');
      }).catch(error => {
        this.$modal.msgError('导出PDF失败：' + (error.message || '未知错误'));
      });
    },

    /** 合并导出PDF操作 */
    handleExportMergedPdf(row) {
      this.$modal.confirm('是否确认合并导出发文"' + row.docTitle + '"？\n合并内容包括：审批单、红头文件、底稿、附件。').then(() => {
        this.$modal.loading("正在生成合并PDF文件，请稍候...");
        return exportMergedSendDocumentPdf(row.docId);
      }).then(response => {
        this.downloadPdfFile(response, row.docTitle + '_合并文件.pdf');
        this.$modal.closeLoading();
        this.$modal.msgSuccess("合并PDF文件导出成功");
      }).catch(error => {
        this.$modal.closeLoading();
        if (error !== 'cancel') {
          this.$modal.msgError('合并导出失败：' + (error.message || '未知错误'));
        }
      });
    },

    /** 下载PDF文件 */
    downloadPdfFile(data, fileName) {
      try {
        const blob = new Blob([data], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('下载文件失败:', error);
        this.$modal.msgError('下载文件失败，请联系管理员！');
      }
    },


  }
};
</script>

<style scoped>
.workflow-description {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
}

.workflow-description p {
  margin: 5px 0;
}

.workflow-description strong {
  color: #303133;
}
</style>
