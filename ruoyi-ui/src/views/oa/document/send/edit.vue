<template>
  <div class="app-container">
    <el-card class="document-form-card">
      <div slot="header" class="clearfix">
        <span class="form-title">{{ docId ? '编辑发文' : '新增发文' }}</span>
        <div style="float: right;">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button type="success" @click="showTemplateDialog" v-if="permissions.canApplyTemplate">套红</el-button>
          <el-button type="info" @click="showSealDialog" v-if="permissions.canAddSeal">印章</el-button>
          <el-button type="warning" icon="el-icon-download" @click="handleExportPdf" v-if="form.docId">下载PDF</el-button>
        </div>
      </div>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <!-- Left Column: Document Information -->
          <el-col :span="10">
            <div class="form-section">

              <el-form-item label="文档编号" prop="docNumber">
                <el-input v-model="form.docNumber" placeholder="请输入文档编号" />
              </el-form-item>

              <el-form-item label="公文标题" prop="docTitle">
                <el-input v-model="form.docTitle" placeholder="请输入公文标题" />
              </el-form-item>

              <el-form-item label="紧急程度" prop="urgencyLevel">
                <el-select v-model="form.urgencyLevel" placeholder="请选择紧急程度" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.oa_urgency_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="密级" prop="securityLevel">
                <el-select v-model="form.securityLevel" placeholder="请选择密级" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.oa_security_level"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="发文人" prop="senderName">
                <el-input v-model="form.senderName" placeholder="请输入发文人" />
              </el-form-item>

              <el-form-item label="发文日期" prop="sendDate">
                <el-date-picker
                  v-model="form.sendDate"
                  type="date"
                  placeholder="选择发文日期"
                  value-format="yyyy-MM-dd"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="保管期限" prop="retentionPeriod">
                <el-select v-model="form.retentionPeriod" placeholder="请选择保管期限" style="width: 100%">
                  <el-option
                    v-for="dict in dict.type.oa_retention_period"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="状态" prop="status">
                <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="草稿" value="0" />
                  <el-option label="审批中" value="1" />
                  <el-option label="已审批" value="2" />
                  <el-option label="已发布" value="3" />
                  <el-option label="已归档" value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注"
                />
              </el-form-item>

              <!-- 附件上传 -->
              <el-form-item label="附件">
                <el-upload
                  :action="uploadUrl"
                  :headers="headers"
                  :on-success="handleUploadSuccess"
                  :on-remove="handleUploadRemove"
                  :file-list="fileList"
                  multiple
                  class="upload-demo"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">只能上传jpg/png/pdf文件，且不超过10MB</div>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>

          <!-- Right Column: Document Content -->
          <el-col :span="14">
            <div class="form-section">
              <!-- 模板选择器 -->
              <div class="template-selector" style="margin-bottom: 15px;">
                <el-row :gutter="10" type="flex" align="middle">
                  <el-col :span="6">
                    <label style="font-weight: bold; color: #606266;">选择模板：</label>
                  </el-col>
                  <el-col :span="12">
                    <el-select
                      v-model="selectedTemplate"
                      placeholder="请选择公文模板"
                      style="width: 100%"
                      @change="handleTemplateChange"
                      clearable
                      size="small"
                    >
                      <el-option
                        v-for="template in templates"
                        :key="template.templateId || template.id"
                        :label="template.templateName || template.name"
                        :value="template.templateId || template.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-button
                      size="small"
                      type="text"
                      @click="refreshTemplates"
                      :loading="templateLoading"
                    >
                      {{ templateLoading ? '加载中...' : '刷新模板' }}
                    </el-button>
                  </el-col>
                </el-row>
                <div v-if="templates.length === 0 && !templateLoading" style="margin-top: 5px;">
                  <el-alert
                    title="暂无可用模板"
                    type="info"
                    :closable="false"
                    size="small"
                  >
                    <span slot="description">
                      请联系管理员添加公文模板，或点击"刷新模板"重新加载
                    </span>
                  </el-alert>
                </div>
              </div>

              <!-- 富文本编辑器 -->
              <div class="editor-container">
                <tiny-editor
                  v-model="form.docContent"
                  height="800px"
                  :permissions="permissions"
                  :disabled="false"
                />
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 套红模板选择对话框 -->
    <el-dialog title="选择红头模板" :visible.sync="templateDialogVisible" width="600px">
      <el-table :data="availableTemplates" @selection-change="handleTemplateSelection">
        <el-table-column type="radio" width="55">
          <template slot-scope="scope">
            <el-radio v-model="selectedTemplateId" :label="scope.row.templateId">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="templateName" label="模板名称" width="200"></el-table-column>
        <el-table-column prop="issuingOrgan" label="发文机关" width="150"></el-table-column>
        <el-table-column prop="templateType" label="模板类型" width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.templateType === '1'">红头文件</span>
            <span v-else-if="scope.row.templateType === '2'">便函</span>
            <span v-else-if="scope.row.templateType === '3'">传真</span>
            <span v-else-if="scope.row.templateType === '4'">纪要</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="templateDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="applyTemplate">应用模板</el-button>
      </div>
    </el-dialog>

    <!-- 印章选择对话框 -->
    <el-dialog title="选择印章" :visible.sync="sealDialogVisible" width="600px">
      <el-table :data="availableSeals" @selection-change="handleSealSelection">
        <el-table-column type="radio" width="55">
          <template slot-scope="scope">
            <el-radio v-model="selectedSealId" :label="scope.row.sealId">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="sealName" label="印章名称" width="200"></el-table-column>
        <el-table-column prop="sealType" label="印章类型" width="100"></el-table-column>
        <el-table-column prop="description" label="说明" width="200"></el-table-column>
        <el-table-column label="预览" width="100">
          <template slot-scope="scope">
            <img v-if="scope.row.sealImage"
                 :src="'data:image/png;base64,' + scope.row.sealImage"
                 style="width: 40px; height: 40px;" />
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sealDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addSeal">添加印章</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getSendDocument, addSendDocument, updateSendDocument, exportSendDocumentPdf, checkSendEditPermission, applySendTemplate, addSendSeal } from "@/api/oa/document";
import { getAvailableTemplates } from "@/api/oa/template";
import { getAvailableSeals } from "@/api/oa/seal";
import TinyEditor from '@/components/TinyEditor'
import { getToken } from '@/utils/auth'

export default {
  name: "SendDocumentEdit",
  dicts: ['oa_urgency_level', 'oa_security_level', 'oa_retention_period'],
  components: {
    TinyEditor
  },
  data() {
    return {
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload",
      headers: {
        Authorization: "Bearer " + getToken()
      },
      // 表单数据
      form: {
        docId: null,
        docNumber: null,
        docTitle: null,
        receiverUnit: null,
        receiverContact: null,
        sendDate: null,
        urgencyLevel: '2',
        securityLevel: '1',
        docContent: '',
        senderName: null,
        sendTime: null,
        status: '0',
        retentionPeriod: '0',
        remark: null
      },
      // 表单验证规则
      rules: {
        docNumber: [
          { required: true, message: "文档编号不能为空", trigger: "blur" }
        ],
        docTitle: [
          { required: true, message: "公文标题不能为空", trigger: "blur" }
        ],
        sendDate: [
          { required: true, message: "发文日期不能为空", trigger: "change" }
        ]
      },
      // 模板相关
      templates: [],
      selectedTemplate: '',
      templateLoading: false,
      // 附件列表
      fileList: [],
      // 文档ID
      docId: null,
      // 权限控制
      permissions: {
        canEdit: false,
        isLastApprover: false,
        canApplyTemplate: false,
        canAddSeal: false
      },
      // 套红模板相关
      templateDialogVisible: false,
      availableTemplates: [],
      selectedTemplateId: null,
      // 印章相关
      sealDialogVisible: false,
      availableSeals: [],
      selectedSealId: null,
      // 来源信息
      fromApproval: false,
      approvalInfo: null
    };
  },
  created() {
    this.docId = this.$route.params.docId;
    this.loadTemplates();

    // 简化权限逻辑：能进入编辑页面就允许编辑
    this.permissions.canEdit = true;

    // 检查是否来自审批页面
    this.fromApproval = this.$route.query.fromApproval === 'true';
    if (this.fromApproval) {
      // 保存审批页面信息，用于返回
      this.approvalInfo = {
        taskId: this.$route.query.taskId,
        taskName: this.$route.query.taskName,
        processInstanceId: this.$route.query.processInstanceId,
        businessKey: this.$route.query.businessKey
      };
    }

    if (this.docId) {
      this.getDocumentInfo();
      this.checkPermissions(); // 只检查套红和印章权限
    }

    // 设置全局变量供TinyMCE使用
    window.currentDocId = this.docId;
    window.showTemplateDialog = this.showTemplateDialog.bind(this);
    window.showSealDialog = this.showSealDialog.bind(this);
  },

  beforeDestroy() {
    // 清理全局变量
    delete window.currentDocId;
    delete window.showTemplateDialog;
    delete window.showSealDialog;
  },
  methods: {
    /** 加载模板列表 */
    async loadTemplates() {
      this.templateLoading = true;
      try {
        const response = await getAvailableTemplates();
        this.templates = response.data || [];
        console.log('加载的模板数据:', this.templates);

        if (this.templates.length === 0) {
          console.warn('模板列表为空，可能需要在后台添加模板数据');
          // 如果API返回空数据，使用内置模板作为备选
          this.loadBuiltinTemplates();
        }
      } catch (error) {
        console.error('加载模板失败:', error);
        this.$message.warning('加载模板失败，使用内置模板');
        this.loadBuiltinTemplates();
      } finally {
        this.templateLoading = false;
      }
    },

    /** 加载内置模板（备选方案） */
    loadBuiltinTemplates() {
      this.templates = [
        {
          id: 'builtin_1',
          name: '通知模板',
          content: `<h1 style="text-align: center;">通知</h1>
<p style="text-align: right;">文号：〔2025〕001号</p>
<p>各有关单位：</p>
<p style="text-indent: 2em;">现将有关事项通知如下：</p>
<p style="text-indent: 2em;">一、...</p>
<p style="text-indent: 2em;">二、...</p>
<p style="text-indent: 2em;">特此通知。</p>
<p style="text-align: right;">某某单位<br/>2025年7月12日</p>`
        },
        {
          id: 'builtin_2',
          name: '函件模板',
          content: `<h1 style="text-align: center;">函</h1>
<p style="text-align: right;">文号：〔2025〕函001号</p>
<p>某某单位：</p>
<p style="text-indent: 2em;">关于...事项，现函告如下：</p>
<p style="text-indent: 2em;">...</p>
<p style="text-indent: 2em;">请予以支持配合。</p>
<p style="text-align: right;">某某单位<br/>2025年7月12日</p>`
        },
        {
          id: 'builtin_3',
          name: '会议纪要模板',
          content: `<h1 style="text-align: center;">会议纪要</h1>
<p><strong>会议时间：</strong>2025年7月12日</p>
<p><strong>会议地点：</strong></p>
<p><strong>主持人：</strong></p>
<p><strong>参会人员：</strong></p>
<p><strong>会议议题：</strong></p>
<p><strong>会议内容：</strong></p>
<p style="text-indent: 2em;">一、...</p>
<p style="text-indent: 2em;">二、...</p>
<p><strong>会议决定：</strong></p>
<p style="text-indent: 2em;">...</p>`
        }
      ];
      console.log('已加载内置模板:', this.templates.length, '个');
    },

    /** 刷新模板列表 */
    refreshTemplates() {
      this.loadTemplates();
    },
    /** 获取文档信息 */
    getDocumentInfo() {
      getSendDocument(this.docId).then(response => {
        this.form = response.data;

        // 解析附件信息
        if (response.data.attachments) {
          try {
            const attachments = JSON.parse(response.data.attachments);
            if (Array.isArray(attachments)) {
                let temp = 1;
                this.fileList = attachments.map(f => ({
                    ...f,
                    name: f.fileName,
                    size: f.fileSize,
                    uid: new Date().getTime() + temp++
                }));
            }
          } catch (e) {
            this.fileList = [];
          }
        }
      });
    },
    /** 模板选择变化 */
    handleTemplateChange(templateId) {
      if (templateId) {
        const template = this.templates.find(t =>
          (t.templateId || t.id) === templateId
        );
        if (template) {
          const templateName = template.templateName || template.name;
          const templateContent = template.templateContent || template.content;

          if (this.form.docContent.trim()) {
            this.$modal.confirm('选择模板将覆盖当前内容，是否继续？').then(() => {
              this.form.docContent = templateContent;
              this.$modal.msgSuccess(`已应用模板: ${templateName}`);
            }).catch(() => {
              this.selectedTemplate = '';
            });
          } else {
            this.form.docContent = templateContent;
            this.$modal.msgSuccess(`已应用模板: ${templateName}`);
          }
        }
      }
    },
    /** 附件上传成功 */
    handleUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        // 更新 fileList 中对应文件的信息
        const index = fileList.findIndex(f => f.uid === file.uid);
        if (index > -1) {
            const updatedFile = {
                ...fileList[index],
                name: response.fileName, // 使用服务器返回的文件名
                url: response.url,
                fileSize: response.size || file.size
            };
            fileList.splice(index, 1, updatedFile);
        }
        this.fileList = fileList;
      } else {
        this.$message.error(response.msg);
        this.fileList = fileList.filter(item => item.uid !== file.uid);
      }
    },
    /** 附件移除 */
    handleUploadRemove(file, fileList) {
      this.fileList = fileList;
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理附件信息
          if (this.fileList.length > 0) {
            this.form.attachments = JSON.stringify(this.fileList.map(f => ({
              fileName: f.name,
              url: f.url,
              fileSize: f.size
            })));
          }

          // 根据是否有docId判断是新增还是修改
          if (this.docId) {
            // 修改模式
            updateSendDocument(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.handleSaveSuccess();
            }).catch(error => {
              console.error('修改发文失败:', error);
              this.$modal.msgError("修改失败：" + (error.message || "请检查网络连接"));
            });
          } else {
            // 新增模式
            addSendDocument(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.handleSaveSuccess();
            }).catch(error => {
              console.error('新增发文失败:', error);
              this.$modal.msgError("新增失败：" + (error.message || "请检查网络连接"));
            });
          }
        }
      });
    },

    /** 导出PDF操作 */
    handleExportPdf() {
      if (!this.form.docId) {
        this.$modal.msgWarning("请先保存文档后再下载PDF");
        return;
      }

      this.$modal.confirm('是否确认下载发文"' + this.form.docTitle + '"的PDF文件？').then(() => {
        this.$modal.loading("正在生成PDF文件，请稍候...");
        return exportSendDocumentPdf(this.form.docId);
      }).then(response => {
        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.form.docTitle}_发文.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$modal.closeLoading();
        this.$modal.msgSuccess("PDF文件下载成功");
      }).catch(error => {
        this.$modal.closeLoading();
        this.$modal.msgError("PDF生成失败: " + (error.message || "未知错误"));
      });
    },

    /** 取消按钮 */
    cancel() {
      // 关闭当前标签页并跳转到列表页面
      this.$tab.closeOpenPage('/oa/document/send');
    },
    /** 检查权限 - 简化版，只检查套红和印章权限 */
    async checkPermissions() {
      if (!this.docId) return;

      try {
        const response = await checkSendEditPermission(this.docId);
        // 保持编辑权限为true，只更新套红和印章权限
        this.permissions.canEdit = true;
        this.permissions.isLastApprover = response.data.isLastApprover || false;
        this.permissions.canApplyTemplate = response.data.canApplyTemplate || false;
        this.permissions.canAddSeal = response.data.canAddSeal || false;
      } catch (error) {
        console.error('检查权限失败:', error);
        // 权限检查失败时，保持基本编辑权限
        this.permissions.canEdit = true;
        this.permissions.isLastApprover = false;
        this.permissions.canApplyTemplate = false;
        this.permissions.canAddSeal = false;
      }
    },

    /** 显示套红模板对话框 */
    async showTemplateDialog() {
      try {
        const response = await getAvailableTemplates('1'); // 1=红头文件
        this.availableTemplates = response.data || [];
        this.templateDialogVisible = true;
      } catch (error) {
        console.error('加载模板失败:', error);
        this.$modal.msgError('加载模板失败');
      }
    },

    /** 应用红头模板 */
    async applyTemplate() {
      if (!this.selectedTemplateId) {
        this.$modal.msgWarning('请选择模板');
        return;
      }

      try {
        await applySendTemplate(this.docId, {
          templateId: this.selectedTemplateId
        });
        this.$modal.msgSuccess('红头模板应用成功');
        this.templateDialogVisible = false;
        // 重新加载文档内容
        this.getDocumentInfo();
      } catch (error) {
        console.error('应用模板失败:', error);
        this.$modal.msgError('应用模板失败: ' + (error.message || '请检查网络连接'));
      }
    },

    /** 显示印章选择对话框 */
    async showSealDialog() {
      try {
        const response = await getAvailableSeals();
        this.availableSeals = response.data || [];
        this.sealDialogVisible = true;
      } catch (error) {
        console.error('加载印章失败:', error);
        this.$modal.msgError('加载印章失败');
      }
    },

    /** 添加印章 */
    async addSeal() {
      if (!this.selectedSealId) {
        this.$modal.msgWarning('请选择印章');
        return;
      }

      try {
        await addSendSeal(this.docId, {
          sealId: this.selectedSealId,
          position: 'top:50%;left:50%;transform:translate(-50%,-50%);' // 默认居中位置
        });
        this.$modal.msgSuccess('印章添加成功');
        this.sealDialogVisible = false;
        // 重新加载文档内容
        this.getDocumentInfo();
      } catch (error) {
        console.error('添加印章失败:', error);
        this.$modal.msgError('添加印章失败: ' + (error.message || '请检查网络连接'));
      }
    },

    /** 模板选择处理 */
    handleTemplateSelection(selection) {
      // 单选处理
    },

    /** 印章选择处理 */
    handleSealSelection(selection) {
      // 单选处理
    },

    /** 处理保存成功后的跳转 */
    handleSaveSuccess() {
      if (this.fromApproval && this.approvalInfo) {
        // 来自审批页面，返回审批页面
        this.$router.push({
          path: '/oa/workflow/task/approval',
          query: {
            taskId: this.approvalInfo.taskId,
            taskName: this.approvalInfo.taskName,
            processInstanceId: this.approvalInfo.processInstanceId,
            businessKey: this.approvalInfo.businessKey
          }
        });
      } else {
        // 来自列表页面，返回列表页面
        this.$tab.closeOpenPage('/oa/document/send');
      }
    },

    /** 返回按钮操作 */
    goBack() {
      if (this.fromApproval && this.approvalInfo) {
        // 来自审批页面，返回审批页面
        this.$router.push({
          path: '/oa/workflow/task/approval',
          query: {
            taskId: this.approvalInfo.taskId,
            taskName: this.approvalInfo.taskName,
            processInstanceId: this.approvalInfo.processInstanceId,
            businessKey: this.approvalInfo.businessKey
          }
        });
      } else {
        // 来自列表页面，返回列表页面
        this.$tab.closeOpenPage('/oa/document/send');
      }
    }

  }
};
</script>

<style scoped>
.document-form-card {
  margin: 20px;
}

.form-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.form-section h3 {
  margin-bottom: 15px;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

/* 工作流选择对话框样式 */
.workflow-description {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.workflow-description p {
  margin: 5px 0;
  color: #606266;
}

.workflow-description strong {
  color: #303133;
}

.editor-container {
  width: 100%;
  min-height: 800px;
}

.editor-container .tox-tinymce {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.template-selector {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.upload-demo {
  margin-top: 10px;
}
</style>
