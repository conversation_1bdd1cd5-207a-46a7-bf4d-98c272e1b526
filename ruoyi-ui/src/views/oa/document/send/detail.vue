<template>
  <div class="app-container">
    <el-card class="document-detail-card">
      <div slot="header" class="clearfix">
        <span class="document-title">{{ document.docTitle }}</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <el-row :gutter="20">
        <!-- Left Column: Document Information -->
        <el-col :span="8">
          <div class="document-info">
            <h3>公文信息</h3>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="文档编号">{{ document.docNumber }}</el-descriptions-item>
              <el-descriptions-item label="公文标题">{{ document.docTitle }}</el-descriptions-item>

              <el-descriptions-item label="紧急程度">
                <dict-tag :options="dict.type.oa_urgency_level" :value="document.urgencyLevel"/>
              </el-descriptions-item>
              <el-descriptions-item label="密级">
                <dict-tag :options="dict.type.oa_security_level" :value="document.securityLevel"/>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <dict-tag :options="dict.type.oa_doc_status" :value="document.status"/>
              </el-descriptions-item>
              <el-descriptions-item label="保管期限">
                <dict-tag :options="dict.type.oa_retention_period" :value="document.retentionPeriod"/>
              </el-descriptions-item>
              <el-descriptions-item label="发文人">{{ document.senderName }}</el-descriptions-item>
              <el-descriptions-item label="发文日期">
                {{ parseTime(document.sendDate, '{y}-{m}-{d}') }}
              </el-descriptions-item>
              <el-descriptions-item label="当前步骤">{{ document.currentStep }}</el-descriptions-item>
              <el-descriptions-item label="当前处理人">{{ document.currentAssignee }}</el-descriptions-item>
              <el-descriptions-item label="创建人">{{ document.createBy }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ parseTime(document.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 操作按钮 -->
            <div class="action-buttons" style="margin-top: 20px;">
              <el-button
                type="primary"
                icon="el-icon-edit"
                @click="handleEdit"
                v-hasPermi="['oa:document:send:edit']"
              >编辑</el-button>

              <el-button
                type="primary"
                icon="el-icon-upload"
                @click="handlePublish"
                v-hasPermi="['oa:document:send:publish']"
                v-if="document.status === '2'"
              >发布</el-button>
              <el-button
                type="warning"
                icon="el-icon-download"
                @click="handleExportPdf"
                v-hasPermi="['oa:document:send:export']"
              >导出PDF</el-button>
              <el-button
                type="success"
                icon="el-icon-folder-opened"
                @click="handleExportMergedPdf"
                v-hasPermi="['oa:document:send:export']"
                v-if="document.status === '2'"
              >合并导出</el-button>
            </div>
          </div>
        </el-col>

        <!-- Right Column: Document Content -->
        <el-col :span="16">
          <div class="document-content">
            <h3>公文内容</h3>
            <div class="content-editor">
              <tiny-editor
                v-model="docContent"
                height="600px"
                :disabled="true"
              />
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 附件信息 -->
      <div class="document-attachments" v-if="document.attachments" style="margin-top: 20px;">
        <h3>附件信息</h3>
        <el-table :data="attachmentList" border>
          <el-table-column label="附件名称" prop="fileName" />
          <el-table-column label="文件大小" prop="fileSize">
            <template slot-scope="scope">
              <span>{{ formatFileSize(scope.row.fileSize) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="downloadAttachment(scope.row)">下载</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 备注信息 -->
      <div class="document-remarks" v-if="document.remark" style="margin-top: 20px;">
        <h3>备注</h3>
        <div class="remark-text">{{ document.remark }}</div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getSendDocument, exportSendDocumentPdf, exportMergedSendDocumentPdf, submitSendDocument, publishSendDocument } from "@/api/oa/document";
import TinyEditor from '@/components/TinyEditor'

export default {
  name: "SendDocumentDetail",
  dicts: ['oa_urgency_level', 'oa_security_level', 'oa_doc_status', 'oa_retention_period'],
  components: {
    TinyEditor
  },
  data() {
    return {
      // 文档信息
      document: {},
      // 文档内容
      docContent: '',
      // 附件列表
      attachmentList: [],
      // 文档ID
      docId: null
    };
  },
  created() {
    this.docId = this.$route.params.docId;
    if (this.docId) {
      this.getDocumentInfo();
    }
  },
  methods: {
    formatFileSize(size) {
      if (!size) return '0 Bytes';
      const i = Math.floor(Math.log(size) / Math.log(1024));
      return (size / Math.pow(1024, i)).toFixed(2) + ' ' + ['B', 'KB', 'MB', 'GB', 'TB'][i];
    },
    /** 获取文档信息 */
    getDocumentInfo() {
      getSendDocument(this.docId).then(response => {
        this.document = response.data;
        this.docContent = response.data.docContent || '';

        // 解析附件信息
        if (response.data.attachments) {
          try {
            this.attachmentList = JSON.parse(response.data.attachments);
          } catch (e) {
            this.attachmentList = [];
          }
        }
      });
    },
    /** 编辑按钮操作 */
    handleEdit() {
      this.$router.push('/oa/document/send/edit/' + this.docId);
    },

    /** 发布操作 */
    handlePublish() {
      this.$modal.confirm('是否确认发布发文"' + this.document.docTitle + '"？').then(() => {
        return publishSendDocument(this.docId);
      }).then(() => {
        this.getDocumentInfo();
        this.$modal.msgSuccess("发布成功");
      }).catch(() => {});
    },
    /** 导出PDF操作 */
    handleExportPdf() {
      this.$modal.confirm('是否确认导出发文"' + this.document.docTitle + '"为PDF？').then(() => {
        return exportSendDocumentPdf(this.docId);
      }).then(() => {
        this.$modal.msgSuccess("导出成功");
      }).catch(() => {});
    },

    /** 合并导出PDF操作 */
    handleExportMergedPdf() {
      this.$modal.confirm('是否确认合并导出发文"' + this.document.docTitle + '"？\n合并内容包括：审批单、红头文件、底稿、附件。').then(() => {
        this.$modal.loading("正在生成合并PDF文件，请稍候...");
        return exportMergedSendDocumentPdf(this.docId);
      }).then(response => {
        // 创建下载链接
        const blob = new Blob([response], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${this.document.docTitle}_合并文件.pdf`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$modal.closeLoading();
        this.$modal.msgSuccess("合并PDF文件导出成功");
      }).catch(error => {
        this.$modal.closeLoading();
        if (error !== 'cancel') {
          this.$modal.msgError('合并导出失败：' + (error.message || '未知错误'));
        }
      });
    },

    /** 下载附件 */
    downloadAttachment(attachment) {
      // 实现附件下载逻辑
      this.$modal.msgInfo("附件下载功能待实现");
    },
    /** 返回按钮操作 */
    goBack() {
      // 关闭当前标签页并跳转到列表页面
      this.$tab.closeOpenPage('/oa/document/send');
    }
  }
};
</script>

<style scoped>
.document-detail-card {
  margin: 20px;
}

.document-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.document-info h3,
.document-content h3 {
  margin-bottom: 15px;
  color: #409EFF;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 5px;
}

.action-buttons {
  text-align: center;
}

.action-buttons .el-button {
  margin: 0 5px;
}

.content-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  min-height: 600px;
}

.remark-text {
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}
</style>
