<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 日历视图 -->
      <el-col :span="18">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>日程日历</span>
            <el-button-group style="float: right;">
              <el-button size="mini" @click="changeView('month')" :type="currentView === 'month' ? 'primary' : ''">月</el-button>
              <el-button size="mini" @click="changeView('week')" :type="currentView === 'week' ? 'primary' : ''">周</el-button>
              <el-button size="mini" @click="changeView('day')" :type="currentView === 'day' ? 'primary' : ''">日</el-button>
            </el-button-group>
          </div>
          <el-calendar v-model="currentDate" v-if="currentView === 'month'">
            <template slot="dateCell" slot-scope="{date, data}">
              <div class="calendar-day">
                <p :class="data.isSelected ? 'is-selected' : ''">
                  {{ data.day.split('-').slice(2).join('-') }}
                </p>
                <div class="schedule-items">
                  <div
                    v-for="schedule in getSchedulesForDate(date)"
                    :key="schedule.scheduleId"
                    class="schedule-item"
                    :class="'priority-' + schedule.priority"
                    @click="viewSchedule(schedule)"
                  >
                    {{ schedule.title }}
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>

          <!-- 周视图和日视图的简化实现 -->
          <div v-else class="week-day-view">
            <el-table :data="filteredSchedules" style="width: 100%">
              <el-table-column prop="title" label="日程标题" show-overflow-tooltip></el-table-column>
              <el-table-column prop="startTime" label="开始时间" width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="endTime" label="结束时间" width="180">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="location" label="地点" show-overflow-tooltip></el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template slot-scope="scope">
                  <dict-tag :options="dict.type.oa_schedule_status" :value="scope.row.status"/>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" @click="viewSchedule(scope.row)">查看</el-button>
                  <el-button size="mini" type="text" @click="editSchedule(scope.row)">编辑</el-button>
                  <el-button size="mini" type="text" @click="deleteSchedule(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

      <!-- 侧边栏 -->
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>快速操作</span>
          </div>
          <el-button type="primary" size="small" @click="addSchedule" style="width: 100%; margin-bottom: 10px; text-align: center;">
            <i class="el-icon-plus"></i> 新增日程
          </el-button>
          <el-button type="success" size="small" @click="getTodaySchedules" style="width: 100%; margin-bottom: 10px; text-align: center;">
            <i class="el-icon-date"></i> 今日日程
          </el-button>
          <el-button type="info" size="small" @click="getWeekSchedules" style="width: 100%; text-align: center;">
            <i class="el-icon-time"></i> 本周日程
          </el-button>
        </el-card>

        <!-- 今日日程 -->
        <el-card class="box-card" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>今日日程</span>
          </div>
          <el-timeline>
            <el-timeline-item
              v-for="schedule in todaySchedules"
              :key="schedule.scheduleId"
              :timestamp="parseTime(schedule.startTime, '{h}:{i}')"
              placement="top"
            >
              <el-card>
                <h4>{{ schedule.title }}</h4>
                <p v-if="schedule.location">地点：{{ schedule.location }}</p>
                <p v-if="schedule.participants">参与人：{{ schedule.participants }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改日程对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日程标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入日程标题" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                type="datetime"
                placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                v-model="form.endTime"
                type="datetime"
                placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%;">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="地点" prop="location">
          <el-input v-model="form.location" placeholder="请输入地点" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="重要程度" prop="priority">
              <el-select v-model="form.priority" placeholder="请选择重要程度" style="width: 100%;">
                <el-option label="普通" value="1"></el-option>
                <el-option label="重要" value="2"></el-option>
                <el-option label="紧急" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否全天" prop="isAllDay">
              <el-switch v-model="form.isAllDay" active-value="1" inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="参与人员" prop="participants">
          <el-input v-model="form.participants" placeholder="请输入参与人员" />
        </el-form-item>
        <el-form-item label="日程内容" prop="content">
          <el-input v-model="form.content" type="textarea" :rows="4" placeholder="请输入日程内容" />
        </el-form-item>
        <el-form-item label="提醒时间" prop="remindTime">
          <el-date-picker
            v-model="form.remindTime"
            type="datetime"
            placeholder="选择提醒时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%;">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看日程详情对话框 -->
    <el-dialog title="日程详情" :visible.sync="detailOpen" width="500px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="日程标题">{{ scheduleDetail.title }}</el-descriptions-item>
        <el-descriptions-item label="开始时间">{{ parseTime(scheduleDetail.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="结束时间">{{ parseTime(scheduleDetail.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="地点">{{ scheduleDetail.location }}</el-descriptions-item>
        <el-descriptions-item label="重要程度">
          <el-tag :type="scheduleDetail.priority === '3' ? 'danger' : scheduleDetail.priority === '2' ? 'warning' : 'primary'">
            {{ scheduleDetail.priority === '3' ? '紧急' : scheduleDetail.priority === '2' ? '重要' : '普通' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="参与人员">{{ scheduleDetail.participants }}</el-descriptions-item>
        <el-descriptions-item label="日程内容">{{ scheduleDetail.content }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="dict.type.oa_schedule_status" :value="scheduleDetail.status"/>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="editSchedule(scheduleDetail)">编辑</el-button>
        <el-button @click="detailOpen = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSchedule, getSchedule, delSchedule, addSchedule, updateSchedule, getTodaySchedules, getWeekSchedules } from "@/api/oa/schedule";

export default {
  name: "PersonalSchedule",
  dicts: ['oa_schedule_status'],
  data() {
    return {
      // 当前日期
      currentDate: new Date(),
      // 当前视图
      currentView: 'month',
      // 日程列表
      scheduleList: [],
      // 今日日程
      todaySchedules: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 日程详情
      scheduleDetail: {},
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "日程标题不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    filteredSchedules() {
      // 根据当前视图过滤日程
      const now = new Date();
      const today = this.parseTime(now, '{y}-{m}-{d}');

      if (this.currentView === 'day') {
        // 日视图：显示今天的日程
        return this.scheduleList.filter(schedule => {
          const scheduleDate = this.parseTime(schedule.startTime, '{y}-{m}-{d}');
          return scheduleDate === today;
        });
      } else if (this.currentView === 'week') {
        // 周视图：显示本周的日程
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);

        const weekStart = this.parseTime(startOfWeek, '{y}-{m}-{d}');
        const weekEnd = this.parseTime(endOfWeek, '{y}-{m}-{d}');

        return this.scheduleList.filter(schedule => {
          const scheduleDate = this.parseTime(schedule.startTime, '{y}-{m}-{d}');
          return scheduleDate >= weekStart && scheduleDate <= weekEnd;
        });
      }

      // 月视图：显示所有日程（默认）
      return this.scheduleList;
    }
  },
  created() {
    this.getList();
    this.loadTodaySchedules();
  },
  methods: {
    /** 查询日程列表 */
    getList() {
      listSchedule({}).then(response => {
        this.scheduleList = response.rows;
      });
    },
    /** 加载今日日程 */
    loadTodaySchedules() {
      getTodaySchedules().then(response => {
        this.todaySchedules = response.data;
      });
    },
    /** 获取指定日期的日程 */
    getSchedulesForDate(date) {
      const dateStr = this.parseTime(date, '{y}-{m}-{d}');
      return this.scheduleList.filter(schedule => {
        const scheduleDate = this.parseTime(schedule.startTime, '{y}-{m}-{d}');
        return scheduleDate === dateStr;
      });
    },
    /** 切换视图 */
    changeView(view) {
      this.currentView = view;
    },
    /** 新增日程 */
    addSchedule() {
      this.reset();
      this.open = true;
      this.title = "添加日程";
    },
    /** 编辑日程 */
    editSchedule(row) {
      this.reset();
      this.detailOpen = false;
      const scheduleId = row.scheduleId;
      getSchedule(scheduleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改日程";
      });
    },
    /** 查看日程 */
    viewSchedule(row) {
      this.scheduleDetail = row;
      this.detailOpen = true;
    },
    /** 删除日程 */
    deleteSchedule(row) {
      this.$modal.confirm('是否确认删除日程"' + row.title + '"？').then(function() {
        return delSchedule(row.scheduleId);
      }).then(() => {
        this.getList();
        this.loadTodaySchedules();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 提交表单 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.scheduleId != null) {
            updateSchedule(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.loadTodaySchedules();
            });
          } else {
            addSchedule(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.loadTodaySchedules();
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        scheduleId: null,
        title: null,
        content: null,
        startTime: null,
        endTime: null,
        location: null,
        remindTime: null,
        status: "1",
        repeatType: "0",
        priority: "1",
        participants: null,
        isAllDay: "0"
      };
      this.resetForm("form");
    },
    /** 获取今日日程 */
    getTodaySchedules() {
      this.loadTodaySchedules();
    },
    /** 获取本周日程 */
    getWeekSchedules() {
      getWeekSchedules().then(response => {
        this.scheduleList = response.data;
        this.currentView = 'week';
      });
    }
  }
};
</script>

<style scoped>
.calendar-day {
  height: 100px;
  overflow: hidden;
}

.schedule-items {
  margin-top: 5px;
}

.schedule-item {
  font-size: 12px;
  padding: 2px 4px;
  margin: 1px 0;
  border-radius: 2px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.priority-1 {
  background-color: #e1f3d8;
  color: #67c23a;
}

.priority-2 {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.priority-3 {
  background-color: #fef0f0;
  color: #f56c6c;
}

.week-day-view {
  min-height: 400px;
}

.box-card {
  margin-bottom: 20px;
}
</style>
