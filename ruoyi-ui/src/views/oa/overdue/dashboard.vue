<template>
  <div class="overdue-warning-dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="8">
        <el-card class="stats-card overdue-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-warning"></i> 逾期文档</span>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overdueStats.overdueCount }}</div>
            <div class="stats-label">份</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stats-card upcoming-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-time"></i> 即将逾期</span>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ overdueStats.upcomingCount }}</div>
            <div class="stats-label">份</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="stats-card normal-card">
          <div slot="header" class="clearfix">
            <span><i class="el-icon-refresh"></i> 最后更新</span>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ formatTime(lastUpdateTime) }}</div>
            <div class="stats-label">时间</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 操作栏 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="refreshData"
        >刷新数据</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-time"
          size="mini"
          @click="batchUpdateOverdueStatus"
        >批量更新状态</el-button>
      </el-col>
    </el-row>

    <!-- 逾期文档列表 -->
    <el-card>
      <div slot="header" class="clearfix">
        <span>逾期文档列表</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="showAllOverdue">查看全部</el-button>
      </div>
      
      <el-table v-loading="loading" :data="overdueList" style="width: 100%">
        <el-table-column label="文档标题" prop="docTitle" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-link type="primary" @click="viewDocument(scope.row.docId)">{{ scope.row.docTitle }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="文档编号" prop="docNumber" width="150" />
        <el-table-column label="紧急程度" prop="urgencyLevel" width="100">
          <template slot-scope="scope">
            <el-tag :type="getUrgencyType(scope.row.urgencyLevel)" size="mini">
              {{ getUrgencyText(scope.row.urgencyLevel) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="完成期限" prop="completionDeadline" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.completionDeadline, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="逾期天数" prop="overdueDays" width="100">
          <template slot-scope="scope">
            <el-tag type="danger" size="mini">{{ scope.row.overdueDays }}天</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="creatorName" width="100" />
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-time"
              @click="setDeadline(scope.row)"
            >设置期限</el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="markCompleted(scope.row.docId)"
            >标记完成</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 设置期限对话框 -->
    <el-dialog title="设置完成期限" :visible.sync="deadlineDialogVisible" width="500px">
      <el-form ref="deadlineForm" :model="deadlineForm" :rules="deadlineRules" label-width="100px">
        <el-form-item label="文档标题">
          <el-input v-model="deadlineForm.docTitle" readonly></el-input>
        </el-form-item>
        <el-form-item label="紧急程度" prop="urgencyLevel">
          <el-select v-model="deadlineForm.urgencyLevel" placeholder="请选择紧急程度">
            <el-option label="紧急" value="1"></el-option>
            <el-option label="普通" value="2"></el-option>
            <el-option label="缓办" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="完成期限" prop="completionDeadline">
          <el-date-picker
            v-model="deadlineForm.completionDeadline"
            type="datetime"
            placeholder="请选择完成期限"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="deadlineForm.remark" type="textarea" placeholder="请输入备注信息"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deadlineDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveDeadline">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getOverdueStats, getOverdueList, getUpcomingOverdueList, setDocumentDeadline, markDocumentCompleted, batchUpdateOverdueStatus } from "@/api/oa/overdue";

export default {
  name: "OverdueWarningDashboard",
  data() {
    return {
      loading: false,
      overdueStats: {
        overdueCount: 0,
        upcomingCount: 0
      },
      overdueList: [],
      upcomingList: [],
      lastUpdateTime: new Date(),
      deadlineDialogVisible: false,
      deadlineForm: {
        docId: null,
        docTitle: '',
        urgencyLevel: '2',
        completionDeadline: null,
        remark: ''
      },
      deadlineRules: {
        urgencyLevel: [
          { required: true, message: '请选择紧急程度', trigger: 'change' }
        ],
        completionDeadline: [
          { required: true, message: '请选择完成期限', trigger: 'change' }
        ]
      }
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    /** 加载数据 */
    loadData() {
      this.loading = true;
      Promise.all([
        getOverdueStats(),
        getOverdueList({ pageNum: 1, pageSize: 10 }),
        getUpcomingOverdueList({ pageNum: 1, pageSize: 10 })
      ]).then(([statsRes, overdueRes, upcomingRes]) => {
        this.overdueStats = statsRes.data;
        this.overdueList = overdueRes.rows;
        this.upcomingList = upcomingRes.rows;
        this.lastUpdateTime = new Date();
      }).catch(() => {
        // 如果API不可用，使用默认数据
        this.overdueStats = { overdueCount: 0, upcomingCount: 0 };
        this.overdueList = [];
        this.upcomingList = [];
      }).finally(() => {
        this.loading = false;
      });
    },
    
    /** 刷新数据 */
    refreshData() {
      this.loadData();
      this.$message.success('数据已刷新');
    },
    
    /** 批量更新逾期状态 */
    batchUpdateOverdueStatus() {
      this.$modal.confirm('确认要批量更新所有文档的逾期状态吗？').then(() => {
        return batchUpdateOverdueStatus();
      }).then(() => {
        this.$modal.msgSuccess('逾期状态更新成功');
        this.loadData();
      }).catch(() => {});
    },
    
    /** 查看文档 */
    viewDocument(docId) {
      this.$router.push({ path: '/oa/document/view', query: { docId: docId } });
    },
    
    /** 设置期限 */
    setDeadline(row) {
      this.deadlineForm = {
        docId: row.docId,
        docTitle: row.docTitle,
        urgencyLevel: row.urgencyLevel || '2',
        completionDeadline: row.completionDeadline ? this.parseTime(row.completionDeadline, '{y}-{m}-{d} {h}:{i}:{s}') : null,
        remark: ''
      };
      this.deadlineDialogVisible = true;
    },
    
    /** 保存期限设置 */
    saveDeadline() {
      this.$refs.deadlineForm.validate(valid => {
        if (valid) {
          const params = {
            docId: this.deadlineForm.docId,
            completionDeadline: new Date(this.deadlineForm.completionDeadline).getTime(),
            urgencyLevel: this.deadlineForm.urgencyLevel
          };
          
          setDocumentDeadline(params).then(() => {
            this.$modal.msgSuccess('期限设置成功');
            this.deadlineDialogVisible = false;
            this.loadData();
          }).catch(() => {
            this.$modal.msgError('期限设置失败');
          });
        }
      });
    },
    
    /** 标记完成 */
    markCompleted(docId) {
      this.$modal.confirm('确认要标记该文档为完成状态吗？').then(() => {
        return markDocumentCompleted(docId);
      }).then(() => {
        this.$modal.msgSuccess('标记完成成功');
        this.loadData();
      }).catch(() => {});
    },
    
    /** 查看全部逾期 */
    showAllOverdue() {
      this.$router.push({ path: '/oa/overdue/list', query: { type: 'overdue' } });
    },
    
    /** 格式化时间 */
    formatTime(time) {
      return this.parseTime(time, '{h}:{i}');
    },
    
    /** 获取紧急程度类型 */
    getUrgencyType(level) {
      const types = { '1': 'danger', '2': 'warning', '3': 'info' };
      return types[level] || 'info';
    },
    
    /** 获取紧急程度文本 */
    getUrgencyText(level) {
      const texts = { '1': '紧急', '2': '普通', '3': '缓办' };
      return texts[level] || '普通';
    }
  }
};
</script>

<style scoped>
.overdue-warning-dashboard {
  padding: 20px;
}

.stats-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  padding: 20px 0;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.overdue-card .stats-number {
  color: #f56c6c;
}

.upcoming-card .stats-number {
  color: #e6a23c;
}

.normal-card .stats-number {
  color: #67c23a;
  font-size: 16px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>