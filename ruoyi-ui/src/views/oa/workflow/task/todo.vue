<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程名称" prop="workflowName">
        <el-input
          v-model="queryParams.workflowName"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程实例ID" prop="processInstanceId">
        <el-input
          v-model="queryParams.processInstanceId"
          placeholder="请输入流程实例ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="taskList">
      <el-table-column label="任务ID" align="center" prop="taskId" />
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="流程名称" align="center" prop="workflowName" />
      <el-table-column label="当前处理人" align="center" prop="assigneeName" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.assigneeName">{{ scope.row.assigneeName }}</span>
          <span v-else-if="scope.row.assignee">{{ scope.row.assignee }}</span>
          <span v-else style="color: #999;">待分配</span>
        </template>
      </el-table-column>
      <el-table-column label="流程实例ID" align="center" prop="processInstanceId" width="180" />
      <el-table-column label="任务创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleTask(scope.row)"
          >处理</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-share"
            @click="handleDelegate(scope.row)"
          >转办</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务处理对话框 -->
    <el-dialog :title="taskTitle" :visible.sync="taskOpen" width="800px" append-to-body>
      <el-form ref="taskForm" :model="taskForm" label-width="100px">
        <el-form-item label="任务名称">
          <el-input v-model="taskForm.taskName" readonly />
        </el-form-item>
        <el-form-item label="流程名称">
          <el-input v-model="taskForm.workflowName" readonly />
        </el-form-item>
        <el-form-item label="审批意见" prop="comment">
          <el-input v-model="taskForm.comment" type="textarea" :rows="4" placeholder="请输入审批意见" />
        </el-form-item>
        <el-form-item label="审批结果" prop="result">
          <el-radio-group v-model="taskForm.result">
            <el-radio label="agree">同意</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="业务信息" v-if="taskForm.businessInfo">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文档标题">{{ taskForm.businessInfo.docTitle }}</el-descriptions-item>
            <el-descriptions-item label="文档编号">{{ taskForm.businessInfo.docNumber }}</el-descriptions-item>
            <el-descriptions-item label="紧急程度">
              <el-tag v-if="taskForm.businessInfo.urgencyLevel == '1'" type="danger">紧急</el-tag>
              <el-tag v-else-if="taskForm.businessInfo.urgencyLevel == '2'" type="warning">普通</el-tag>
              <el-tag v-else type="info">缓办</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="来文单位">{{ taskForm.businessInfo.sourceUnit }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        <!-- 统一审批意见显示 -->
        <el-form-item label="审批历史" v-if="taskForm.businessInfo && approvalHistory.length > 0">
          <div class="approval-history">
            <div class="approval-header">
              <span>全部审批意见（按时间倒序）</span>
              <el-button size="mini" type="text" @click="toggleApprovalHistory">
                {{ showApprovalHistory ? '收起' : '展开' }}
              </el-button>
            </div>
            <div v-show="showApprovalHistory" class="approval-content">
              <div
                v-for="(item, index) in approvalHistory"
                :key="index"
                class="approval-item"
                :class="{ 'latest': index === 0 }"
              >
                <div class="approval-opinion">{{ item.opinion }}</div>
                <div class="approval-time">{{ item.time }}</div>
                <div class="approval-signature">{{ item.signature }}</div>
                <div v-if="index < approvalHistory.length - 1" class="approval-divider"></div>
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 在线文档编辑功能 -->
        <el-form-item label="文档内容" v-if="taskForm.businessInfo && canEditDocument">
          <div class="document-editor">
            <div class="editor-toolbar">
              <el-button-group>
                <el-button size="mini" icon="el-icon-edit" @click="toggleEdit">
                  {{ isEditing ? '预览' : '编辑' }}
                </el-button>
                <el-button size="mini" icon="el-icon-download" @click="saveDocument" :disabled="!isEditing">
                  保存
                </el-button>
                <el-button size="mini" icon="el-icon-refresh" @click="resetDocument" :disabled="!isEditing">
                  重置
                </el-button>
              </el-button-group>
            </div>
            <div v-if="isEditing" class="editor-content">
              <el-input
                v-model="documentContent"
                type="textarea"
                :rows="15"
                placeholder="请输入文档内容..."
                class="document-textarea"
              />
              <div class="editor-format-toolbar">
                <el-button-group size="mini">
                  <el-button @click="insertText('【', '】')">添加标记</el-button>
                  <el-button @click="insertText('（', '）')">添加括号</el-button>
                  <el-button @click="insertText('\n\n', '')">添加换行</el-button>
                  <el-button @click="insertText('    ', '')">添加缩进</el-button>
                </el-button-group>
              </div>
            </div>
            <div v-else class="preview-content">
              <div class="document-preview" v-html="formattedContent"></div>
            </div>
            <div class="editor-info">
              <el-alert
                :title="'修改记录：' + (documentModifyInfo || '无修改')"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTask">提 交</el-button>
        <el-button @click="cancelTask">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 转办对话框 -->
    <el-dialog title="任务转办" :visible.sync="delegateOpen" width="500px" append-to-body>
      <el-form ref="delegateForm" :model="delegateForm" :rules="delegateRules" label-width="100px">
        <el-form-item label="转办给" prop="assignee">
          <el-select
            v-model="delegateForm.assignee"
            placeholder="请输入姓名或用户名搜索"
            style="width: 100%"
            filterable
            :filter-method="filterUsers"
            :loading="loadingUsers">
            <el-option
              v-for="user in filteredUserList"
              :key="user.userId"
              :label="`${user.nickName}(${user.userName})`"
              :value="user.userName">
              <span style="float: left">{{ user.nickName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ user.userName }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="转办说明">
          <el-input
            v-model="delegateForm.comment"
            type="textarea"
            :rows="3"
            placeholder="请输入转办说明（可选）"
            maxlength="200"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDelegate">确 定</el-button>
        <el-button @click="cancelDelegate">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTodoTasks, completeTask, delegateTask, getTaskDetail } from "@/api/oa/workflow";
import { listUser, listAllActiveUsers } from "@/api/system/user";
import { getReceiveDocument, getSendDocument } from "@/api/oa/document";

export default {
  name: "TodoTask",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 待办任务表格数据
      taskList: [],
      // 用户列表
      userList: [],
      // 过滤后的用户列表
      filteredUserList: [],
      // 加载用户状态
      loadingUsers: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        workflowName: null,
        processInstanceId: null
      },
      // 任务处理表单
      taskForm: {},
      // 任务处理对话框
      taskOpen: false,
      taskTitle: "",
      // 转办表单
      delegateForm: {},
      // 转办对话框
      delegateOpen: false,
      // 转办表单验证规则
      delegateRules: {
        assignee: [
          { required: true, message: '请选择转办人', trigger: 'change' }
        ]
      },
      // 在线编辑相关
      isEditing: false,
      documentContent: '',
      originalDocumentContent: '',
      documentModifyInfo: '',
      canEditDocument: false,
      // 审批历史相关
      approvalHistory: [],
      showApprovalHistory: false
    };
  },
  computed: {
    /** 格式化的文档内容用于预览 */
    formattedContent() {
      if (!this.documentContent) return '';
      return this.documentContent
        .replace(/\n/g, '<br>')
        .replace(/    /g, '&nbsp;&nbsp;&nbsp;&nbsp;')
        .replace(/【([^】]+)】/g, '<strong>【$1】</strong>')
        .replace(/（([^）]+)）/g, '<em>（$1）</em>');
    }
  },
  created() {
    this.getList();
    this.getUserList();
  },
  methods: {
    /** 查询待办任务列表 */
    getList() {
      this.loading = true;
      getTodoTasks(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询用户列表 */
    getUserList() {
      console.log('开始获取用户列表...');
      this.loadingUsers = true;
      // 使用新的API获取所有启用用户，不受数据权限限制
      return listAllActiveUsers().then(response => {
        console.log('用户列表API响应:', response);
        if (response && response.data) {
          this.userList = response.data;
          this.filteredUserList = this.userList; // 初始化过滤列表
          console.log('用户列表设置成功，数量:', this.userList.length);
          console.log('用户列表前3个:', this.userList.slice(0, 3));
        } else {
          console.warn('用户列表响应格式异常:', response);
          this.userList = [];
          this.filteredUserList = [];
        }
        return this.userList;
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.$modal.msgError("获取用户列表失败: " + (error.message || error));
        this.userList = [];
        this.filteredUserList = [];
        return [];
      }).finally(() => {
        this.loadingUsers = false;
      });
    },

    /** 过滤用户列表 */
    filterUsers(query) {
      if (!query) {
        this.filteredUserList = this.userList;
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredUserList = this.userList.filter(user => {
        return user.nickName.toLowerCase().includes(lowerQuery) ||
               user.userName.toLowerCase().includes(lowerQuery);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 处理任务 */
    handleTask(row) {
      console.log('处理任务:', row);

      // 统一跳转到通用审批页面，能看到任务说明用户就是当前处理人
      this.$router.push({
        path: '/oa/workflow/task/approval',
        query: {
          taskId: row.flowableTaskId || row.taskId,
          taskName: row.taskName,
          workflowName: row.workflowName,
          workflowKey: row.workflowKey,
          processInstanceId: row.processInstanceId,
          businessKey: row.businessKey
        }
      });
    },

    /** 加载业务信息 */
    loadBusinessInfo(row) {
      // 根据流程实例ID获取业务信息
      if (row.businessKey) {
        const businessKey = row.businessKey;

        // 从业务主键中解析文档ID和类型
        const docInfo = this.parseBusinessKey(businessKey);
        if (docInfo) {
          if (docInfo.type === 'receive') {
            // 收文流程
            getReceiveDocument(docInfo.docId).then(response => {
              this.taskForm.businessInfo = response.data;
              this.taskForm.businessInfo.documentType = 'receive';
            }).catch(() => {
              this.taskForm.businessInfo = null;
            });
          } else if (docInfo.type === 'send') {
            // 发文流程
            getSendDocument(docInfo.docId).then(response => {
              this.taskForm.businessInfo = response.data;
              this.taskForm.businessInfo.documentType = 'send';
            }).catch(() => {
              this.taskForm.businessInfo = null;
            });
          }
        }
      }
    },

    /** 解析业务主键 */
    parseBusinessKey(businessKey) {
      if (!businessKey) return null;

      try {
        // 后端生成的业务键格式：doc_receive_、doc_receive_special_、doc_send_
        if (businessKey.startsWith('doc_receive_')) {
          // 收文业务主键格式：doc_receive_{docId} 或 doc_receive_special_{docId}
          let docIdStr = businessKey.replace('doc_receive_', '').replace('special_', '');
          return {
            type: 'receive',
            docId: parseInt(docIdStr),
            isSpecial: businessKey.includes('special')
          };
        } else if (businessKey.startsWith('doc_send_')) {
          // 发文业务主键格式：doc_send_{docId}
          let docIdStr = businessKey.replace('doc_send_', '');
          return {
            type: 'send',
            docId: parseInt(docIdStr)
          };
        }
      } catch (error) {
        console.error('解析业务主键失败:', businessKey, error);
      }

      return null;
    },

    /** 转办任务 */
    handleDelegate(row) {
      console.log('转办任务:', row);
      console.log('当前用户列表长度:', this.userList.length);

      this.delegateForm = {
        taskId: row.flowableTaskId || row.taskId,
        assignee: ""
      };

      // 强制重新加载用户列表
      console.log('重新加载用户列表...');
      this.getUserList().then(() => {
        console.log('用户列表加载完成，长度:', this.userList.length);
        this.delegateOpen = true;
      });
    },
    /** 提交任务处理 */
    submitTask() {
      this.$refs["taskForm"].validate(valid => {
        if (valid) {
          completeTask(this.taskForm.taskId, this.taskForm).then(response => {
            this.$modal.msgSuccess("任务处理成功");
            this.taskOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消任务处理 */
    cancelTask() {
      this.taskOpen = false;
      this.taskForm = {};
    },
    /** 提交转办 */
    submitDelegate() {
      this.$refs["delegateForm"].validate(valid => {
        if (valid) {
          delegateTask(this.delegateForm.taskId, this.delegateForm).then(response => {
            this.$modal.msgSuccess("任务转办成功");
            this.delegateOpen = false;
            this.getList();
          });
        }
      });
    },
    /** 取消转办 */
    cancelDelegate() {
      this.delegateOpen = false;
      this.delegateForm = {};
    },

    // ==================== 在线编辑功能 ====================

    /** 切换编辑模式 */
    toggleEdit() {
      this.isEditing = !this.isEditing;
      if (this.isEditing && !this.documentContent) {
        this.documentContent = this.taskForm.businessInfo.content || '';
        this.originalDocumentContent = this.documentContent;
      }
    },

    /** 保存文档 */
    saveDocument() {
      if (this.documentContent !== this.originalDocumentContent) {
        // 更新业务信息中的文档内容
        this.taskForm.businessInfo.content = this.documentContent;

        // 记录修改信息
        const currentUser = this.$store.state.user.name || '当前用户';
        const currentTime = new Date().toLocaleString();
        this.documentModifyInfo = `${currentUser} 于 ${currentTime} 修改`;

        // 这里可以调用API保存文档内容
        // updateDocumentContent(this.taskForm.businessInfo.docId, {
        //   content: this.documentContent,
        //   modifier: currentUser,
        //   modifyTime: new Date()
        // });

        this.$message.success('文档内容已保存');
        this.originalDocumentContent = this.documentContent;
      } else {
        this.$message.info('文档内容未发生变化');
      }
    },

    /** 重置文档 */
    resetDocument() {
      this.documentContent = this.originalDocumentContent;
      this.$message.info('文档内容已重置');
    },

    /** 插入文本 */
    insertText(prefix, suffix = '') {
      const textarea = this.$el.querySelector('.document-textarea textarea');
      if (textarea) {
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;
        const selectedText = this.documentContent.substring(start, end);

        const newText = prefix + selectedText + suffix;
        this.documentContent = this.documentContent.substring(0, start) + newText + this.documentContent.substring(end);

        // 重新设置光标位置
        this.$nextTick(() => {
          textarea.focus();
          textarea.setSelectionRange(start + prefix.length, start + prefix.length + selectedText.length);
        });
      }
    },

    /** 检查是否可以编辑文档 */
    checkCanEditDocument(taskInfo) {
      // 根据任务类型和用户权限判断是否可以编辑
      // 这里可以根据实际需求添加更复杂的权限判断逻辑
      const editableTaskTypes = ['拟稿', '核稿', '签发', '审批'];
      return editableTaskTypes.includes(taskInfo.taskName) || taskInfo.taskName.includes('审批');
    },

    // ==================== 审批历史功能 ====================

    /** 加载审批历史 */
    loadApprovalHistory(taskInfo) {
      // 这里应该调用API获取审批历史数据
      // 暂时使用示例数据
      this.approvalHistory = [
        {
          opinion: '同意按照相关规定办理',
          time: '2025年01月08日 14:30',
          signature: '张三 [电子签名]'
        },
        {
          opinion: '建议加强监管措施，同意通过',
          time: '2025年01月08日 11:20',
          signature: '李四 [电子签名]'
        },
        {
          opinion: '经审核，符合相关要求',
          time: '2025年01月08日 10:15',
          signature: '王五 [电子签名]'
        },
        {
          opinion: '收文登记完成，提交审批',
          time: '2025年01月08日 09:00',
          signature: '赵六 [电子签名]'
        }
      ];
    },

    /** 切换审批历史显示 */
    toggleApprovalHistory() {
      this.showApprovalHistory = !this.showApprovalHistory;
    }
  }
};
</script>

<style scoped>
.document-editor {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
  background-color: #fafafa;
}

.editor-toolbar {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.editor-content {
  margin-bottom: 10px;
}

.document-textarea {
  width: 100%;
}

.document-textarea textarea {
  font-family: 'SimSun', serif;
  font-size: 14px;
  line-height: 1.6;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  resize: vertical;
}

.editor-format-toolbar {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e4e7ed;
}

.preview-content {
  margin-bottom: 10px;
  max-height: 400px;
  overflow-y: auto;
}

.document-preview {
  font-family: 'SimSun', serif;
  font-size: 14px;
  line-height: 1.8;
  padding: 15px;
  background-color: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
  min-height: 300px;
}

.document-preview strong {
  color: #409eff;
  font-weight: bold;
}

.document-preview em {
  color: #67c23a;
  font-style: normal;
}

.editor-info {
  margin-top: 10px;
}

.editor-info .el-alert {
  background-color: #f4f4f5;
  border: 1px solid #e9e9eb;
}

/* 审批历史样式 */
.approval-history {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.approval-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: bold;
}

.approval-content {
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.approval-item {
  margin-bottom: 15px;
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.approval-item.latest {
  border-color: #409eff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
}

.approval-opinion {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin-bottom: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 3px;
}

.approval-time {
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.approval-signature {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.approval-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0;
}
</style>
