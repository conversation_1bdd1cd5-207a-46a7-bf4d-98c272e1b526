<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="approval-header">
      <h2>{{ taskInfo.taskName }} - {{ taskInfo.workflowName }}</h2>
      <div class="task-info">
        <el-tag type="primary">{{ taskInfo.taskName }}</el-tag>
        <el-tag type="info">流程实例ID: {{ taskInfo.processInstanceId }}</el-tag>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 左侧：公文详情 -->
      <el-col :span="16">
        <el-card shadow="never" class="document-detail">
          <div slot="header" class="clearfix">
            <span>公文详情</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="showFullDocument">
              查看完整文档
            </el-button>
          </div>

          <!-- 文档基本信息 -->
          <div v-if="documentInfo" class="document-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="公文标题">
                <span style="font-weight: bold; color: #303133;">{{ documentInfo.docTitle || documentInfo.title }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="文档编号">{{ documentInfo.docNumber || documentInfo.number }}</el-descriptions-item>
              <el-descriptions-item label="来源单位">
                <span style="color: #409EFF;">{{ documentInfo.sourceUnit || documentInfo.source || '无' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="紧急程度">
                <dict-tag :options="dict.type.oa_urgency_level" :value="documentInfo.urgencyLevel || documentInfo.urgency"/>
              </el-descriptions-item>
              <el-descriptions-item label="密级">
                <dict-tag :options="dict.type.oa_security_level" :value="documentInfo.securityLevel || documentInfo.secretLevel"/>
              </el-descriptions-item>
              <el-descriptions-item label="发文机关" v-if="documentInfo.issuingOrgan || documentInfo.issuer">
                {{ documentInfo.issuingOrgan || documentInfo.issuer }}
              </el-descriptions-item>
              <el-descriptions-item label="收文日期" v-if="documentInfo.receiveDate">
                {{ parseTime(documentInfo.receiveDate, '{y}-{m}-{d}') }}
              </el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ parseTime(documentInfo.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <dict-tag :options="statusOptions" :value="documentInfo.status"/>
              </el-descriptions-item>
              <el-descriptions-item label="处理人" v-if="documentInfo.handlerName">
                {{ documentInfo.handlerName }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 文档内容预览 -->
            <div class="document-content" v-if="documentInfo && documentInfo.docContent">
              <h4>文档内容</h4>
              <div class="content-preview" v-html="documentInfo.docContent" style="max-height: 300px; overflow-y: auto; border: 1px solid #e4e7ed; padding: 10px; background-color: #fafafa;"></div>
            </div>

            <!-- 如果没有文档内容，显示提示 -->
            <div v-else-if="documentInfo && !documentInfo.docContent" class="no-content">
              <h4>文档内容</h4>
              <div style="color: #909399; text-align: center; padding: 20px;">暂无文档内容</div>
            </div>

            <!-- 附件列表 -->
            <div class="attachments" v-if="documentInfo.attachments && documentInfo.attachments.length > 0">
              <h4>附件</h4>
              <el-table :data="documentInfo.attachments" size="small">
                <el-table-column prop="fileName" label="文件名" />
                <el-table-column label="大小" width="100">
                  <template slot-scope="scope">
                    <span>{{ formatFileSize(scope.row.fileSize) }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button size="mini" type="text" @click="downloadAttachment(scope.row)">
                      下载
                    </el-button>
                    <el-button size="mini" type="text" @click="previewAttachment(scope.row)">
                      预览
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：审批操作 -->
      <el-col :span="8">
        <!-- 审批操作区 -->
        <el-card shadow="never" class="approval-actions">
          <div slot="header" class="clearfix">
            <span>审批操作</span>
          </div>

          <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="80px">
            <el-form-item label="审批结果" prop="result">
              <el-radio-group v-model="approvalForm.result">
                <el-radio label="agree">同意</el-radio>
                <el-radio label="reject">拒绝</el-radio>
                <el-radio label="return">退回</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="审批意见" prop="comment">
              <el-input
                v-model="approvalForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入审批意见"
              />
            </el-form-item>

            <!-- 退回说明 -->
            <el-form-item v-if="approvalForm.result === 'return'">
              <el-alert
                title="退回说明"
                description="选择退回将自动退回到上一个审批节点"
                type="info"
                :closable="false"
                show-icon>
              </el-alert>
            </el-form-item>

            <!-- 书记审批时的分管领导选择（收文审批流程） -->
            <el-form-item label="指派分管" v-if="approvalForm.result === 'agree' && isSecretaryApproval" prop="selectedLeaders">
              <el-select
                v-model="approvalForm.selectedLeaders"
                placeholder="请选择需要审批的分管领导"
                style="width: 100%"
                multiple
                clearable
                :loading="loadingLeaders">
                <el-option
                  v-for="leader in departmentLeaders"
                  :key="leader.userName"
                  :label="`${leader.nickName}(${leader.userName})`"
                  :value="leader.userName">
                  <span style="float: left">{{ leader.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ leader.userName }}</span>
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                <i class="el-icon-info"></i> 请选择一个或多个分管领导进行后续审批，未选择的分管领导将跳过审批
              </div>
            </el-form-item>

            <!-- 办公室负责人审核时的分管领导选择（发文审批流程） -->
            <el-form-item label="指派分管" v-if="approvalForm.result === 'agree' && isOfficeManagerReview" prop="selectedLeaders">
              <el-select
                v-model="approvalForm.selectedLeaders"
                placeholder="请选择需要审批的分管领导"
                style="width: 100%"
                multiple
                clearable
                :loading="loadingLeaders">
                <el-option
                  v-for="leader in departmentLeaders"
                  :key="leader.userName"
                  :label="`${leader.nickName}(${leader.userName})`"
                  :value="leader.userName">
                  <span style="float: left">{{ leader.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ leader.userName }}</span>
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                <i class="el-icon-info"></i> 请选择一个或多个分管领导进行后续审批，未选择的分管领导将跳过审批
              </div>
            </el-form-item>

            <!-- 分管领导审批时的科室负责人选择（收文审批流程） -->
            <el-form-item label="指派科室负责人" v-if="approvalForm.result === 'agree' && isLeaderApproval" prop="selectedManagers">
              <el-select
                v-model="approvalForm.selectedManagers"
                placeholder="请选择需要审批的科室负责人"
                style="width: 100%"
                multiple
                clearable
                :loading="loadingManagers">
                <el-option
                  v-for="manager in departmentManagers"
                  :key="manager.userName"
                  :label="`${manager.nickName}(${manager.userName}) - ${manager.deptName}`"
                  :value="manager.userName">
                  <span style="float: left">{{ manager.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ manager.deptName }}</span>
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                <i class="el-icon-info"></i> 请选择一个或多个科室负责人进行后续审批，未选择的科室负责人将跳过审批
              </div>
            </el-form-item>

            <!-- 科室负责人审批时的经办人选择（收文审批流程） -->
            <el-form-item label="指派经办人" v-if="approvalForm.result === 'agree' && isManagerApproval" prop="selectedHandlers">
              <el-select
                v-model="approvalForm.selectedHandlers"
                placeholder="请选择需要查阅和确认的经办人"
                style="width: 100%"
                multiple
                clearable
                :loading="loadingHandlers">
                <el-option
                  v-for="handler in departmentHandlers"
                  :key="handler.userName"
                  :label="`${handler.nickName}(${handler.userName}) - ${handler.deptName}`"
                  :value="handler.userName">
                  <span style="float: left">{{ handler.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ handler.deptName }}</span>
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                <i class="el-icon-info"></i> 请选择一个或多个经办人进行文档查阅和确认，未选择的经办人将跳过处理
              </div>
            </el-form-item>

            <!-- 书记收文时的任意人员选择（收文特批流程） -->
            <el-form-item label="指派人员" v-if="approvalForm.result === 'agree' && isSecretaryReceive" prop="nextAssignee">
              <el-select
                v-model="approvalForm.nextAssignee"
                placeholder="请选择处理人员"
                style="width: 100%"
                clearable
                filterable
                :filter-method="filterDepartmentLeaders"
                :loading="loadingLeaders">
                <el-option
                  v-for="leader in filteredDepartmentLeaders"
                  :key="leader.userName"
                  :label="`${leader.nickName}(${leader.userName})`"
                  :value="leader.userName">
                  <span style="float: left">{{ leader.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ leader.userName }}</span>
                </el-option>
              </el-select>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                <i class="el-icon-info"></i> 请选择一个人员进行后续处理
              </div>
            </el-form-item>

            <!-- 下一个处理人选择（普通流程节点） -->
            <el-form-item label="下步审批" v-if="approvalForm.result === 'agree' && nextAssignees.length > 0 && !isSecretaryApproval && !isSecretaryReceive">
              <el-select
                v-model="approvalForm.nextAssignee"
                placeholder="请输入姓名或用户名搜索"
                style="width: 100%"
                clearable
                filterable
                :filter-method="filterNextAssignees"
                :loading="loadingNextAssignees">
                <el-option
                  v-for="assignee in filteredNextAssignees"
                  :key="assignee.userId"
                  :label="`${assignee.nickName}(${assignee.userName})`"
                  :value="assignee.userName">
                  <span style="float: left">{{ assignee.nickName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ assignee.userName }}</span>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="submitApproval" :loading="submitting">
                提交审批
              </el-button>
              <el-button type="success" @click="editDocument" icon="el-icon-edit">
                编辑文档
              </el-button>
              <el-button @click="cancelApproval">取消</el-button>
            </el-form-item>
          </el-form>
        </el-card>



        <!-- 审批历史 -->
        <el-card shadow="never" class="approval-history" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span>审批历史</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="loadApprovalHistory">
              刷新
            </el-button>
          </div>

          <el-timeline v-if="approvalHistory.length > 0">
            <el-timeline-item
              v-for="(item, index) in approvalHistory"
              :key="index"
              :timestamp="parseTime(item.endTime, '{y}-{m}-{d} {h}:{i}:{s}')"
              placement="top"
            >
              <el-card>
                <h4>{{ item.taskName }}</h4>
                <p><strong>处理人：</strong>{{ item.assignee }}</p>
                <p v-if="item.comment"><strong>审批意见：</strong>{{ item.comment }}</p>
                <p><strong>处理时间：</strong>{{ parseTime(item.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>

          <div v-else class="no-history">
            <el-empty description="暂无审批历史" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getReceiveDocument, getSendDocument } from "@/api/oa/document";
import { completeTask, getProcessApprovalInfo, getReturnNodes, getNextAssignees, getAvailableLeaders, getAvailableManagers, getAvailableHandlers } from "@/api/oa/workflow";
import { listUser, listAllActiveUsers } from "@/api/system/user";
import { getInfo } from "@/api/login";

export default {
  name: "TaskApproval",
  dicts: ['oa_urgency_level', 'oa_security_level'],
  data() {
    return {
      // 任务信息
      taskInfo: {
        taskId: '',
        taskName: '',
        workflowName: '',
        workflowKey: '',
        processInstanceId: '',
        businessKey: ''
      },
      // 文档信息
      documentInfo: null,
      documentType: '', // 'receive' 或 'send'
      // 审批表单
      approvalForm: {
        result: 'agree',
        comment: '',
        returnTo: '',
        nextAssignee: '',
        selectedLeaders: [],
        selectedManagers: [],
        selectedHandlers: [],
        selectedHandlers: []
      },
      // 下一步处理人列表
      nextAssignees: [],
      // 过滤后的下一步处理人列表
      filteredNextAssignees: [],
      // 加载下一步处理人状态
      loadingNextAssignees: false,
      // 表单验证规则
      approvalRules: {
        result: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        comment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ],
        selectedLeaders: [
          {
            validator: (rule, value, callback) => {
              if ((this.isSecretaryApproval || this.isOfficeManagerReview) && this.approvalForm.result === 'agree') {
                if (!value || value.length === 0) {
                  const taskType = this.isSecretaryApproval ? '书记审批' : '办公室负责人审核';
                  callback(new Error(`${taskType}时请至少选择一个分管领导`));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        selectedManagers: [
          {
            validator: (rule, value, callback) => {
              if (this.isLeaderApproval && this.approvalForm.result === 'agree') {
                if (!value || value.length === 0) {
                  callback(new Error('分管领导审批时请至少选择一个科室负责人'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        selectedHandlers: [
          {
            validator: (rule, value, callback) => {
              if (this.isManagerApproval && this.approvalForm.result === 'agree') {
                if (!value || value.length === 0) {
                  callback(new Error('科室负责人审批时请至少选择一个经办人'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        nextAssignee: [
          {
            validator: (rule, value, callback) => {
              if (this.isSecretaryReceive && this.approvalForm.result === 'agree') {
                if (!value) {
                  callback(new Error('书记收文时请选择一个处理人员'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      },
      // 审批历史
      approvalHistory: [],
      // 可退回的节点
      returnNodes: [],
      // 状态选项
      statusOptions: [
        { label: '草稿', value: '0' },
        { label: '审批中', value: '1' },
        { label: '已审批', value: '2' },
        { label: '已归档', value: '3' }
      ],
      // 提交状态
      submitting: false,
      // 分管领导列表
      departmentLeaders: [],
      // 过滤后的分管领导列表
      filteredDepartmentLeaders: [],
      // 加载分管领导状态
      loadingLeaders: false,
      // 科室负责人列表
      departmentManagers: [],
      // 加载科室负责人状态
      loadingManagers: false,
      // 经办人列表
      departmentHandlers: [],
      // 加载经办人状态
      loadingHandlers: false,
      // 是否为书记审批
      isSecretaryApproval: false,
      // 是否为书记收文
      isSecretaryReceive: false,
      // 是否为办公室负责人审核
      isOfficeManagerReview: false,
      // 是否为分管领导审批
      isLeaderApproval: false,
      // 是否为科室负责人审批
      isManagerApproval: false
    };
  },
  created() {
    this.initPage();
  },
  methods: {
    /** 初始化页面 */
    initPage() {
      // 从路由参数获取任务信息
      const { taskId, taskName, workflowName, workflowKey, processInstanceId, businessKey, viewMode } = this.$route.query;

      this.taskInfo = {
        taskId,
        taskName,
        workflowName,
        workflowKey,
        processInstanceId,
        businessKey
      };

      console.log('审批页面初始化:', {
        taskId,
        taskName,
        workflowName,
        processInstanceId,
        businessKey
      });

      // 检查是否为书记审批（收文审批流程）
      // 只有收文流程中的书记审批才需要选择分管领导
      this.isSecretaryApproval = taskName === '书记审批' && (workflowKey === 'document_receive_approval_v2');

      // 检查是否为书记收文（收文特批流程）
      this.isSecretaryReceive = taskName === '书记收文';

      // 检查是否为办公室负责人审核（发文审批流程）
      this.isOfficeManagerReview = taskName === '办公室负责人审核';

      // 检查是否为分管领导审批（收文审批流程）
      // 只有收文流程中的分管领导审批才需要选择科室负责人
      this.isLeaderApproval = taskName === '分管领导审批' && (workflowKey === 'document_receive_approval_v2');

      // 检查是否为科室负责人审批（收文审批流程）
      // 只有收文流程中的科室负责人审批才需要选择经办人
      this.isManagerApproval = taskName === '科室负责人审批' && (workflowKey === 'document_receive_approval_v2');

      // 加载文档详情
      this.loadDocumentInfo();
      // 加载审批历史
      this.loadApprovalHistory();

      // 加载审批相关数据
      this.loadReturnNodes();
      this.loadNextAssignees();
      // 如果是书记审批（收文流程）或办公室负责人审核，加载分管领导列表
      if (this.isSecretaryApproval || this.isOfficeManagerReview) {
        this.loadDepartmentLeaders();
      }
      // 如果是书记收文，也需要加载人员列表用于任意人员选择
      if (this.isSecretaryReceive) {
        this.loadDepartmentLeaders(); // 复用分管领导加载方法
      }
      // 如果是分管领导审批，加载当前分管领导下的科室负责人列表
      if (this.isLeaderApproval) {
        this.loadDepartmentManagers();
      }
      // 如果是科室负责人审批，加载当前科室负责人下的经办人列表
      if (this.isManagerApproval) {
        this.loadDepartmentHandlers();
      }
    },

    /** 加载文档信息 */
    loadDocumentInfo() {
      console.log('开始加载文档信息，业务主键:', this.taskInfo.businessKey);

      if (!this.taskInfo.businessKey) {
        console.warn('业务主键为空，无法加载文档信息');
        return;
      }

      const docInfo = this.parseBusinessKey(this.taskInfo.businessKey);
      if (docInfo) {
        this.documentType = docInfo.type;
        console.log('文档类型:', this.documentType, '文档ID:', docInfo.docId);

        if (docInfo.type === 'receive') {
          console.log('加载收文文档:', docInfo.docId);
          getReceiveDocument(docInfo.docId).then(response => {
            console.log('收文文档加载成功:', response.data);
            this.documentInfo = response.data;

            // 处理文档内容显示
            this.processDocumentContent();

            // 解析附件信息
            if (response.data.attachments) {
              try {
                this.documentInfo.attachments = JSON.parse(response.data.attachments);
              } catch (e) {
                this.documentInfo.attachments = [];
              }
            }
          }).catch(error => {
            console.error('加载收文文档失败:', error);
            this.$modal.msgError('加载文档信息失败: ' + (error.message || error));
          });
        } else if (docInfo.type === 'send') {
          console.log('加载发文文档:', docInfo.docId);
          getSendDocument(docInfo.docId).then(response => {
            console.log('发文文档加载成功:', response.data);
            this.documentInfo = response.data;

            // 处理文档内容显示
            this.processDocumentContent();

            // 解析附件信息
            if (response.data.attachments) {
              try {
                this.documentInfo.attachments = JSON.parse(response.data.attachments);
              } catch (e) {
                this.documentInfo.attachments = [];
              }
            }
          }).catch(error => {
            console.error('加载发文文档失败:', error);
            this.$modal.msgError('加载文档信息失败: ' + (error.message || error));
          });
        }
      } else {
        console.error('解析业务主键失败，无法确定文档类型');
        this.$modal.msgError('业务键格式错误，无法加载文档信息');
      }
    },

    /** 解析业务主键 */
    parseBusinessKey(businessKey) {
      console.log('解析业务主键:', businessKey);

      if (!businessKey) {
        console.warn('业务主键为空');
        return null;
      }

      try {
        // 支持多种业务键格式

        // 新格式：receive_doc_xxx, send_doc_xxx
        if (businessKey.startsWith('receive_doc_')) {
          let docIdStr = businessKey.replace('receive_doc_', '');
          const docId = parseInt(docIdStr);
          console.log('解析收文业务主键成功（新格式）:', { type: 'receive', docId });
          return {
            type: 'receive',
            docId: docId,
            isSpecial: false // 新格式暂时不区分特批
          };
        } else if (businessKey.startsWith('send_doc_')) {
          let docIdStr = businessKey.replace('send_doc_', '');
          const docId = parseInt(docIdStr);
          console.log('解析发文业务主键成功（新格式）:', { type: 'send', docId });
          return {
            type: 'send',
            docId: docId
          };
        }
        // 旧格式：doc_receive_、doc_receive_special_、doc_send_
        else if (businessKey.startsWith('doc_receive_')) {
          let docIdStr = businessKey.replace('doc_receive_', '').replace('special_', '');
          const docId = parseInt(docIdStr);
          console.log('解析收文业务主键成功（旧格式）:', { type: 'receive', docId, isSpecial: businessKey.includes('special') });
          return {
            type: 'receive',
            docId: docId,
            isSpecial: businessKey.includes('special')
          };
        } else if (businessKey.startsWith('doc_send_')) {
          let docIdStr = businessKey.replace('doc_send_', '');
          const docId = parseInt(docIdStr);
          console.log('解析发文业务主键成功（旧格式）:', { type: 'send', docId });
          return {
            type: 'send',
            docId: docId
          };
        } else {
          console.warn('不支持的业务主键格式:', businessKey);
        }
      } catch (error) {
        console.error('解析业务主键失败:', businessKey, error);
      }

      return null;
    },

    /** 加载审批历史 */
    loadApprovalHistory() {
      if (!this.taskInfo.processInstanceId) {
        console.log('流程实例ID为空，跳过加载审批历史');
        return;
      }

      console.log('开始加载审批历史，流程实例ID:', this.taskInfo.processInstanceId);
      getProcessApprovalInfo(this.taskInfo.processInstanceId).then(response => {
        console.log('审批历史API响应:', response);
        if (response && response.data) {
          this.approvalHistory = response.data.approvalHistory || response.data.completedTasks || [];
          console.log('审批历史加载成功，数量:', this.approvalHistory.length);
        } else {
          console.warn('审批历史响应格式异常:', response);
          this.approvalHistory = [];
        }
      }).catch(error => {
        console.error('加载审批历史失败:', error);
        this.approvalHistory = [];
      });
    },

    /** 加载可退回节点 */
    loadReturnNodes() {
      if (!this.taskInfo.taskId) return;

      getReturnNodes(this.taskInfo.taskId).then(response => {
        this.returnNodes = response.data || [];
      }).catch(error => {
        console.error('加载可退回节点失败:', error);
        this.returnNodes = [];
      });
    },

    /** 处理文档内容显示 */
    processDocumentContent() {
      if (this.documentInfo && this.documentInfo.docContent) {
        // 如果docContent是JSON格式，尝试解析
        try {
          if (typeof this.documentInfo.docContent === 'string' &&
              this.documentInfo.docContent.startsWith('{')) {
            const contentObj = JSON.parse(this.documentInfo.docContent);
            this.documentInfo.docContent = contentObj.content || this.documentInfo.docContent;
          }
        } catch (e) {
          // 如果解析失败，保持原内容
          console.log('文档内容不是JSON格式，保持原内容');
        }
      }
    },

    /** 提交审批 */
    submitApproval() {
      this.$refs.approvalForm.validate(valid => {
        if (valid) {
          this.submitting = true;

          const params = {
            result: this.approvalForm.result,
            comment: this.approvalForm.comment,
            variables: {
              // 简化：统一使用 approvalResult 变量
              approvalResult: this.approvalForm.result === 'agree' ? 'approve' :
                             this.approvalForm.result === 'reject' ? 'reject' : 'return'
            }
          };

          // 退回操作不需要指定节点，自动退回到上一个节点

          if (this.approvalForm.result === 'agree' && this.approvalForm.nextAssignee) {
            params.variables.nextAssignee = this.approvalForm.nextAssignee;
          }

          // 如果是书记审批或办公室负责人审核且选择了分管领导，传递选中的分管领导列表
          if ((this.isSecretaryApproval || this.isOfficeManagerReview) && this.approvalForm.result === 'agree' && this.approvalForm.selectedLeaders.length > 0) {
            params.variables.selectedLeaders = this.approvalForm.selectedLeaders.join(',');
          }

          // 如果是分管领导审批且选择了科室负责人，传递选中的科室负责人列表
          if (this.isLeaderApproval && this.approvalForm.result === 'agree' && this.approvalForm.selectedManagers.length > 0) {
            params.variables.selectedManagers = this.approvalForm.selectedManagers.join(',');
          }

          // 如果是科室负责人审批且选择了经办人，传递选中的经办人列表
          if (this.isManagerApproval && this.approvalForm.result === 'agree' && this.approvalForm.selectedHandlers.length > 0) {
            params.variables.selectedHandlers = this.approvalForm.selectedHandlers.join(',');
          }

          console.log('提交审批参数:', params);

          completeTask(this.taskInfo.taskId, params).then(response => {
            console.log('审批提交响应:', response);
            this.$modal.msgSuccess("审批提交成功");

            // 刷新审批历史以显示刚提交的审批记录
            setTimeout(() => {
              this.loadApprovalHistory();
            }, 1000);

            // 延迟跳转，让用户看到更新的审批历史
            setTimeout(() => {
              this.$router.push('/oa/workflow/my');
            }, 2000);
          }).catch(error => {
            console.error('审批提交失败:', error);
            const errorMsg = error.message || error;

            // 如果是任务不存在或已完成的错误，提示用户刷新
            if (errorMsg.includes('任务不存在') || errorMsg.includes('任务已完成') || errorMsg.includes('请刷新页面')) {
              this.$modal.confirm('任务状态已发生变化，是否返回任务列表刷新？', '提示', {
                confirmButtonText: '返回刷新',
                cancelButtonText: '留在当前页',
                type: 'warning'
              }).then(() => {
                this.$router.push('/oa/workflow/my');
              }).catch(() => {
                // 用户选择留在当前页，不做任何操作
              });
            } else {
              this.$modal.msgError("审批提交失败: " + errorMsg);
            }
          }).finally(() => {
            this.submitting = false;
          });
        }
      });
    },

    /** 取消审批 */
    cancelApproval() {
      this.$router.push('/oa/workflow/my');
    },

    /** 编辑文档 */
    editDocument() {
      if (!this.taskInfo.businessKey) {
        this.$modal.msgError('无法获取文档信息');
        return;
      }

      const docInfo = this.parseBusinessKey(this.taskInfo.businessKey);
      if (!docInfo) {
        this.$modal.msgError('业务键格式错误');
        return;
      }

      // 提示用户将关闭当前页面
      this.$modal.confirm('编辑文档将关闭当前审批页面，编辑完成后请重新进入审批页面查看最新内容。是否继续？').then(() => {
        // 关闭当前审批页面标签
        this.$tab.closePage(this.$route);

        // 根据文档类型跳转到对应的编辑页面，并传递审批页面信息
        if (docInfo.type === 'receive') {
          this.$router.push({
            path: `/oa/document/receive/edit/${docInfo.docId}`,
            query: {
              fromApproval: 'true',
              taskId: this.taskInfo.taskId,
              taskName: this.taskInfo.taskName,
              processInstanceId: this.taskInfo.processInstanceId,
              businessKey: this.taskInfo.businessKey
            }
          });
        } else if (docInfo.type === 'send') {
          this.$router.push({
            path: `/oa/document/send/edit/${docInfo.docId}`,
            query: {
              fromApproval: 'true',
              taskId: this.taskInfo.taskId,
              taskName: this.taskInfo.taskName,
              processInstanceId: this.taskInfo.processInstanceId,
              businessKey: this.taskInfo.businessKey
            }
          });
        } else {
          this.$modal.msgError('不支持的文档类型');
        }
      }).catch(() => {
        // 用户取消操作
      });
    },

    /** 加载下一步处理人列表 */
    loadNextAssignees() {
      if (!this.taskInfo.taskId) {
        console.log('任务ID为空，跳过加载下一步处理人');
        return;
      }

      this.loadingNextAssignees = true;
      console.log('审批页面开始加载下一步处理人，任务ID:', this.taskInfo.taskId);
      return getNextAssignees(this.taskInfo.taskId).then(response => {
        console.log('下一步处理人API响应:', response);
        if (response && response.data) {
          this.nextAssignees = response.data;
          this.filteredNextAssignees = this.nextAssignees; // 初始化过滤列表
          console.log('下一步处理人加载成功，数量:', this.nextAssignees.length);
          if (this.nextAssignees.length > 0) {
            console.log('第一个下一步处理人:', this.nextAssignees[0]);
          }
        } else {
          console.warn('下一步处理人响应格式异常:', response);
          this.nextAssignees = [];
          this.filteredNextAssignees = [];
        }
        return this.nextAssignees;
      }).catch(error => {
        console.error('获取下一步处理人失败:', error);
        // 如果获取下一步处理人失败，回退到获取所有用户
        console.log('回退到获取所有启用用户...');
        return this.loadAllUsers();
      }).finally(() => {
        this.loadingNextAssignees = false;
      });
    },

    /** 加载所有启用用户（作为备选方案） */
    loadAllUsers() {
      return listAllActiveUsers().then(response => {
        console.log('备选用户列表API响应:', response);
        if (response && response.data) {
          this.nextAssignees = response.data.map(user => ({
            userId: user.userId,
            userName: user.userName,
            nickName: user.nickName,
            activityId: '',
            activityName: '任意处理人'
          }));
          this.filteredNextAssignees = this.nextAssignees; // 初始化过滤列表
          console.log('备选用户列表加载成功，数量:', this.nextAssignees.length);
        } else {
          this.nextAssignees = [];
          this.filteredNextAssignees = [];
        }
        return this.nextAssignees;
      }).catch(error => {
        console.error('获取备选用户列表失败:', error);
        this.$modal.msgError("获取处理人列表失败: " + (error.message || error));
        this.nextAssignees = [];
        this.filteredNextAssignees = [];
        return [];
      });
    },

    /** 过滤下一步处理人 */
    filterNextAssignees(query) {
      if (!query) {
        this.filteredNextAssignees = this.nextAssignees;
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredNextAssignees = this.nextAssignees.filter(assignee => {
        return assignee.nickName.toLowerCase().includes(lowerQuery) ||
               assignee.userName.toLowerCase().includes(lowerQuery);
      });
    },

    /** 查看完整文档 */
    showFullDocument() {
      console.log('查看完整文档，文档类型:', this.documentType, '业务主键:', this.taskInfo.businessKey);

      if (!this.taskInfo.businessKey) {
        this.$modal.msgError('无法获取文档信息');
        return;
      }

      const docInfo = this.parseBusinessKey(this.taskInfo.businessKey);
      if (!docInfo) {
        this.$modal.msgError('业务键格式错误');
        return;
      }

      try {
        if (this.documentType === 'receive') {
          const path = '/oa/document/receive/detail/' + docInfo.docId;
          console.log('跳转到收文详情页面:', path);
          this.$router.push(path);
        } else if (this.documentType === 'send') {
          const path = '/oa/document/send/detail/' + docInfo.docId;
          console.log('跳转到发文详情页面:', path);
          this.$router.push(path);
        } else {
          this.$modal.msgError('不支持的文档类型: ' + this.documentType);
        }
      } catch (error) {
        console.error('跳转到文档详情页面失败:', error);
        this.$modal.msgError('跳转失败: ' + error.message);
      }
    },

    /** 获取紧急程度类型 */
    getUrgencyType(urgency) {
      const typeMap = {
        '特急': 'danger',
        '加急': 'warning',
        '平急': 'info',
        '普通': 'success'
      };
      return typeMap[urgency] || 'info';
    },

    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return '0 Bytes';
      const i = Math.floor(Math.log(size) / Math.log(1024));
      return (size / Math.pow(1024, i)).toFixed(2) + ' ' + ['B', 'KB', 'MB', 'GB', 'TB'][i];
    },

    /** 下载附件 */
    downloadAttachment(attachment) {
      if (attachment.url) {
        window.open(attachment.url, '_blank');
      } else {
        this.$modal.msgError("附件链接不存在");
      }
    },

    /** 预览附件 */
    previewAttachment(attachment) {
      if (attachment.url) {
        window.open(attachment.url, '_blank');
      } else {
        this.$modal.msgError("附件链接不存在");
      }
    },

    /** 加载分管领导列表 */
    loadDepartmentLeaders() {
      this.loadingLeaders = true;
      // 使用新的工作流API获取分管领导列表
      getAvailableLeaders().then(response => {
        if (response && response.code === 200 && response.data) {
          this.departmentLeaders = response.data;
          this.filteredDepartmentLeaders = this.departmentLeaders; // 初始化过滤列表
          console.log('分管领导列表加载成功，数量:', this.departmentLeaders.length);
        } else {
          this.departmentLeaders = [];
          this.filteredDepartmentLeaders = [];
          console.warn('分管领导列表响应格式异常:', response);
        }
      }).catch(error => {
        console.error('获取分管领导列表失败:', error);
        this.departmentLeaders = [];
        this.filteredDepartmentLeaders = [];
        this.$modal.msgError("获取分管领导列表失败: " + (error.message || error));
      }).finally(() => {
        this.loadingLeaders = false;
      });
    },

    /** 加载科室负责人列表 */
    loadDepartmentManagers() {
      console.log('🔍 开始加载科室负责人列表...');
      this.loadingManagers = true;

      // 获取当前登录用户信息，确定当前分管领导
      this.getCurrentUser().then(currentUser => {
        console.log('📋 获取到当前用户信息:', currentUser);
        if (currentUser && currentUser.userName) {
          console.log(`🔍 正在查询用户 ${currentUser.userName} 管理的科室负责人...`);
          // 使用当前用户作为分管领导ID查询其管理的科室负责人
          getAvailableManagers(currentUser.userName).then(response => {
            console.log('📡 API响应:', response);
            if (response && response.code === 200 && response.data) {
              this.departmentManagers = response.data;
              console.log('✅ 科室负责人列表加载成功，数量:', this.departmentManagers.length);
              console.log('📋 科室负责人列表:', this.departmentManagers);
            } else {
              this.departmentManagers = [];
              console.warn('⚠️ 科室负责人列表响应格式异常:', response);
            }
          }).catch(error => {
            console.error('❌ 获取科室负责人列表失败:', error);
            this.departmentManagers = [];
            this.$modal.msgError("获取科室负责人列表失败: " + (error.message || error));
          }).finally(() => {
            this.loadingManagers = false;
          });
        } else {
          console.error('❌ 无法获取当前用户信息');
          this.departmentManagers = [];
          this.loadingManagers = false;
        }
      }).catch(error => {
        console.error('❌ 获取当前用户信息失败:', error);
        this.departmentManagers = [];
        this.loadingManagers = false;
      });
    },

    /** 获取当前用户信息 */
    getCurrentUser() {
      return new Promise((resolve, reject) => {
        // 使用导入的getInfo函数获取用户信息
        getInfo().then(response => {
          if (response.code === 200 && response.user) {
            const userInfo = response.user;
            console.log('📋 获取到当前用户信息:', userInfo);
            resolve({
              userName: userInfo.userName,
              nickName: userInfo.nickName,
              userId: userInfo.userId
            });
          } else {
            console.error('❌ 获取用户信息失败:', response.msg);
            reject(new Error('获取用户信息失败: ' + response.msg));
          }
        }).catch(error => {
          console.error('❌ 调用用户信息接口失败:', error);
          reject(error);
        });
      });
    },

    /** 加载经办人列表 */
    loadDepartmentHandlers() {
      console.log('🔍 开始加载经办人列表...');
      this.loadingHandlers = true;

      // 获取当前登录用户信息，确定当前科室负责人
      this.getCurrentUser().then(currentUser => {
        console.log('📋 获取到当前用户信息:', currentUser);
        if (currentUser && currentUser.userName) {
          console.log(`🔍 正在查询科室负责人 ${currentUser.userName} 负责的部门人员...`);
          // 使用当前用户作为科室负责人ID查询其负责部门下的经办人
          getAvailableHandlers(currentUser.userName).then(response => {
            console.log('📡 API响应:', response);
            if (response && response.code === 200 && response.data) {
              this.departmentHandlers = response.data;
              console.log('✅ 经办人列表加载成功，数量:', this.departmentHandlers.length);
              console.log('📋 经办人列表:', this.departmentHandlers);
            } else {
              this.departmentHandlers = [];
              console.warn('⚠️ 经办人列表响应格式异常:', response);
            }
          }).catch(error => {
            console.error('❌ 获取经办人列表失败:', error);
            this.departmentHandlers = [];
            this.$modal.msgError("获取经办人列表失败: " + (error.message || error));
          }).finally(() => {
            this.loadingHandlers = false;
          });
        } else {
          console.error('❌ 无法获取当前用户信息');
          this.departmentHandlers = [];
          this.loadingHandlers = false;
        }
      }).catch(error => {
        console.error('❌ 获取当前用户信息失败:', error);
        this.departmentHandlers = [];
        this.loadingHandlers = false;
      });
    },

    /** 过滤分管领导 */
    filterDepartmentLeaders(query) {
      if (!query) {
        this.filteredDepartmentLeaders = this.departmentLeaders;
        return;
      }

      const lowerQuery = query.toLowerCase();
      this.filteredDepartmentLeaders = this.departmentLeaders.filter(leader => {
        return leader.nickName.toLowerCase().includes(lowerQuery) ||
               leader.userName.toLowerCase().includes(lowerQuery);
      });
    }
  }
};
</script>

<style scoped>
.approval-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;
}

.approval-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.task-info .el-tag {
  margin-right: 10px;
}

.document-detail {
  min-height: 600px;
}

.document-info {
  margin-top: 20px;
}

.content-preview {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  padding: 15px;
  margin-top: 10px;
  background-color: #fafafa;
}

.attachments {
  margin-top: 20px;
}

.approval-actions {
  position: sticky;
  top: 20px;
}

.approval-history {
  max-height: 400px;
  overflow-y: auto;
}

.no-history {
  text-align: center;
  padding: 20px;
}
</style>
