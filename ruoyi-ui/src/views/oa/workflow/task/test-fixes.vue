<template>
  <div class="app-container">
    <h2>待办任务功能测试</h2>

    <el-card class="test-section">
      <div slot="header">
        <span>测试功能</span>
      </div>

      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" @click="testUserList">测试用户列表加载</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="testRouteNavigation">测试路由跳转</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="testDelegateDialog">测试转办对话框</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" @click="goToTodoPage">跳转到待办任务</el-button>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 10px;">
        <el-col :span="6">
          <el-button type="danger" @click="testNextAssignees">测试下一步处理人</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" @click="testApprovalHistory">测试审批历史</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" @click="goToApprovalPage">跳转到审批页面</el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="clearLogs">清空日志</el-button>
        </el-col>
      </el-row>
    </el-card>

    <el-card class="test-section" style="margin-top: 20px;">
      <div slot="header">
        <span>用户列表测试结果</span>
      </div>

      <div v-if="userList.length > 0">
        <p>用户列表加载成功，共 {{ userList.length }} 个用户：</p>
        <el-table :data="userList.slice(0, 5)" size="mini">
          <el-table-column prop="userId" label="用户ID" width="80" />
          <el-table-column prop="userName" label="用户名" width="120" />
          <el-table-column prop="nickName" label="昵称" width="120" />
          <el-table-column prop="status" label="状态" width="80" />
        </el-table>
      </div>
      <div v-else>
        <el-empty description="暂无用户数据" />
      </div>
    </el-card>

    <el-card class="test-section" style="margin-top: 20px;">
      <div slot="header">
        <span>测试日志</span>
      </div>

      <div class="test-logs">
        <div v-for="(log, index) in testLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </el-card>

    <!-- 转办对话框测试 -->
    <el-dialog title="转办测试" :visible.sync="delegateTestOpen" width="500px">
      <el-form :model="delegateForm" :rules="delegateRules" label-width="100px">
        <el-form-item label="转办给" prop="assignee">
          <el-select v-model="delegateForm.assignee" placeholder="请选择转办人" style="width: 100%">
            <el-option
              v-for="user in userList"
              :key="user.userId"
              :label="user.nickName"
              :value="user.userName"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="delegateTestOpen = false">取消</el-button>
        <el-button type="primary" @click="submitDelegateTest">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";

export default {
  name: "TestFixes",
  data() {
    return {
      userList: [],
      testLogs: [],
      delegateTestOpen: false,
      delegateForm: {
        assignee: ''
      },
      delegateRules: {
        assignee: [
          { required: true, message: '请选择转办人', trigger: 'change' }
        ]
      }
    };
  },
  methods: {
    addLog(message) {
      this.testLogs.unshift({
        time: new Date().toLocaleTimeString(),
        message: message
      });
    },

    testUserList() {
      this.addLog('开始测试用户列表加载...');

      listUser({ status: '0' }).then(response => {
        console.log('用户列表响应:', response);
        if (response && response.rows) {
          this.userList = response.rows;
          this.addLog(`用户列表加载成功，共 ${this.userList.length} 个用户`);
          if (this.userList.length > 0) {
            this.addLog(`第一个用户: ${this.userList[0].nickName} (${this.userList[0].userName})`);
          }
        } else {
          this.addLog(`用户列表响应格式异常: ${JSON.stringify(response)}`);
        }
      }).catch(error => {
        console.error('获取用户列表失败:', error);
        this.addLog(`用户列表加载失败: ${error.message || error}`);
      });
    },

    testRouteNavigation() {
      this.addLog('测试路由跳转到监控页面...');

      // 模拟跳转到监控页面
      const testProcessInstanceId = 'test-process-instance-123';

      // 先检查路由是否存在
      const route = this.$router.resolve({
        path: '/oa/workflow/monitor',
        query: {
          processInstanceId: testProcessInstanceId,
          taskId: 'test-task-456',
          businessKey: 'test-business-789'
        }
      });

      this.addLog('路由解析结果: ' + route.resolved.fullPath);

      this.$router.push({
        path: '/oa/workflow/monitor',
        query: {
          processInstanceId: testProcessInstanceId,
          taskId: 'test-task-456',
          businessKey: 'test-business-789'
        }
      }).then(() => {
        this.addLog('路由跳转成功');
      }).catch(error => {
        this.addLog('路由跳转失败: ' + error.message);
      });
    },

    testDelegateDialog() {
      this.addLog('打开转办对话框测试...');

      if (this.userList.length === 0) {
        this.testUserList();
      }

      this.delegateTestOpen = true;
      this.addLog('转办对话框已打开');
    },

    submitDelegateTest() {
      if (!this.delegateForm.assignee) {
        this.$message.warning('请选择转办人');
        return;
      }

      this.addLog(`转办测试成功，选择的用户: ${this.delegateForm.assignee}`);
      this.delegateTestOpen = false;
      this.delegateForm.assignee = '';
    },

    goToTodoPage() {
      this.addLog('跳转到待办任务页面...');
      this.$router.push('/oa/workflow/task/todo');
    },

    testNextAssignees() {
      this.addLog('测试下一步处理人API...');

      // 使用一个测试任务ID
      const testTaskId = 'test-task-id-123';

      import('@/api/oa/workflow').then(module => {
        if (module.getNextAssignees) {
          module.getNextAssignees(testTaskId).then(response => {
            this.addLog(`下一步处理人API调用成功: ${JSON.stringify(response)}`);
          }).catch(error => {
            this.addLog(`下一步处理人API调用失败: ${error.message}`);
          });
        } else {
          this.addLog('getNextAssignees API 不存在');
        }
      });
    },

    testApprovalHistory() {
      this.addLog('测试审批历史API...');

      // 使用一个测试流程实例ID
      const testProcessInstanceId = 'test-process-instance-123';

      import('@/api/oa/workflow').then(module => {
        if (module.getProcessApprovalInfo) {
          module.getProcessApprovalInfo(testProcessInstanceId).then(response => {
            this.addLog(`审批历史API调用成功: ${JSON.stringify(response)}`);
          }).catch(error => {
            this.addLog(`审批历史API调用失败: ${error.message}`);
          });
        } else {
          this.addLog('getProcessApprovalInfo API 不存在');
        }
      });
    },

    goToApprovalPage() {
      this.addLog('跳转到审批页面...');
      this.$router.push({
        path: '/oa/workflow/task/approval',
        query: {
          taskId: 'test-task-123',
          taskName: '测试任务',
          workflowName: '测试流程',
          processInstanceId: 'test-process-123',
          businessKey: 'test-business-123'
        }
      });
    },

    clearLogs() {
      this.testLogs = [];
      this.addLog('日志已清空');
    }
  },

  created() {
    this.addLog('测试页面初始化完成');
  }
};
</script>

<style scoped>
.test-section {
  margin-bottom: 20px;
}

.test-logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  margin-bottom: 8px;
  font-size: 14px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #303133;
}
</style>
