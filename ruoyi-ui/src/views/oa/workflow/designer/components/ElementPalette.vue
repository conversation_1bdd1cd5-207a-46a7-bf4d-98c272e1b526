<template>
  <div class="element-palette">
    <div class="palette-tool" v-for="group in paletteTools" :key="group.title">
      <div class="palette-title">{{ group.title }}</div>
      <div class="palette-items">
        <div
          v-for="item in group.tools"
          :key="item.type"
          class="palette-item"
          :class="{ disabled: !modelerReady }"
          @mousedown="handleMouseDown(item.type, item.name, $event)"
          @click="handleClick(item.type, item.name, $event)"
          @dragstart="handleDragStart"
          :title="modelerReady ? item.name : '正在初始化...'"
          :draggable="false"
        >
          <i :class="item.icon"></i>
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElementPalette',
  props: {
    modelerReady: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDragging: false,
      paletteTools: [
        {
          title: '基本元素',
          tools: [
            { name: '开始事件', type: 'bpmn:StartEvent', icon: 'start-icon' },
            { name: '结束事件', type: 'bpmn:EndEvent', icon: 'end-icon' },
            { name: '用户任务', type: 'bpmn:UserTask', icon: 'task-icon' },
          ]
        },
        {
          title: '流程控制',
          tools: [
            { name: '排他网关', type: 'bpmn:ExclusiveGateway', icon: 'exclusive-icon' },
            { name: '并行网关', type: 'bpmn:ParallelGateway', icon: 'parallel-icon' },
          ]
        },
        {
          title: '辅助工具',
          tools: [
            { name: '文本注释', type: 'bpmn:TextAnnotation', icon: 'note-icon' },
          ]
        }
      ]
    };
  },
  methods: {
    handleMouseDown(type, name, event) {
      if (!this.modelerReady) {
        console.log('建模器未准备好，忽略mousedown事件');
        return;
      }

      console.log('ElementPalette mousedown:', { type, name, event });

      // 启动拖拽
      this.startDrag(type, name, event);
    },

    handleClick(type, name, event) {
      if (!this.modelerReady) {
        console.log('建模器未准备好，忽略click事件');
        return;
      }

      console.log('ElementPalette click:', { type, name, event });

      // 阻止默认行为和事件冒泡
      event.preventDefault();
      event.stopPropagation();

      // 如果是点击事件（不是拖拽后的点击），则创建元素
      if (!this.isDragging) {
        this.createElement(type, name);
      }

      this.isDragging = false;
    },

    handleDragStart(event) {
      // 阻止HTML5原生拖拽
      event.preventDefault();
      return false;
    },

    startDrag(type, name, event) {
      console.log('开始拖拽:', { type, name });
      this.isDragging = true;
      this.$emit('start-drag', { type, name, event });
    },

    createElement(type, name) {
      console.log('创建元素:', { type, name });
      this.$emit('create-element', { type, name });
    }
  },


};
</script>

<style lang="scss" scoped>
.element-palette {
  width: 140px;
  background-color: #fff;
  border-right: 1px solid #ddd;
  overflow-y: auto;
  flex-shrink: 0;

  .palette-title {
    font-size: 13px;
    font-weight: 600;
    padding: 10px 8px;
    border-bottom: 1px solid #eee;
    background-color: #f8f9fa;
    color: #333;
  }

  .palette-items {
    display: flex;
    flex-direction: column;
    padding: 3px;
  }

  .palette-item {
    width: 100%;
    padding: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    cursor: grab;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin-bottom: 2px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    &:hover {
      background-color: #e3f2fd;
    }

    &:active {
      cursor: grabbing;
      background-color: #bbdefb;
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }

    i {
      font-size: 18px;
      margin-right: 8px;
      width: 20px;
      text-align: center;
    }

    span {
      font-size: 12px;
      color: #333;
      line-height: 1.2;
    }
  }
}

/* 自定义图标样式 */
.palette-item i {
  font-style: normal;
  font-weight: bold;
}

.start-icon:before { content: '●'; color: #4caf50; }
.end-icon:before { content: '◉'; color: #f44336; }
.task-icon:before { content: '□'; color: #2196f3; }
.exclusive-icon:before { content: '◇'; color: #ff9800; }
.parallel-icon:before { content: '✚'; color: #9c27b0; }
.note-icon:before { content: '📝'; color: #607d8b; }
</style>
