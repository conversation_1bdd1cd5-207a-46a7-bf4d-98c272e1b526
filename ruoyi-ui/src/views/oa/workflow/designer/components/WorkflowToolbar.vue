<template>
  <div class="workflow-toolbar">
    <div class="toolbar-left">
      <el-button type="primary" size="small" icon="el-icon-back" @click="goBack">返回</el-button>
      <el-divider direction="vertical"></el-divider>
      <el-input
        v-if="isEditTitle"
        v-model="processName"
        size="small"
        @blur="handleTitleBlur"
        @keyup.enter.native="handleTitleBlur"
        v-focus
        style="width: 250px"
      />
      <span v-else class="process-title" @click="isEditTitle = true">
        {{ processName || '新建流程' }}
        <i class="el-icon-edit"></i>
      </span>
    </div>
    <div class="toolbar-right">
      <el-button-group size="small">
        <el-button icon="el-icon-refresh-left" @click="undo" :disabled="!canUndo">撤销</el-button>
        <el-button icon="el-icon-refresh-right" @click="redo" :disabled="!canRedo">恢复</el-button>
        <el-button icon="el-icon-rank" @click="fitViewport">自适应</el-button>
      </el-button-group>
      <el-divider direction="vertical"></el-divider>
      <el-button size="small" type="success" icon="el-icon-upload" @click="deploy" :loading="deployLoading">部署</el-button>
      <el-button size="small" type="primary" icon="el-icon-check" @click="save" :loading="saveLoading">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WorkflowToolbar',
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus();
      }
    }
  },
  props: {
    processName: {
      type: String,
      default: '新建流程'
    },
    canUndo: {
      type: Boolean,
      default: false
    },
    canRedo: {
      type: Boolean,
      default: false
    },
    saveLoading: {
      type: Boolean,
      default: false
    },
    deployLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditTitle: false
    };
  },
  methods: {
    goBack() {
      this.$emit('go-back');
    },
    handleTitleBlur() {
      this.isEditTitle = false;
      this.$emit('title-change', this.processName);
    },
    undo() {
      this.$emit('undo');
    },
    redo() {
      this.$emit('redo');
    },
    fitViewport() {
      this.$emit('fit-viewport');
    },
    save() {
      this.$emit('save');
    },
    deploy() {
      this.$emit('deploy');
    }
  }
};
</script>

<style lang="scss" scoped>
.workflow-toolbar {
  height: 50px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  flex-shrink: 0;

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .process-title {
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    .el-icon-edit {
      opacity: 0;
      transition: opacity 0.2s;
    }
    &:hover .el-icon-edit {
      opacity: 1;
    }
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
}
</style> 