<template>
  <div class="properties-panel" :class="{ collapsed: collapsed }">
    <div class="collapse-handle" @click="toggleCollapse">
      <i :class="collapsed ? 'el-icon-d-arrow-left' : 'el-icon-d-arrow-right'"></i>
    </div>
    <div class="properties-content" v-show="!collapsed">
      <div v-if="selectedElement" class="properties-panel-inner">
        <div class="properties-header">
          <h4>{{ selectedElement.name || selectedElement.id }}</h4>
          <p>{{ selectedElement.type }}</p>
        </div>
        <el-tabs v-model="activeTab" type="border-card" stretch>
          <el-tab-pane label="常规" name="general">
            <el-form size="small" label-width="80px">
              <el-form-item label="元素ID">
                <el-input v-model="selectedElement.id" disabled />
              </el-form-item>
              <el-form-item label="元素名称">
                <el-input v-model="selectedElement.name" @input="updateProperty('name', $event)" />
              </el-form-item>
              <!-- 流程属性 -->
              <template v-if="isProcess(selectedElement.type)">
                <el-form-item label="流程标识">
                  <el-input v-model="processKey" @input="updateProcessProperty('key', $event)" />
                </el-form-item>
                <el-form-item label="可执行">
                  <el-switch v-model="selectedElement.isExecutable" @change="updateProperty('isExecutable', $event)" />
                </el-form-item>
              </template>
            </el-form>
          </el-tab-pane>

          <!-- 用户任务特有属性 -->
          <el-tab-pane label="处理人" name="assignment" v-if="isUserTask(selectedElement.type)">
            <el-form size="small" label-width="100px">
              <el-form-item label="处理人">
                <el-select
                  :value="selectedElement.assignee"
                  @input="handleAssigneeChange"
                  @change="handleAssigneeChange"
                  @focus="loadUsers"
                  filterable
                  remote
                  reserve-keyword
                  placeholder="搜索用户"
                  :remote-method="searchUsers"
                  :loading="userLoading"
                  style="width: 100%;"
                  clearable
                >
                  <el-option
                    v-for="user in userOptions"
                    :key="user.userId"
                    :label="`${user.nickName}(${user.userName})`"
                    :value="user.userName"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <!-- 网关/连线 条件 -->
          <el-tab-pane label="条件" name="condition" v-if="isSequenceFlow(selectedElement.type)">
            <el-form size="small" label-width="100px">
              <el-form-item label="条件类型">
                <el-select v-model="selectedElement.conditionType" @change="updateConditionType">
                  <el-option label="无条件" value="none" />
                  <el-option label="默认路径" value="default" />
                  <el-option label="条件表达式" value="expression" />
                </el-select>
              </el-form-item>
              <el-form-item label="表达式" v-if="selectedElement.conditionType === 'expression'">
                <el-input 
                  v-model="selectedElement.conditionExpression" 
                  @input="updateProperty('conditionExpression', { body: $event })" 
                  placeholder="例如：${approved == true}" 
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else class="properties-panel-inner placeholder">
        <h4>流程属性</h4>
        <el-form size="small" label-width="80px">
          <el-form-item label="流程ID">
            <el-input v-model="processKey" placeholder="流程唯一标识" />
          </el-form-item>
          <el-form-item label="流程名称">
            <el-input v-model="processName" placeholder="流程名称" />
          </el-form-item>
          <el-form-item label="流程分类">
            <el-select v-model="processCategory" placeholder="选择分类" style="width: 100%;">
              <el-option label="收文流程" value="receive" />
              <el-option label="发文流程" value="send" />
              <el-option label="印章流程" value="seal" />
              <el-option label="会议流程" value="meeting" />
              <el-option label="其他流程" value="other" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";

export default {
  name: 'PropertiesPanel',
  props: {
    selectedElement: {
      type: Object,
      default: null
    },
    processKey: {
      type: String,
      default: ''
    },
    processName: {
      type: String,
      default: ''
    },
    processCategory: {
      type: String,
      default: 'other'
    }
  },
  data() {
    return {
      collapsed: false,
      activeTab: 'general',
      userOptions: [],
      userLoading: false,
      searchTimeout: null
    };
  },
  beforeDestroy() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  },
  methods: {
    toggleCollapse() {
      this.collapsed = !this.collapsed;
    },
    updateProperty(key, value) {
      this.$emit('update-property', { key, value });
    },
    updateProcessProperty(key, value) {
      this.$emit('update-process-property', { key, value });
    },
    handleAssigneeChange(value) {
      // 立即更新本地状态
      if (this.selectedElement) {
        this.selectedElement.assignee = value;
      }
      // 立即同步到BPMN模型
      this.updateProperty('assignee', value);
    },
    updateConditionType(type) {
      this.$emit('update-condition-type', type);
    },
    isProcess(type) {
      return type === 'bpmn:Process';
    },
    isUserTask(type) {
      return type === 'bpmn:UserTask';
    },
    isSequenceFlow(type) {
      return type === 'bpmn:SequenceFlow';
    },
    searchUsers(query) {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.doSearchUsers(query);
      }, 300);
    },
    async doSearchUsers(query) {
      if (!query || query.length < 2) {
        this.loadUsers();
        return;
      }
      this.userLoading = true;
      try {
        const response = await listUser({
          userName: query,
          nickName: query,
          pageNum: 1,
          pageSize: 20
        });
        this.userOptions = response.rows || [];
      } catch (error) {
        this.$message.error('搜索用户失败');
      } finally {
        this.userLoading = false;
      }
    },
    async loadUsers() {
      if (this.userOptions.length > 0) return; // 已经加载过
      this.userLoading = true;
      try {
        const response = await listUser({
          pageNum: 1,
          pageSize: 50
        });
        this.userOptions = response.rows || [];
      } catch (error) {
        console.warn('加载用户失败:', error);
      } finally {
        this.userLoading = false;
      }
    }
  },
  mounted() {
    this.loadUsers();
  }
};
</script>

<style lang="scss" scoped>
.properties-panel {
  width: 280px;
  background-color: #fff;
  border-left: 1px solid #ddd;
  overflow-y: auto;
  flex-shrink: 0;
  transition: width 0.3s;
  position: relative;

  &.collapsed {
    width: 30px;
  }

  .collapse-handle {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 80px;
    background-color: #f1f3f5;
    border: 1px solid #ddd;
    border-right: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px 0 0 6px;
    margin-left: -20px;

    &:hover {
      background-color: #e9ecef;
    }
  }

  .properties-panel-inner {
    &.placeholder {
      padding: 20px;
    }
  }

  .properties-header {
    padding: 15px;
    border-bottom: 1px solid #eee;

    h4 {
      margin: 0 0 5px 0;
      font-size: 14px;
    }
    p {
      margin: 0;
      font-size: 12px;
      color: #888;
    }
  }

  .el-tabs--border-card {
    box-shadow: none;
    border: none;
    .el-tabs__content {
      padding: 15px;
    }
  }
}
</style> 