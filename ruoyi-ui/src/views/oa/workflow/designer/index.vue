<template>
  <div class="designer-container">
    <!-- 工具栏 -->
    <WorkflowToolbar
      :process-name="processInfo.workflowName"
      :can-undo="canUndo"
      :can-redo="canRedo"
      :save-loading="saveLoading"
      :deploy-loading="deployLoading"
      @go-back="goBack"
      @title-change="handleTitleChange"
      @undo="handleUndo"
      @redo="handleRedo"
      @fit-viewport="handleFitViewport"
      @save="handleSave"
      @deploy="handleDeploy"
    />

    <!-- 主要内容区域 -->
    <div class="designer-main">
      <!-- 元素面板 -->
      <ElementPalette
        :modeler-ready="modelerReady"
        @start-drag="handleStartDrag"
        @create-element="handleCreateElement"
      />

      <!-- BPMN画布 -->
      <BpmnCanvas
        ref="bpmnCanvas"
        :xml="currentXml"
        @ready="handleModelerReady"
        @element-selected="handleElementSelected"
        @element-deselected="handleElementDeselected"
        @element-changed="handleElementChanged"
        @command-stack-changed="handleCommandStackChanged"
      />

      <!-- 属性面板 -->
      <PropertiesPanel
        ref="propertiesPanel"
        :selected-element="selectedElement"
        :process-key="processInfo.workflowKey"
        :process-name="processInfo.workflowName"
        :process-category="processInfo.workflowCategory"
        @update-property="handleUpdateProperty"
        @update-process-property="handleUpdateProcessProperty"
        @update-condition-type="handleUpdateConditionType"
      />
    </div>
  </div>
</template>

<script>
import WorkflowToolbar from './components/WorkflowToolbar.vue';
import ElementPalette from './components/ElementPalette.vue';
import BpmnCanvas from './components/BpmnCanvas.vue';
import PropertiesPanel from './components/PropertiesPanel.vue';
import workflowMixin from './mixins/workflowMixin';

// 默认的空白BPMN XML
const emptyBpmn = `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1" isExecutable="true" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1" />
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`;

export default {
  name: 'WorkflowDesigner',
  components: {
    WorkflowToolbar,
    ElementPalette,
    BpmnCanvas,
    PropertiesPanel
  },
  mixins: [workflowMixin],
  data() {
    return {
      modelerReady: false,
      selectedElement: null,
      currentXml: emptyBpmn, // 初始化为默认的空白XML
      canUndo: false,
      canRedo: false
    };
  },
  async mounted() {
    this.initProcessInfo();
    await this.loadInitialData();
  },
  watch: {
    '$route'(to, from) {
      // 当路由参数变化时，重新初始化组件
      // 检测workflowId变化或时间戳变化（用于强制刷新）
      if (to.path === from.path &&
          (to.query.workflowId !== from.query.workflowId || to.query._t !== from.query._t)) {
        console.log('检测到路由参数变化，重新初始化', {
          fromId: from.query.workflowId,
          toId: to.query.workflowId,
          fromTimestamp: from.query._t,
          toTimestamp: to.query._t
        });
        this.resetComponent();
      }
    }
  },
  methods: {
    // 重置组件状态
    resetComponent() {
      // 重置所有状态
      this.modelerReady = false;
      this.selectedElement = null;
      this.currentXml = emptyBpmn;
      this.canUndo = false;
      this.canRedo = false;

      // 重新初始化流程信息
      this.initProcessInfo();

      // 重新加载数据
      this.loadInitialData();
    },

    // 初始化加载数据
    async loadInitialData() {
      try {
        const xml = await this.loadProcessData();
        // 如果有加载到XML，则使用加载的XML，否则保持默认的空白XML
        if (xml) {
          this.currentXml = xml;
        }
      } catch (error) {
        console.warn('加载流程数据失败，使用默认空白画布:', error);
        // 保持默认的空白XML
      }
    },

    // 处理建模器就绪
    handleModelerReady() {
      this.modelerReady = true;
      this.updateCommandStackStatus();

      // 建模器就绪后，设置流程基本信息
      this.$nextTick(() => {
        if (this.$refs.bpmnCanvas) {
          this.$refs.bpmnCanvas.updateProcessProperty('name', this.processInfo.workflowName);
          this.$refs.bpmnCanvas.updateProcessProperty('key', this.processInfo.workflowKey);
        }
      });
    },

    // 处理元素选择
    handleElementSelected(element) {
      if (!element) return;

      // 在选择新元素前，先保存当前元素的状态
      if (this.selectedElement && this.selectedElement.id !== element.id) {
        this.handleElementDeselected(this.selectedElement);
      }

      // 如果选择的是Process根元素，进行特殊处理
      if (element.type === 'bpmn:Process') {
        console.log('选择了流程根元素:', element.id);
        this.selectedElement = {
          id: element.id,
          type: element.type,
          name: element.businessObject.name || '',
          isExecutable: element.businessObject.isExecutable || false
        };
        return;
      }

      // 强制刷新元素引用，确保获取最新状态
      const freshElement = this.$refs.bpmnCanvas ?
        this.$refs.bpmnCanvas.modeler.get('elementRegistry').get(element.id) : element;
      const bo = freshElement.businessObject || {};

      // 正确读取Flowable命名空间的属性
      let assignee = '';
      let candidateUsers = '';
      let candidateGroups = '';

      // 优先从$attrs中读取Flowable命名空间属性
      if (bo.$attrs) {
        assignee = bo.$attrs['flowable:assignee'] || '';
        candidateUsers = bo.$attrs['flowable:candidateUsers'] || '';
        candidateGroups = bo.$attrs['flowable:candidateGroups'] || '';
      }

      // 如果$attrs中没有，尝试从标准属性读取
      if (!assignee && bo.assignee) {
        assignee = bo.assignee;
      }
      if (!candidateUsers && bo.candidateUsers) {
        candidateUsers = bo.candidateUsers;
      }
      if (!candidateGroups && bo.candidateGroups) {
        candidateGroups = bo.candidateGroups;
      }

      // 调试输出
      console.log('读取元素属性:', {
        elementId: element.id,
        elementType: element.type,
        businessObject: bo,
        attrs: bo.$attrs,
        attrsFlowableAssignee: bo.$attrs ? bo.$attrs['flowable:assignee'] : 'undefined',
        standardAssignee: bo.assignee,
        finalAssignee: assignee,
        finalCandidateUsers: candidateUsers,
        finalCandidateGroups: candidateGroups
      });

      this.selectedElement = {
        id: element.id || '',
        type: element.type || '',
        name: bo.name || '',
        isExecutable: bo.isExecutable || false,
        assignee: assignee,
        candidateUsers: candidateUsers,
        candidateGroups: candidateGroups,
        documentation: bo.documentation && bo.documentation[0] ? bo.documentation[0].text : '',
        conditionExpression: bo.conditionExpression ? bo.conditionExpression.body : '',
        conditionType: bo.conditionExpression ? 'expression' : 'none'
      };

      console.log('选中元素属性:', this.selectedElement);

      // 立即同步一次，确保数据一致性
      this.$nextTick(() => {
        if (this.$refs.propertiesPanel) {
          this.$refs.propertiesPanel.$forceUpdate();
        }
      });
    },

    // 处理元素取消选择
    handleElementDeselected(element) {
      // 保存当前元素状态到BPMN模型中
      if (this.selectedElement && this.$refs.bpmnCanvas) {
        // 只对特定类型的元素进行属性保存
        const saveableTypes = ['bpmn:UserTask', 'bpmn:ServiceTask', 'bpmn:ScriptTask', 'bpmn:ManualTask'];
        if (!saveableTypes.includes(this.selectedElement.type)) {
          console.log('跳过不需要保存属性的元素类型:', this.selectedElement.type);
          return;
        }

        try {
          // 首先验证元素是否仍然存在
          const allElements = this.$refs.bpmnCanvas.getAllElements();
          const elementExists = allElements.some(el => el.id === this.selectedElement.id);

          if (!elementExists) {
            console.warn('元素已不存在，跳过保存:', this.selectedElement.id);
            console.log('当前存在的元素:', allElements.map(el => ({ id: el.id, type: el.type })));
            return;
          }

          // 额外验证：检查元素类型是否匹配
          const currentElement = allElements.find(el => el.id === this.selectedElement.id);
          if (currentElement && currentElement.type !== this.selectedElement.type) {
            console.warn('元素类型不匹配，跳过保存:', {
              expected: this.selectedElement.type,
              actual: currentElement.type,
              elementId: this.selectedElement.id
            });
            return;
          }

          // 分别处理标准属性和Flowable属性
          const standardProperties = {};
          const flowableProperties = {};

          if (this.selectedElement.name !== undefined) {
            standardProperties.name = this.selectedElement.name;
          }

          // 对于用户任务，保存Flowable特定属性
          if (this.selectedElement.type === 'bpmn:UserTask') {
            if (this.selectedElement.assignee !== undefined) {
              flowableProperties['flowable:assignee'] = this.selectedElement.assignee;
            }
            if (this.selectedElement.candidateUsers !== undefined) {
              flowableProperties['flowable:candidateUsers'] = this.selectedElement.candidateUsers;
            }
            if (this.selectedElement.candidateGroups !== undefined) {
              flowableProperties['flowable:candidateGroups'] = this.selectedElement.candidateGroups;
            }
          }

          console.log('保存标准属性:', this.selectedElement.id, standardProperties);
          console.log('保存Flowable属性:', this.selectedElement.id, flowableProperties);

          // 分别更新标准属性和Flowable属性
          if (Object.keys(standardProperties).length > 0) {
            this.$refs.bpmnCanvas.updateElementProperty(this.selectedElement.id, standardProperties);
          }

          // 逐个更新Flowable属性，避免批量更新导致的问题
          for (const [key, value] of Object.entries(flowableProperties)) {
            this.$refs.bpmnCanvas.updateElementProperty(this.selectedElement.id, key, value);
          }
        } catch (error) {
          console.warn('保存元素状态失败:', error);
        }
      }
    },

    // 处理元素变更
    handleElementChanged(element) {
      if (this.selectedElement && element.id === this.selectedElement.id) {
        this.handleElementSelected(element);
      }
    },

    // 处理命令栈变更
    handleCommandStackChanged() {
      this.updateCommandStackStatus();
    },

    // 更新命令栈状态
    updateCommandStackStatus() {
      if (this.$refs.bpmnCanvas) {
        this.canUndo = this.$refs.bpmnCanvas.canUndo();
        this.canRedo = this.$refs.bpmnCanvas.canRedo();
      }
    },

    // 处理标题变更
    handleTitleChange(title) {
      this.updateProcessInfo('workflowName', title);
      if (this.$refs.bpmnCanvas) {
        this.$refs.bpmnCanvas.updateProcessProperty('name', title);
      }
    },

    // 处理撤销
    handleUndo() {
      if (this.$refs.bpmnCanvas) {
        this.$refs.bpmnCanvas.undo();
      }
    },

    // 处理重做
    handleRedo() {
      if (this.$refs.bpmnCanvas) {
        this.$refs.bpmnCanvas.redo();
      }
    },

    // 处理自适应视图
    handleFitViewport() {
      if (this.$refs.bpmnCanvas) {
        this.$refs.bpmnCanvas.fitViewport();
      }
    },

    // 处理保存
    async handleSave() {
      if (!this.$refs.bpmnCanvas) return;

      try {
        // 验证用户任务处理人
        const elements = this.$refs.bpmnCanvas.getAllElements();
        if (!this.validateUserTaskAssignments(elements)) {
          return;
        }

        const xml = await this.$refs.bpmnCanvas.saveXML();
        this.validateAssignmentInXML(xml);

        const result = await this.saveProcess(xml);
        if (result) {
          this.currentXml = xml;
        }
      } catch (error) {
        this.$message.error('保存失败: ' + error.message);
      }
    },

    // 处理部署
    async handleDeploy() {
      if (!this.$refs.bpmnCanvas) return;

      try {
        const xml = await this.$refs.bpmnCanvas.saveXML();
        const success = await this.deployProcess(xml);
        if (success) {
          this.goBack();
        }
      } catch (error) {
        this.$message.error('部署失败: ' + error.message);
      }
    },

    // 处理开始拖拽
    handleStartDrag({ type, name, event }) {
      console.log('主页面处理拖拽:', { type, name, event });

      if (!this.$refs.bpmnCanvas) {
        console.error('BpmnCanvas引用不存在');
        this.$message.error('画布未初始化');
        return;
      }

      try {
        this.$refs.bpmnCanvas.startDrag(type, name, event);
        console.log('拖拽启动成功');
      } catch (error) {
        console.error('拖拽失败:', error);
        this.$message.error('拖拽失败: ' + error.message);

        // 如果拖拽失败，尝试直接创建元素
        console.log('尝试直接创建元素作为备选方案');
        this.handleCreateElement({ type, name });
      }
    },

    // 处理创建元素
    handleCreateElement({ type, name }) {
      console.log('主页面处理创建元素:', { type, name });

      if (!this.$refs.bpmnCanvas) {
        console.error('BpmnCanvas引用不存在');
        this.$message.error('画布未初始化');
        return;
      }

      try {
        const element = this.$refs.bpmnCanvas.createElement(type, name);
        console.log('元素创建成功:', element);
        this.$message.success(`已创建 ${name}`);
      } catch (error) {
        console.error('创建元素失败:', error);
        this.$message.error('创建元素失败: ' + error.message);
      }
    },

    // 处理更新属性
    handleUpdateProperty({ key, value }) {
      if (!this.selectedElement || !this.$refs.bpmnCanvas) return;

      try {
        // 对于Flowable特定属性，需要添加命名空间前缀
        let propertyKey = key;
        if (this.selectedElement.type === 'bpmn:UserTask' &&
            ['assignee', 'candidateUsers', 'candidateGroups'].includes(key)) {
          propertyKey = `flowable:${key}`;
        }

        console.log('更新属性:', this.selectedElement.id, propertyKey, value);

        this.$refs.bpmnCanvas.updateElementProperty(this.selectedElement.id, propertyKey, value);
        this.$set(this.selectedElement, key, value);
      } catch (error) {
        console.error('更新属性失败:', error);
        this.$message.error('更新属性失败: ' + error.message);
      }
    },

    // 处理更新流程属性
    handleUpdateProcessProperty({ key, value }) {
      if (!this.$refs.bpmnCanvas) return;

      try {
        this.$refs.bpmnCanvas.updateProcessProperty(key, value);
        this.updateProcessInfo(key === 'key' ? 'workflowKey' : key, value);
      } catch (error) {
        this.$message.error('更新流程属性失败: ' + error.message);
      }
    },

    // 处理更新条件类型
    handleUpdateConditionType(type) {
      if (!this.selectedElement || !this.$refs.bpmnCanvas) return;

      try {
        this.$refs.bpmnCanvas.updateConditionType(this.selectedElement.id, type);
        this.$set(this.selectedElement, 'conditionType', type);
      } catch (error) {
        this.$message.error('更新条件类型失败: ' + error.message);
      }
    }
  }
};
</script>

<style lang="scss">
.designer-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;
}

.designer-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}
</style>
