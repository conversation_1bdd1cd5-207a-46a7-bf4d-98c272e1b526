<template>
  <div class="simple-workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <el-button size="small" icon="el-icon-back" @click="goBack">返回</el-button>
        <el-divider direction="vertical"></el-divider>
        <span class="process-title">{{ processInfo.workflowName || '新建流程' }}</span>
      </div>

      <div class="header-right">
        <el-button size="small" type="primary" @click="saveProcess">保存流程</el-button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="designer-main">
      <el-row :gutter="20">
        <!-- 左侧流程信息 -->
        <el-col :span="8">
          <el-card class="info-card">
            <div slot="header" class="card-header">
              <span>流程信息</span>
            </div>

            <el-form :model="processInfo" label-width="100px">
              <el-form-item label="流程名称" required>
                <el-input v-model="processInfo.workflowName" placeholder="请输入流程名称" />
              </el-form-item>

              <el-form-item label="流程标识" required>
                <el-input v-model="processInfo.workflowKey" placeholder="请输入流程标识" />
              </el-form-item>

              <el-form-item label="流程分类">
                <el-select v-model="processInfo.workflowCategory" placeholder="选择分类" style="width: 100%;">
                  <el-option label="收文流程" value="receive" />
                  <el-option label="发文流程" value="send" />
                  <el-option label="印章流程" value="seal" />
                  <el-option label="会议流程" value="meeting" />
                  <el-option label="其他流程" value="other" />
                </el-select>
              </el-form-item>

              <el-form-item label="状态">
                <el-radio-group v-model="processInfo.status">
                  <el-radio label="0">停用</el-radio>
                  <el-radio label="1">启用</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="流程描述">
                <el-input v-model="processInfo.description" type="textarea" :rows="3" placeholder="请输入流程描述" />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 右侧设计区域 -->
        <el-col :span="16">
          <el-card class="design-card">
            <div slot="header" class="card-header">
              <span>流程设计</span>
              <div class="design-tools">
                <el-button-group size="mini">
                  <el-button icon="el-icon-document" @click="loadTemplate">选择模板</el-button>
                  <el-button icon="el-icon-view" @click="previewBpmn">预览BPMN</el-button>
                  <el-button icon="el-icon-refresh" @click="resetDesign">重置</el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 可视化设计区域 -->
            <div class="visual-design">
              <div id="bpmn-canvas" ref="canvas" style="height: 500px; border: 1px solid #ddd;"></div>
            </div>

            <!-- 如果BPMN.js不可用，显示备用设计器 -->
            <div v-if="!bpmnAvailable" class="fallback-design">
              <el-alert
                title="正在加载可视化设计器..."
                type="info"
                description="如果长时间未加载，请使用下方的模板选择功能"
                show-icon
                :closable="false"
                style="margin-bottom: 20px;">
              </el-alert>

              <!-- 模板选择 -->
              <div class="template-section">
                <h4>选择流程模板</h4>
                <el-row :gutter="16">
                  <el-col :span="8" v-for="template in templates" :key="template.id">
                    <el-card class="template-card" @click.native="selectTemplate(template)">
                      <div class="template-info">
                        <h5>{{ template.name }}</h5>
                        <p>{{ template.description }}</p>
                        <el-button size="mini" type="primary">使用模板</el-button>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>

              <!-- XML编辑器 -->
              <div class="xml-editor" style="margin-top: 20px;">
                <h4>BPMN XML定义</h4>
                <el-input
                  v-model="processInfo.workflowXml"
                  type="textarea"
                  :rows="15"
                  placeholder="BPMN XML内容将在此显示，您也可以直接编辑"
                />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- BPMN预览对话框 -->
    <el-dialog title="BPMN XML预览" :visible.sync="previewVisible" width="80%" append-to-body>
      <el-input
        v-model="previewXml"
        type="textarea"
        :rows="20"
        readonly
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关 闭</el-button>
        <el-button type="primary" @click="copyXml">复制XML</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addWorkflowDefinition, updateWorkflowDefinition, getWorkflowDefinition } from "@/api/oa/workflow"

// 动态导入BPMN.js
let BpmnModeler = null

export default {
  name: 'SimpleWorkflowDesigner',
  data() {
    return {
      bpmnAvailable: false,
      modeler: null,
      previewVisible: false,
      previewXml: '',
      processInfo: {
        workflowId: null,
        workflowName: '',
        workflowKey: '',
        workflowCategory: '',
        description: '',
        status: '1',
        workflowXml: ''
      },
      templates: [
        {
          id: 1,
          name: '简单审批流程',
          description: '开始 → 审批 → 结束',
          xml: this.getSimpleApprovalTemplate()
        },
        {
          id: 2,
          name: '并行审批流程',
          description: '支持多人同时审批',
          xml: this.getParallelApprovalTemplate()
        },
        {
          id: 3,
          name: '收文审批流程',
          description: '收文专用审批流程',
          xml: this.getReceiveDocumentTemplate()
        }
      ]
    }
  },
  mounted() {
    this.loadProcessData()
    this.initDesigner()
  },
  beforeDestroy() {
    if (this.modeler) {
      this.modeler.destroy()
    }
  },
  methods: {
    // 初始化设计器
    async initDesigner() {
      try {
        // 动态导入BPMN.js
        const BpmnModelerModule = await import('bpmn-js/lib/Modeler')
        BpmnModeler = BpmnModelerModule.default || BpmnModelerModule

        this.modeler = new BpmnModeler({
          container: this.$refs.canvas
        })
        this.bpmnAvailable = true
        this.createNewDiagram()
        console.log('BPMN设计器初始化成功')
      } catch (error) {
        console.warn('BPMN.js加载失败，使用简化模式:', error)
        this.bpmnAvailable = false
        this.$message.info('正在使用简化设计模式，您可以选择模板或直接编辑XML')
      }
    },

    // 创建新图表
    async createNewDiagram() {
      if (!this.modeler) return

      const xml = this.processInfo.workflowXml || this.getSimpleApprovalTemplate()
      try {
        await this.modeler.importXML(xml)
        const canvas = this.modeler.get('canvas')
        canvas.zoom('fit-viewport')
      } catch (error) {
        console.error('导入BPMN失败:', error)
      }
    },

    // 加载流程数据
    loadProcessData() {
      const workflowId = this.$route.query.workflowId
      if (workflowId) {
        getWorkflowDefinition(workflowId).then(response => {
          this.processInfo = response.data
          if (this.processInfo.workflowXml && this.modeler) {
            this.modeler.importXML(this.processInfo.workflowXml)
          }
        })
      } else {
        // 从查询参数加载基本信息
        const query = this.$route.query
        if (query.workflowName) {
          this.processInfo.workflowName = query.workflowName
          this.processInfo.workflowKey = query.workflowKey
          this.processInfo.workflowCategory = query.workflowCategory
          this.processInfo.description = query.description
        }
      }
    },

    // 选择模板
    selectTemplate(template) {
      this.processInfo.workflowXml = template.xml
      if (this.modeler) {
        this.modeler.importXML(template.xml)
      }
      this.$message.success(`已加载模板：${template.name}`)
    },

    // 加载模板
    loadTemplate() {
      this.$message.info('请从下方选择预设模板')
    },

    // 预览BPMN
    async previewBpmn() {
      if (this.modeler) {
        try {
          const { xml } = await this.modeler.saveXML({ format: true })
          this.previewXml = xml
          this.processInfo.workflowXml = xml
        } catch (error) {
          this.previewXml = this.processInfo.workflowXml
        }
      } else {
        this.previewXml = this.processInfo.workflowXml
      }
      this.previewVisible = true
    },

    // 复制XML
    copyXml() {
      const textarea = document.createElement('textarea')
      textarea.value = this.previewXml
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('XML已复制到剪贴板')
    },

    // 重置设计
    resetDesign() {
      this.$confirm('确认重置流程设计？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.processInfo.workflowXml = ''
        if (this.modeler) {
          this.createNewDiagram()
        }
        this.$message.success('已重置')
      })
    },

    // 保存流程
    async saveProcess() {
      if (!this.processInfo.workflowName) {
        this.$message.warning('请输入流程名称')
        return
      }
      if (!this.processInfo.workflowKey) {
        this.$message.warning('请输入流程标识')
        return
      }

      // 如果有BPMN设计器，获取最新的XML
      if (this.modeler) {
        try {
          const { xml } = await this.modeler.saveXML({ format: true })
          this.processInfo.workflowXml = xml
        } catch (error) {
          console.error('获取BPMN XML失败:', error)
        }
      }

      try {
        const isEdit = !!this.processInfo.workflowId
        const apiCall = isEdit ? updateWorkflowDefinition : addWorkflowDefinition

        await apiCall(this.processInfo)
        this.$message.success(isEdit ? '修改成功' : '保存成功')

        if (!isEdit) {
          this.$router.push('/oa/workflow/definition')
        }
      } catch (err) {
        this.$message.error('保存失败')
        console.error(err)
      }
    },

    // 返回
    goBack() {
      this.$router.go(-1)
    },

    // 获取简单审批模板
    getSimpleApprovalTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="SimpleApproval" name="简单审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="ApprovalTask" name="审批">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="ApprovalTask" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="ApprovalTask" targetRef="EndEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="SimpleApproval">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="187" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ApprovalTask_di" bpmnElement="ApprovalTask">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="432" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="440" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="432" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
    },

    // 获取并行审批模板
    getParallelApprovalTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ParallelApproval" name="并行审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:parallelGateway id="ParallelGateway_1" name="分支">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:userTask id="Approval1" name="审批人1">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Approval2" name="审批人2">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:parallelGateway id="ParallelGateway_2" name="汇聚">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="ParallelGateway_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="ParallelGateway_1" targetRef="Approval1" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="ParallelGateway_1" targetRef="Approval2" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Approval1" targetRef="ParallelGateway_2" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Approval2" targetRef="ParallelGateway_2" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="ParallelGateway_2" targetRef="EndEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ParallelApproval">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="187" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_1_di" bpmnElement="ParallelGateway_1">
        <dc:Bounds x="265" y="92" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="280" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Approval1_di" bpmnElement="Approval1">
        <dc:Bounds x="370" y="40" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Approval2_di" bpmnElement="Approval2">
        <dc:Bounds x="370" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ParallelGateway_2_di" bpmnElement="ParallelGateway_2">
        <dc:Bounds x="525" y="92" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="540" y="149" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="632" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="640" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="265" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="290" y="92" />
        <di:waypoint x="290" y="80" />
        <di:waypoint x="370" y="80" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="290" y="142" />
        <di:waypoint x="290" y="190" />
        <di:waypoint x="370" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="470" y="80" />
        <di:waypoint x="550" y="80" />
        <di:waypoint x="550" y="92" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_5_di" bpmnElement="Flow_5">
        <di:waypoint x="470" y="190" />
        <di:waypoint x="550" y="190" />
        <di:waypoint x="550" y="142" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_6_di" bpmnElement="Flow_6">
        <di:waypoint x="575" y="117" />
        <di:waypoint x="632" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
    },

    // 获取收文审批模板
    getReceiveDocumentTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  id="Definitions_1"
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ReceiveDocument" name="收文审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="收文登记">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="DirectorApproval" name="主任审批">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="LeaderApproval" name="领导审批">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="DeptApproval" name="科室处理">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="办结">
      <bpmn:incoming>Flow_4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="DirectorApproval" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="DirectorApproval" targetRef="LeaderApproval" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="LeaderApproval" targetRef="DeptApproval" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="DeptApproval" targetRef="EndEvent_1" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="ReceiveDocument">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="173" y="142" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DirectorApproval_di" bpmnElement="DirectorApproval">
        <dc:Bounds x="270" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="LeaderApproval_di" bpmnElement="LeaderApproval">
        <dc:Bounds x="420" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="DeptApproval_di" bpmnElement="DeptApproval">
        <dc:Bounds x="570" y="77" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="722" y="99" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="730" y="142" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1_di" bpmnElement="Flow_1">
        <di:waypoint x="215" y="117" />
        <di:waypoint x="270" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_2_di" bpmnElement="Flow_2">
        <di:waypoint x="370" y="117" />
        <di:waypoint x="420" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_3_di" bpmnElement="Flow_3">
        <di:waypoint x="520" y="117" />
        <di:waypoint x="570" y="117" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_4_di" bpmnElement="Flow_4">
        <di:waypoint x="670" y="117" />
        <di:waypoint x="722" y="117" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>`
    }
  }
}
</script>

<style scoped>
.simple-workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.designer-header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.process-title {
  font-size: 16px;
  font-weight: 500;
  margin-left: 10px;
}

.designer-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.info-card,
.design-card {
  height: calc(100vh - 140px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.design-tools {
  margin-left: auto;
}

.visual-design {
  margin-bottom: 20px;
}

.template-section h4 {
  margin-bottom: 16px;
  color: #333;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.template-info h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.template-info p {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 12px;
}

.xml-editor h4 {
  margin-bottom: 10px;
  color: #333;
}
</style>
