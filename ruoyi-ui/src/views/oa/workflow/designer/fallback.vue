<template>
  <div class="workflow-designer-fallback">
    <!-- 顶部工具栏 -->
    <div class="designer-header">
      <div class="header-left">
        <el-button size="small" icon="el-icon-back" @click="goBack">返回</el-button>
        <el-divider direction="vertical"></el-divider>
        <span class="process-title">{{ processInfo.workflowName || '新建流程' }}</span>
      </div>
      
      <div class="header-right">
        <el-button size="small" type="primary" @click="saveProcess">保存流程</el-button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="designer-main">
      <el-card class="fallback-card">
        <div slot="header" class="card-header">
          <i class="el-icon-warning" style="color: #E6A23C; margin-right: 8px;"></i>
          <span>流程设计器（简化模式）</span>
        </div>
        
        <el-alert
          title="BPMN.js依赖未安装"
          type="warning"
          description="请运行 'npm install bpmn-js@^11.5.0' 安装依赖后刷新页面使用完整功能"
          show-icon
          :closable="false"
          style="margin-bottom: 20px;">
        </el-alert>

        <el-form :model="processInfo" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="流程名称" required>
                <el-input v-model="processInfo.workflowName" placeholder="请输入流程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="流程标识" required>
                <el-input v-model="processInfo.workflowKey" placeholder="请输入流程标识" />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="流程分类">
                <el-select v-model="processInfo.workflowCategory" placeholder="选择分类" style="width: 100%;">
                  <el-option label="收文流程" value="receive" />
                  <el-option label="发文流程" value="send" />
                  <el-option label="印章流程" value="seal" />
                  <el-option label="会议流程" value="meeting" />
                  <el-option label="其他流程" value="other" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="状态">
                <el-radio-group v-model="processInfo.status">
                  <el-radio label="0">停用</el-radio>
                  <el-radio label="1">启用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="流程描述">
            <el-input v-model="processInfo.description" type="textarea" :rows="3" placeholder="请输入流程描述" />
          </el-form-item>
          
          <el-form-item label="BPMN定义">
            <div class="xml-editor">
              <div class="editor-toolbar">
                <el-button-group size="mini">
                  <el-button icon="el-icon-document" @click="loadTemplate">加载模板</el-button>
                  <el-button icon="el-icon-view" @click="validateXml">验证XML</el-button>
                  <el-button icon="el-icon-refresh" @click="resetXml">重置</el-button>
                </el-button-group>
              </div>
              <el-input
                v-model="processInfo.workflowXml"
                type="textarea"
                :rows="20"
                placeholder="请输入BPMN XML定义，或点击'加载模板'使用预设模板"
                class="xml-textarea"
              />
            </div>
          </el-form-item>
        </el-form>
        
        <!-- 预设模板选择 -->
        <div class="template-section">
          <h4>预设流程模板</h4>
          <el-row :gutter="16">
            <el-col :span="8" v-for="template in templates" :key="template.id">
              <el-card class="template-card" @click.native="selectTemplate(template)">
                <div class="template-info">
                  <h5>{{ template.name }}</h5>
                  <p>{{ template.description }}</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { addWorkflowDefinition, updateWorkflowDefinition, getWorkflowDefinition } from "@/api/oa/workflow"

export default {
  name: 'WorkflowDesignerFallback',
  data() {
    return {
      processInfo: {
        workflowId: null,
        workflowName: '',
        workflowKey: '',
        workflowCategory: '',
        description: '',
        status: '1',
        workflowXml: ''
      },
      templates: [
        {
          id: 1,
          name: '简单审批流程',
          description: '开始 → 审批 → 结束',
          xml: this.getSimpleApprovalTemplate()
        },
        {
          id: 2,
          name: '并行审批流程',
          description: '支持多人同时审批',
          xml: this.getParallelApprovalTemplate()
        },
        {
          id: 3,
          name: '收文审批流程',
          description: '收文专用审批流程',
          xml: this.getReceiveDocumentTemplate()
        }
      ]
    }
  },
  mounted() {
    this.loadProcessData()
  },
  methods: {
    // 加载流程数据
    loadProcessData() {
      const workflowId = this.$route.query.workflowId
      if (workflowId) {
        getWorkflowDefinition(workflowId).then(response => {
          this.processInfo = response.data
        })
      } else {
        // 从查询参数加载基本信息
        const query = this.$route.query
        if (query.workflowName) {
          this.processInfo.workflowName = query.workflowName
          this.processInfo.workflowKey = query.workflowKey
          this.processInfo.workflowCategory = query.workflowCategory
          this.processInfo.description = query.description
        }
      }
    },

    // 加载模板
    loadTemplate() {
      this.$message.info('请从下方选择预设模板')
    },

    // 选择模板
    selectTemplate(template) {
      this.processInfo.workflowXml = template.xml
      this.$message.success(`已加载模板：${template.name}`)
    },

    // 验证XML
    validateXml() {
      if (!this.processInfo.workflowXml.trim()) {
        this.$message.warning('请输入BPMN XML内容')
        return
      }
      
      try {
        // 简单的XML格式验证
        const parser = new DOMParser()
        const doc = parser.parseFromString(this.processInfo.workflowXml, 'text/xml')
        const errors = doc.getElementsByTagName('parsererror')
        
        if (errors.length > 0) {
          this.$message.error('XML格式错误，请检查语法')
        } else {
          this.$message.success('XML格式验证通过')
        }
      } catch (error) {
        this.$message.error('XML验证失败：' + error.message)
      }
    },

    // 重置XML
    resetXml() {
      this.$confirm('确认重置BPMN定义？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.processInfo.workflowXml = ''
        this.$message.success('已重置')
      })
    },

    // 保存流程
    async saveProcess() {
      if (!this.processInfo.workflowName) {
        this.$message.warning('请输入流程名称')
        return
      }
      if (!this.processInfo.workflowKey) {
        this.$message.warning('请输入流程标识')
        return
      }

      try {
        const isEdit = !!this.processInfo.workflowId
        const apiCall = isEdit ? updateWorkflowDefinition : addWorkflowDefinition
        
        await apiCall(this.processInfo)
        this.$message.success(isEdit ? '修改成功' : '保存成功')
        
        if (!isEdit) {
          this.$router.push('/oa/workflow/definition')
        }
      } catch (err) {
        this.$message.error('保存失败')
        console.error(err)
      }
    },

    // 返回
    goBack() {
      this.$router.go(-1)
    },

    // 获取简单审批模板
    getSimpleApprovalTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="SimpleApproval" name="简单审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="ApprovalTask" name="审批">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_2</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="ApprovalTask" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="ApprovalTask" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`
    },

    // 获取并行审批模板
    getParallelApprovalTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ParallelApproval" name="并行审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:parallelGateway id="ParallelGateway_1" name="并行网关">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:userTask id="Approval1" name="审批人1">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Approval2" name="审批人2">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:parallelGateway id="ParallelGateway_2" name="汇聚网关">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="ParallelGateway_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="ParallelGateway_1" targetRef="Approval1" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="ParallelGateway_1" targetRef="Approval2" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="Approval1" targetRef="ParallelGateway_2" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Approval2" targetRef="ParallelGateway_2" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="ParallelGateway_2" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`
    },

    // 获取收文审批模板
    getReceiveDocumentTemplate() {
      return `<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="ReceiveDocument" name="收文审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="收文登记">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="DirectorApproval" name="主任审批">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="LeaderApproval" name="领导审批">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="DeptApproval" name="科室处理">
      <bpmn:incoming>Flow_3</bpmn:incoming>
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="办结">
      <bpmn:incoming>Flow_4</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="DirectorApproval" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="DirectorApproval" targetRef="LeaderApproval" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="LeaderApproval" targetRef="DeptApproval" />
    <bpmn:sequenceFlow id="Flow_4" sourceRef="DeptApproval" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>`
    }
  }
}
</script>

<style scoped>
.workflow-designer-fallback {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.designer-header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.process-title {
  font-size: 16px;
  font-weight: 500;
  margin-left: 10px;
}

.designer-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.fallback-card {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.xml-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  padding: 10px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.xml-textarea {
  border: none;
}

.xml-textarea .el-textarea__inner {
  border: none;
  border-radius: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.template-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e6e6e6;
}

.template-section h4 {
  margin-bottom: 16px;
  color: #333;
}

.template-card {
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 16px;
}

.template-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.template-info h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.template-info p {
  margin: 0;
  color: #666;
  font-size: 12px;
}
</style>
