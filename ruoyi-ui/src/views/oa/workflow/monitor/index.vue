<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="workflowName">
        <el-input
          v-model="queryParams.workflowName"
          placeholder="请输入流程名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="发起人" prop="startUserName">
        <el-input
          v-model="queryParams.startUserName"
          placeholder="请输入发起人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="流程实例ID" prop="processInstanceId">
        <el-input
          v-model="queryParams.processInstanceId"
          placeholder="请输入流程实例ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="进行中" value="1" />
          <el-option label="已完成" value="2" />
          <el-option label="已终止" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="instanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="流程实例ID" align="center" prop="instanceId" />
      <el-table-column label="业务主键" align="center" prop="businessKey" />
      <el-table-column label="流程名称" align="center" prop="workflowName" width="150" />
      <el-table-column label="发起人" align="center" prop="startUserName" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.startUserName || '未知用户' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="当前任务" align="center" prop="currentTaskName" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.currentTaskName">{{ scope.row.currentTaskName }}</span>
          <span v-else style="color: #999;">-</span>
        </template>
      </el-table-column>
      <el-table-column label="当前处理人" align="center" prop="currentAssignee" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.currentAssignee && scope.row.currentAssignee !== '-'">{{ scope.row.currentAssignee }}</span>
          <span v-else-if="scope.row.status == '2'" style="color: #67C23A;">已完成</span>
          <span v-else-if="scope.row.status == '3'" style="color: #F56C6C;">已终止</span>
          <span v-else style="color: #999;">待分配</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="150">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span v-if="scope.row.endTime">{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '1'" type="primary">进行中</el-tag>
          <el-tag v-else-if="scope.row.status == '2'" type="success">已完成</el-tag>
          <el-tag v-else-if="scope.row.status == '3'" type="danger">已终止</el-tag>
          <el-tag v-else type="info">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleTrack(scope.row)"
          >审批轨迹</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />



    <!-- 审批轨迹对话框 -->
    <el-dialog title="审批轨迹" :visible.sync="historyOpen" width="800px" append-to-body>
      <div v-loading="historyLoading">
        <el-timeline>
          <el-timeline-item
            v-for="(item, index) in historyList"
            :key="index"
            :timestamp="parseTime(item.createTime, '{y}-{m}-{d} {h}:{i}:{s}')"
            placement="top"
          >
            <el-card>
              <h4>{{ item.taskName }}</h4>
              <p>处理人：{{ item.assigneeName }}</p>
              <p v-if="item.comment">处理意见：{{ item.comment }}</p>
              <p v-if="item.completeTime">完成时间：{{ parseTime(item.completeTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</p>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-dialog>



    <!-- 终止流程对话框 -->
    <el-dialog title="终止流程" :visible.sync="terminateOpen" width="500px" append-to-body>
      <el-form ref="terminateForm" :model="terminateForm" :rules="terminateRules" label-width="100px">
        <el-form-item label="终止原因" prop="reason">
          <el-input v-model="terminateForm.reason" type="textarea" :rows="4" placeholder="请输入终止原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTerminate">确 定</el-button>
        <el-button @click="cancelTerminate">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getInstanceListForMonitor, getProcessHistory, getProcessApprovalInfo, terminateProcess } from "@/api/oa/workflow";

export default {
  name: "WorkflowMonitor",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 流程实例表格数据
      instanceList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workflowName: null,
        startUserName: null,
        status: null,
        processInstanceId: null
      },

      // 流程历史相关
      historyOpen: false,
      historyList: [],
      historyLoading: false,


      // 终止流程相关
      terminateOpen: false,
      terminateForm: {
        reason: '',
        instanceId: null
      },
      terminateRules: {
        reason: [
          { required: true, message: "终止原因不能为空", trigger: "blur" }
        ]
      }

    };
  },
  created() {
    console.log('监控页面初始化，路由参数:', this.$route.query);
    const processInstanceId = this.$route.query && this.$route.query.processInstanceId;
    if (processInstanceId) {
      this.queryParams.processInstanceId = processInstanceId;
      console.log('监控页面接收到流程实例ID:', processInstanceId);
      console.log('设置查询参数:', this.queryParams);
    } else {
      console.log('未接收到流程实例ID参数');
    }
    this.getList();
  },
  methods: {
    /** 查询流程实例列表 */
    getList() {
      this.loading = true;
      console.log('监控页面查询参数:', this.queryParams);
      getInstanceListForMonitor(this.queryParams).then(response => {
        console.log('监控页面API响应:', response);
        this.instanceList = response.rows || [];
        this.total = response.total || 0;
        this.loading = false;
        console.log('监控页面数据加载完成，实例数量:', this.instanceList.length);
      }).catch(error => {
        console.error('监控页面数据加载失败:', error);
        this.loading = false;
        this.$modal.msgError("加载流程实例失败: " + (error.message || error));
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.instanceId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },





    /** 查看流程历史 */
    handleHistory(row) {
      getProcessHistory(row.processInstanceId).then(response => {
        this.historyList = response.data;
        this.historyOpen = true;
      });
    },
    /** 审批轨迹 */
    handleTrack(row) {
      this.historyOpen = true; // 使用已有的 historyOpen 变量
      this.historyLoading = true;
      this.historyList = []; // 清空当前历史列表
      getProcessHistory(row.processInstanceId).then(response => {
        this.historyList = response.data;
        this.historyOpen = true;
        this.historyLoading = false;
      }).catch(() => {
        this.historyLoading = false;
      });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/workflow/instance/export', {
        ...this.queryParams
      }, `workflow_instance_${new Date().getTime()}.xlsx`)
    },

    /** 终止流程 */
    handleTerminate(row) {
      this.terminateForm = {
        reason: '',
        instanceId: row.instanceId
      };
      this.terminateOpen = true;
    },

    /** 提交终止 */
    submitTerminate() {
      this.$refs["terminateForm"].validate(valid => {
        if (valid) {
          this.$modal.confirm('是否确认终止该流程实例？').then(() => {
            const loading = this.$loading({
              text: '正在终止流程...',
              background: 'rgba(0, 0, 0, 0.7)'
            });

            terminateProcess(this.terminateForm.instanceId, {
              reason: this.terminateForm.reason
            }).then(() => {
              loading.close();
              this.$modal.msgSuccess("终止成功");
              this.terminateOpen = false;
              this.getList();
            }).catch(() => {
              loading.close();
              this.$modal.msgError("终止失败");
            });
          });
        }
      });
    },

    /** 取消终止 */
    cancelTerminate() {
      this.terminateOpen = false;
      this.terminateForm = {
        reason: '',
        instanceId: null
      };
    }
  }
};
</script>

<style scoped>
.el-timeline-item__timestamp {
  color: #909399;
  line-height: 1;
  font-size: 13px;
}
</style>
