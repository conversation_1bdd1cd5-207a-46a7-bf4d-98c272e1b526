<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会议室名称" prop="roomName">
        <el-input
          v-model="queryParams.roomName"
          placeholder="请输入会议室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="楼层" prop="floor">
        <el-input
          v-model="queryParams.floor"
          placeholder="请输入楼层"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['oa:meeting:room:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['oa:meeting:room:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['oa:meeting:room:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['oa:meeting:room:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="roomList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会议室名称" align="center" prop="roomName" />
      <el-table-column label="容纳人数" align="center" prop="capacity" />
      <el-table-column label="楼层" align="center" prop="floor" />
      <el-table-column label="位置" align="center" prop="location" />
      <el-table-column label="设备" align="center" prop="equipment" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="1"
            inactive-value="0"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['oa:meeting:room:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['oa:meeting:room:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-date"
            @click="handleSchedule(scope.row)"
            v-hasPermi="['oa:meeting:room:schedule']"
          >预约情况</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['oa:meeting:room:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会议室对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="会议室名称" prop="roomName">
              <el-input v-model="form.roomName" placeholder="请输入会议室名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="容纳人数" prop="capacity">
              <el-input-number v-model="form.capacity" :min="1" :max="1000" placeholder="请输入容纳人数" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="楼层" prop="floor">
              <el-input v-model="form.floor" placeholder="请输入楼层" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入位置" />
        </el-form-item>
        <el-form-item label="设备" prop="equipment">
          <el-checkbox-group v-model="form.equipmentList">
            <el-checkbox label="投影仪">投影仪</el-checkbox>
            <el-checkbox label="音响">音响</el-checkbox>
            <el-checkbox label="白板">白板</el-checkbox>
            <el-checkbox label="电视">电视</el-checkbox>
            <el-checkbox label="视频会议">视频会议</el-checkbox>
            <el-checkbox label="空调">空调</el-checkbox>
            <el-checkbox label="WiFi">WiFi</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入会议室描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看会议室详情对话框 -->
    <el-dialog title="会议室详情" :visible.sync="viewOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会议室名称">{{ viewForm.roomName }}</el-descriptions-item>
        <el-descriptions-item label="容纳人数">{{ viewForm.capacity }}人</el-descriptions-item>
        <el-descriptions-item label="楼层">{{ viewForm.floor }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag v-if="viewForm.status == '1'" type="success">启用</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="位置" :span="2">{{ viewForm.location }}</el-descriptions-item>
        <el-descriptions-item label="设备" :span="2">{{ viewForm.equipment }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ viewForm.description }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ viewForm.remark }}</el-descriptions-item>
        <el-descriptions-item label="创建时间" :span="2">{{ parseTime(viewForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 会议室预约情况对话框 -->
    <el-dialog title="会议室预约情况" :visible.sync="scheduleOpen" width="800px" append-to-body>
      <el-calendar v-model="scheduleDate">
        <template slot="dateCell" slot-scope="{date, data}">
          <div class="calendar-cell">
            <p>{{ data.day.split('-').slice(2).join('-') }}</p>
            <div v-for="meeting in getDateMeetings(data.day)" :key="meeting.meetingId" class="meeting-item">
              <el-tag size="mini" :type="getMeetingTagType(meeting.status)">
                {{ meeting.meetingTitle }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-calendar>
    </el-dialog>
  </div>
</template>

<script>
import { listMeetingRoom, getMeetingRoom, delMeetingRoom, addMeetingRoom, updateMeetingRoom, updateMeetingRoomStatus, getRoomSchedule } from "@/api/oa/meeting";

export default {
  name: "MeetingRoom",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会议室表格数据
      roomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示查看弹出层
      viewOpen: false,
      // 是否显示预约情况弹出层
      scheduleOpen: false,
      // 预约日期
      scheduleDate: new Date(),
      // 会议室预约数据
      roomScheduleData: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        roomName: null,
        status: null,
        floor: null
      },
      // 表单参数
      form: {},
      // 查看表单参数
      viewForm: {},
      // 表单校验
      rules: {
        roomName: [
          { required: true, message: "会议室名称不能为空", trigger: "blur" }
        ],
        capacity: [
          { required: true, message: "容纳人数不能为空", trigger: "blur" }
        ],
        floor: [
          { required: true, message: "楼层不能为空", trigger: "blur" }
        ],
        location: [
          { required: true, message: "位置不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会议室列表 */
    getList() {
      this.loading = true;
      listMeetingRoom(this.queryParams).then(response => {
        this.roomList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        roomId: null,
        roomName: null,
        capacity: null,
        floor: null,
        location: null,
        equipment: null,
        equipmentList: [],
        description: null,
        status: "1",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.roomId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会议室";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const roomId = row.roomId || this.ids
      getMeetingRoom(roomId).then(response => {
        this.form = response.data;
        // 处理设备列表
        if (this.form.equipment) {
          this.form.equipmentList = this.form.equipment.split(',');
        }
        this.open = true;
        this.title = "修改会议室";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      getMeetingRoom(row.roomId).then(response => {
        this.viewForm = response.data;
        this.viewOpen = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 处理设备列表
          this.form.equipment = this.form.equipmentList.join(',');
          
          if (this.form.roomId != null) {
            updateMeetingRoom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMeetingRoom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const roomIds = row.roomId || this.ids;
      this.$modal.confirm('是否确认删除会议室编号为"' + roomIds + '"的数据项？').then(function() {
        return delMeetingRoom(roomIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 状态修改 */
    handleStatusChange(row) {
      let text = row.status === "1" ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.roomName + '"会议室吗？').then(function() {
        return updateMeetingRoomStatus(row.roomId, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.status = row.status === "0" ? "1" : "0";
      });
    },
    /** 预约情况 */
    handleSchedule(row) {
      getRoomSchedule(row.roomId).then(response => {
        this.roomScheduleData = response.data;
        this.scheduleOpen = true;
      });
    },
    /** 获取指定日期的会议 */
    getDateMeetings(date) {
      return this.roomScheduleData.filter(meeting => {
        const meetingDate = meeting.startTime.split(' ')[0];
        return meetingDate === date;
      });
    },
    /** 获取会议标签类型 */
    getMeetingTagType(status) {
      const typeMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger'
      };
      return typeMap[status] || 'info';
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('oa/meeting/room/export', {
        ...this.queryParams
      }, `meeting_room_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.calendar-cell {
  height: 80px;
  overflow: hidden;
}

.meeting-item {
  margin: 2px 0;
}

.meeting-item .el-tag {
  width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
