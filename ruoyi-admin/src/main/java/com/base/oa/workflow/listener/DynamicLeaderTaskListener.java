package com.base.oa.workflow.listener;

import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * 动态分管领导任务监听器
 * 根据书记选择的分管领导动态分配任务
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Component("dynamicLeaderTaskListener")
public class DynamicLeaderTaskListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            String taskId = delegateTask.getId();
            String processInstanceId = delegateTask.getProcessInstanceId();

            System.out.println("动态分管领导任务创建: " + taskId + ", 流程实例: " + processInstanceId);

            // 从流程变量中获取当前多实例的分管领导
            Map<String, Object> variables = delegateTask.getVariables();
            Object currentLeaderVar = variables.get("currentLeader");

            if (currentLeaderVar != null) {
                String currentLeader = currentLeaderVar.toString().trim();
                System.out.println("当前多实例分管领导: " + currentLeader);

                // 清除默认的候选组，确保权限隔离
                delegateTask.getCandidates().clear();

                // 只为当前分管领导分配任务
                delegateTask.setAssignee(currentLeader);
                System.out.println("分配分管领导任务给: " + currentLeader);

            } else {
                // 如果没有currentLeader变量，尝试从selectedLeaders中获取
                Object selectedLeadersVar = variables.get("selectedLeaders");

                if (selectedLeadersVar != null) {
                    List<String> selectedLeaders = null;

                    // 处理不同的数据格式
                    if (selectedLeadersVar instanceof List) {
                        selectedLeaders = (List<String>) selectedLeadersVar;
                    } else if (selectedLeadersVar instanceof String) {
                        String leadersStr = (String) selectedLeadersVar;
                        selectedLeaders = java.util.Arrays.asList(leadersStr.split(","));
                    }

                    if (selectedLeaders != null && !selectedLeaders.isEmpty()) {
                        System.out.println("书记选择的分管领导: " + selectedLeaders);

                        // 清除默认的候选组，确保权限隔离
                        delegateTask.getCandidates().clear();

                        // 只为选择的分管领导分配任务
                        for (String leaderId : selectedLeaders) {
                            leaderId = leaderId.trim(); // 去除空格
                            if (!leaderId.isEmpty()) {
                                delegateTask.addCandidateUser(leaderId);
                                System.out.println("分配分管领导任务给: " + leaderId);
                            }
                        }

                        System.out.println("分管领导任务权限隔离设置完成，只有选择的 " + selectedLeaders.size() + " 个分管领导能看到任务");
                    } else {
                        System.out.println("selectedLeaders为空，使用默认分配策略");
                        // 如果没有选择，分配给所有分管领导
                        delegateTask.addCandidateGroup("fgld");
                    }
                } else {
                    System.out.println("未找到selectedLeaders变量，使用默认分配策略");
                    // 如果没有选择，分配给所有分管领导
                    delegateTask.addCandidateGroup("fgld");
                }
            }

        } catch (Exception e) {
            System.err.println("动态分管领导任务监听器执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
