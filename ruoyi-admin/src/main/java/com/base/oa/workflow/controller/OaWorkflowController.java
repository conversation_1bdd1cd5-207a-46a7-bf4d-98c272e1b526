package com.base.oa.workflow.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.domain.OaWorkflowInstance;
import com.base.oa.workflow.dto.WorkflowTaskDTO;
import com.base.oa.workflow.service.IOaWorkflowService;
import com.base.common.core.domain.entity.SysUser;
import com.base.system.service.ISysUserService;

/**
 * 工作流管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/oa/workflow")
public class OaWorkflowController extends BaseController
{
    @Autowired
    private IOaWorkflowService oaWorkflowService;

    @Autowired
    private ISysUserService userService;

    /**
     * 查询工作流定义列表
     */
    @GetMapping("/definition/list")
    public TableDataInfo list(OaWorkflowDefinition oaWorkflowDefinition)
    {
        startPage();
        List<OaWorkflowDefinition> list = oaWorkflowService.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
        return getDataTable(list);
    }

    /**
     * 导出工作流定义列表
     */
    @Log(title = "工作流定义", businessType = BusinessType.EXPORT)
    @PostMapping("/definition/export")
    public void export(HttpServletResponse response, OaWorkflowDefinition oaWorkflowDefinition)
    {
        List<OaWorkflowDefinition> list = oaWorkflowService.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
        ExcelUtil<OaWorkflowDefinition> util = new ExcelUtil<OaWorkflowDefinition>(OaWorkflowDefinition.class);
        util.exportExcel(response, list, "工作流定义数据");
    }

    /**
     * 获取工作流定义详细信息
     */
    @GetMapping(value = "/definition/{workflowId}")
    public AjaxResult getInfo(@PathVariable("workflowId") Long workflowId)
    {
        return success(oaWorkflowService.selectOaWorkflowDefinitionByWorkflowId(workflowId));
    }

    /**
     * 新增工作流定义
     */
    @Log(title = "工作流定义", businessType = BusinessType.INSERT)
    @PostMapping("/definition")
    public AjaxResult add(@RequestBody OaWorkflowDefinition oaWorkflowDefinition)
    {
        return toAjax(oaWorkflowService.insertOaWorkflowDefinition(oaWorkflowDefinition));
    }

    /**
     * 修改工作流定义
     */
    @Log(title = "工作流定义", businessType = BusinessType.UPDATE)
    @PutMapping("/definition")
    public AjaxResult edit(@RequestBody OaWorkflowDefinition oaWorkflowDefinition)
    {
        return toAjax(oaWorkflowService.updateOaWorkflowDefinition(oaWorkflowDefinition));
    }

    /**
     * 删除工作流定义
     */
    @Log(title = "工作流定义", businessType = BusinessType.DELETE)
    @DeleteMapping("/definition/{workflowIds}")
    public AjaxResult remove(@PathVariable Long[] workflowIds)
    {
        return toAjax(oaWorkflowService.deleteOaWorkflowDefinitionByWorkflowIds(workflowIds));
    }

    /**
     * 启动工作流程
     */
    @Log(title = "启动流程", businessType = BusinessType.INSERT)
    @PostMapping("/process/start")
    public AjaxResult startProcess(@RequestBody Map<String, Object> params)
    {
        String workflowKey = (String) params.get("workflowKey");
        String businessKey = (String) params.get("businessKey");
        Map<String, Object> variables = (Map<String, Object>) params.get("variables");

        String processInstanceId = oaWorkflowService.startProcess(workflowKey, businessKey, variables);
        if (processInstanceId != null) {
            return AjaxResult.success("流程启动成功").put("processInstanceId", processInstanceId);
        } else {
            return error("流程启动失败");
        }
    }

    /**
     * 完成任务
     */
    @Log(title = "完成任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/complete/{taskId}")
    public AjaxResult completeTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        Map<String, Object> variables = (Map<String, Object>) params.get("variables");
        String comment = (String) params.get("comment");

        boolean result = oaWorkflowService.completeTask(taskId, variables, comment);
        return result ? success("任务完成成功") : error("任务完成失败");
    }

    /**
     * 拒绝任务（直接结束流程并恢复文档状态为草稿）
     */
    @Log(title = "拒绝任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/reject/{taskId}")
    public AjaxResult rejectTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        String comment = (String) params.get("comment");

        Map<String, Object> variables = new HashMap<>();
        variables.put("result", "reject");

        boolean result = oaWorkflowService.completeTask(taskId, variables, comment);
        return result ? success("任务拒绝成功，流程已结束") : error("任务拒绝失败");
    }

    /**
     * 回退任务（回退到上一个审批节点）
     */
    @Log(title = "回退任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/rollback/{taskId}")
    public AjaxResult rollbackTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        String comment = (String) params.get("comment");

        Map<String, Object> variables = new HashMap<>();
        variables.put("result", "return");

        boolean result = oaWorkflowService.completeTask(taskId, variables, comment);
        return result ? success("任务回退成功") : error("任务回退失败");
    }

    /**
     * 回退任务（回退到上一个审批节点）
     */
    @Log(title = "回退任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/return/{taskId}")
    public AjaxResult returnTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        String comment = (String) params.get("comment");

        Map<String, Object> variables = new HashMap<>();
        variables.put("result", "return");

        boolean result = oaWorkflowService.completeTask(taskId, variables, comment);
        return result ? success("任务回退成功") : error("任务回退失败");
    }

    /**
     * 获取可选择的分管领导列表
     */
    @GetMapping("/personnel/leaders")
    public AjaxResult getAvailableLeaders()
    {
        try {
            List<Map<String, Object>> leaders = oaWorkflowService.getAvailableLeaders();
            return success(leaders);
        } catch (Exception e) {
            logger.error("获取分管领导列表失败", e);
            return error("获取分管领导列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定分管领导下的科室负责人列表
     */
    @GetMapping("/personnel/managers/{leaderId}")
    public AjaxResult getAvailableManagers(@PathVariable String leaderId)
    {
        try {
            List<Map<String, Object>> managers = oaWorkflowService.getAvailableManagers(leaderId);
            return success(managers);
        } catch (Exception e) {
            logger.error("获取科室负责人列表失败", e);
            return error("获取科室负责人列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定科室负责人下的经办人列表
     */
    @GetMapping("/personnel/handlers/{managerId}")
    public AjaxResult getAvailableHandlers(@PathVariable String managerId)
    {
        try {
            List<Map<String, Object>> handlers = oaWorkflowService.getAvailableHandlers(managerId);
            return success(handlers);
        } catch (Exception e) {
            logger.error("获取经办人列表失败", e);
            return error("获取经办人列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有可分配的人员列表（用于特办流程）
     */
    @GetMapping("/personnel/all")
    public AjaxResult getAllAvailablePersonnel()
    {
        try {
            List<Map<String, Object>> personnel = oaWorkflowService.getAllAvailablePersonnel();
            return success(personnel);
        } catch (Exception e) {
            logger.error("获取人员列表失败", e);
            return error("获取人员列表失败: " + e.getMessage());
        }
    }

    /**
     * 催办任务
     */
    @Log(title = "催办任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/urge/{taskId}")
    public AjaxResult urgeTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        try {
            String message = (String) params.get("message");
            boolean result = oaWorkflowService.urgeTask(taskId, message);
            return result ? success("催办成功") : error("催办失败");
        } catch (Exception e) {
            logger.error("催办任务失败", e);
            return error("催办失败: " + e.getMessage());
        }
    }

    /**
     * 撤回流程
     */
    @Log(title = "撤回流程", businessType = BusinessType.UPDATE)
    @PostMapping("/withdraw/{businessKey}")
    public AjaxResult withdrawProcess(@PathVariable String businessKey, @RequestBody Map<String, Object> params)
    {
        try {
            String reason = (String) params.get("reason");
            boolean result = oaWorkflowService.withdrawProcess(businessKey, reason);
            return result ? success("流程撤回成功") : error("流程撤回失败");
        } catch (Exception e) {
            logger.error("撤回流程失败", e);
            return error("撤回流程失败: " + e.getMessage());
        }
    }

    /**
     * 查询待办任务列表
     */
    @GetMapping("/task/todo")
    public TableDataInfo todoTasks(WorkflowTaskDTO taskQuery)
    {
        Long userId = getUserId();
        List<WorkflowTaskDTO> list = oaWorkflowService.selectTodoTasks(userId, taskQuery);
        return getDataTable(list);
    }

    /**
     * 查询已办任务列表
     */
    @GetMapping("/task/done")
    public TableDataInfo doneTasks()
    {
        Long userId = getUserId();
        List<WorkflowTaskDTO> list = oaWorkflowService.selectDoneTasks(userId);
        return getDataTable(list);
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/task/{taskId}")
    public AjaxResult getTaskDetail(@PathVariable String taskId)
    {
        try {
            Map<String, Object> taskDetail = oaWorkflowService.getTaskDetail(taskId);
            return success(taskDetail);
        } catch (Exception e) {
            return error("获取任务详情失败: " + e.getMessage());
        }
    }

    /**
     * 查询流程实例列表
     */
    @GetMapping("/instance/list")
    public TableDataInfo instanceList(OaWorkflowInstance oaWorkflowInstance)
    {
        startPage();
        List<OaWorkflowInstance> list = oaWorkflowService.selectOaWorkflowInstanceList(oaWorkflowInstance);
        return getDataTable(list);
    }

    /**
     * 获取流程实例详细信息
     */
    @GetMapping("/instance/{instanceId}")
    public AjaxResult getInstanceInfo(@PathVariable("instanceId") Long instanceId)
    {
        return success(oaWorkflowService.selectOaWorkflowInstanceByInstanceId(instanceId));
    }

    /**
     * 终止流程实例
     */
    @Log(title = "终止流程", businessType = BusinessType.UPDATE)
    @PostMapping("/instance/terminate/{instanceId}")
    public AjaxResult terminateProcess(@PathVariable Long instanceId, @RequestBody Map<String, Object> params)
    {
        String reason = (String) params.get("reason");
        boolean result = oaWorkflowService.terminateProcess(instanceId, reason);
        return result ? success("流程终止成功") : error("流程终止失败");
    }

    /**
     * 转办任务
     */
    @Log(title = "转办任务", businessType = BusinessType.UPDATE)
    @PostMapping("/task/delegate/{taskId}")
    public AjaxResult delegateTask(@PathVariable String taskId, @RequestBody Map<String, Object> params)
    {
        // 支持两种参数名：assignee（前端传递的用户名）和targetUserId（用户ID）
        String assignee = (String) params.get("assignee");
        Object targetUserIdObj = params.get("targetUserId");
        String comment = (String) params.get("comment");

        Long targetUserId = null;
        if (targetUserIdObj != null) {
            targetUserId = Long.valueOf(targetUserIdObj.toString());
        } else if (assignee != null) {
            // 根据用户名查找用户ID
            SysUser user = userService.selectUserByUserName(assignee);
            if (user != null) {
                targetUserId = user.getUserId();
            }
        }

        if (targetUserId == null) {
            return error("转办人参数错误");
        }

        boolean result = oaWorkflowService.delegateTask(taskId, targetUserId, comment);
        return result ? success("任务转办成功") : error("任务转办失败");
    }

    /**
     * 获取流程图
     */
    @GetMapping("/diagram/{processInstanceId}")
    public AjaxResult getProcessDiagram(@PathVariable String processInstanceId)
    {
        byte[] diagram = oaWorkflowService.getProcessDiagram(processInstanceId);
        if (diagram != null) {
            return AjaxResult.success("获取流程图成功").put("diagram", diagram);
        } else {
            return error("获取流程图失败");
        }
    }

    /**
     * 查询流程历史
     */
    @GetMapping("/history/{processInstanceId}")
    public AjaxResult getProcessHistory(@PathVariable String processInstanceId)
    {
        List<WorkflowTaskDTO> history = oaWorkflowService.selectProcessHistory(processInstanceId);
        return success(history);
    }

    /**
     * 获取流程审批信息
     */
    @GetMapping("/approval/{processInstanceId}")
    public AjaxResult getProcessApprovalInfo(@PathVariable String processInstanceId)
    {
        Map<String, Object> approvalInfo = oaWorkflowService.getProcessApprovalInfo(processInstanceId);
        return success(approvalInfo);
    }

    /**
     * 获取可退回的节点列表
     */
    @GetMapping("/task/returnNodes/{taskId}")
    public AjaxResult getReturnNodes(@PathVariable String taskId)
    {
        try {
            List<Map<String, Object>> returnNodes = oaWorkflowService.getReturnNodes(taskId);
            return success(returnNodes);
        } catch (Exception e) {
            return error("获取可退回节点失败: " + e.getMessage());
        }
    }

    /**
     * 性能测试接口 - 待办任务查询
     */
    @GetMapping("/performance/todo")
    public AjaxResult performanceTodoTest()
    {
        long startTime = System.currentTimeMillis();
        Long userId = getUserId();
        List<WorkflowTaskDTO> list = oaWorkflowService.selectTodoTasks(userId, null);
        long endTime = System.currentTimeMillis();

        return success()
            .put("taskCount", list.size())
            .put("queryTime", endTime - startTime)
            .put("message", "待办任务查询性能测试完成");
    }

    /**
     * 性能测试接口 - 已办任务查询
     */
    @GetMapping("/performance/done")
    public AjaxResult performanceDoneTest()
    {
        long startTime = System.currentTimeMillis();
        Long userId = getUserId();
        List<WorkflowTaskDTO> list = oaWorkflowService.selectDoneTasks(userId);
        long endTime = System.currentTimeMillis();

        return success()
            .put("taskCount", list.size())
            .put("queryTime", endTime - startTime)
            .put("message", "已办任务查询性能测试完成");
    }

    /**
     * 性能测试接口 - 流程监控查询
     */
    @GetMapping("/performance/monitor")
    public AjaxResult performanceMonitorTest()
    {
        long startTime = System.currentTimeMillis();
        OaWorkflowInstance queryParam = new OaWorkflowInstance();
        List<OaWorkflowInstance> list = oaWorkflowService.selectOaWorkflowInstanceList(queryParam);
        long endTime = System.currentTimeMillis();

        return success()
            .put("instanceCount", list.size())
            .put("queryTime", endTime - startTime)
            .put("message", "流程监控查询性能测试完成");
    }

    /**
     * 获取下一步处理人列表
     */
    @GetMapping("/task/nextAssignees/{taskId}")
    public AjaxResult getNextAssignees(@PathVariable String taskId)
    {
        try {
            List<Map<String, Object>> nextAssignees = oaWorkflowService.getNextAssignees(taskId);
            return success(nextAssignees);
        } catch (Exception e) {
            return error("获取下一步处理人失败: " + e.getMessage());
        }
    }

    /**
     * 部署流程定义
     */
    @Log(title = "部署流程", businessType = BusinessType.INSERT)
    @PostMapping("/deploy")
    public AjaxResult deployProcess(@RequestBody Map<String, Object> params)
    {
        String workflowName = (String) params.get("workflowName");
        String workflowKey = (String) params.get("workflowKey");
        String bpmnXml = (String) params.get("bpmnXml");

        String deploymentId = oaWorkflowService.deployProcess(workflowName, workflowKey, bpmnXml);
        if (deploymentId != null) {
            return AjaxResult.success("流程部署成功").put("deploymentId", deploymentId);
        } else {
            return error("流程部署失败");
        }
    }

    /**
     * 更新发文审批流程定义（临时方法）
     */
    @Log(title = "更新发文审批流程", businessType = BusinessType.UPDATE)
    @PostMapping("/updateSendDocumentApproval")
    public AjaxResult updateSendDocumentApproval()
    {
        try {
            // 读取新的BPMN文件内容
            String bpmnXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<definitions xmlns=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:flowable=\"http://flowable.org/bpmn\" xmlns:bpmndi=\"http://www.omg.org/spec/BPMN/20100524/DI\" xmlns:omgdc=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:omgdi=\"http://www.omg.org/spec/DD/20100524/DI\" typeLanguage=\"http://www.w3.org/2001/XMLSchema\" expressionLanguage=\"http://www.w3.org/1999/XPath\" targetNamespace=\"http://www.flowable.org/processdef\">\n" +
                "  <process id=\"send_document_approval\" name=\"发文审批流程\" isExecutable=\"true\">\n" +
                "    <documentation>发文审批流程：科员 → 科室负责人 → 办公室科员 → 办公室负责人 → 分管领导(可选) → 书记</documentation>\n" +
                "\n" +
                "    <startEvent id=\"startEvent\" name=\"开始\"></startEvent>\n" +
                "\n" +
                "    <!-- 科室负责人审批 -->\n" +
                "    <userTask id=\"deptManagerApproval\" name=\"科室负责人审批\" flowable:assignee=\"${deptManager}\">\n" +
                "      <documentation>科室负责人对发文进行审批</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:formProperty id=\"approvalResult\" name=\"审批结果\" type=\"enum\" required=\"true\">\n" +
                "          <flowable:value id=\"approve\" name=\"同意\"/>\n" +
                "          <flowable:value id=\"reject\" name=\"退回\"/>\n" +
                "        </flowable:formProperty>\n" +
                "        <flowable:formProperty id=\"approvalOpinion\" name=\"审批意见\" type=\"string\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <!-- 办公室科员审核 -->\n" +
                "    <userTask id=\"officeClerkReview\" name=\"办公室科员审核\" flowable:candidateGroups=\"clerk\">\n" +
                "      <documentation>办公室科员对发文进行审核</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:formProperty id=\"approvalResult\" name=\"审核结果\" type=\"enum\" required=\"true\">\n" +
                "          <flowable:value id=\"approve\" name=\"通过\"/>\n" +
                "          <flowable:value id=\"reject\" name=\"退回\"/>\n" +
                "        </flowable:formProperty>\n" +
                "        <flowable:formProperty id=\"approvalOpinion\" name=\"审核意见\" type=\"string\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <!-- 办公室负责人审核 -->\n" +
                "    <userTask id=\"officeManagerReview\" name=\"办公室负责人审核\" flowable:assignee=\"${officeManager}\">\n" +
                "      <documentation>办公室负责人对发文进行审核，可选择分管领导</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:taskListener event=\"complete\" class=\"com.base.oa.workflow.listener.TaskCompleteListener\"/>\n" +
                "        <flowable:formProperty id=\"approvalResult\" name=\"审核结果\" type=\"enum\" required=\"true\">\n" +
                "          <flowable:value id=\"approve\" name=\"通过\"/>\n" +
                "          <flowable:value id=\"reject\" name=\"退回\"/>\n" +
                "        </flowable:formProperty>\n" +
                "        <flowable:formProperty id=\"approvalOpinion\" name=\"审核意见\" type=\"string\"/>\n" +
                "        <flowable:formProperty id=\"selectedLeaders\" name=\"选择的分管领导\" type=\"string\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <!-- 包含网关：分发到选中的分管领导 -->\n" +
                "    <inclusiveGateway id=\"leaderDistributionGateway\" name=\"分管领导分发\"/>\n" +
                "\n" +
                "    <!-- 分管领导审批任务（动态生成） -->\n" +
                "    <userTask id=\"leader_zhangsan\" name=\"张三审批\" flowable:assignee=\"zhangsan\">\n" +
                "      <documentation>分管领导张三审批</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:taskListener event=\"complete\" class=\"com.base.oa.workflow.listener.TaskCompleteListener\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <userTask id=\"leader_wutao\" name=\"吴涛审批\" flowable:assignee=\"wutao\">\n" +
                "      <documentation>分管领导吴涛审批</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:taskListener event=\"complete\" class=\"com.base.oa.workflow.listener.TaskCompleteListener\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <userTask id=\"leader_liuer\" name=\"刘二审批\" flowable:assignee=\"liuer\">\n" +
                "      <documentation>分管领导刘二审批</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:taskListener event=\"complete\" class=\"com.base.oa.workflow.listener.TaskCompleteListener\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <!-- 包含网关：汇聚分管领导审批结果 -->\n" +
                "    <inclusiveGateway id=\"leaderMergeGateway\" name=\"分管领导汇聚\"/>\n" +
                "\n" +
                "    <!-- 书记审批 -->\n" +
                "    <userTask id=\"secretaryApproval\" name=\"书记审批\" flowable:candidateGroups=\"sj\">\n" +
                "      <documentation>书记对发文进行最终审批</documentation>\n" +
                "      <extensionElements>\n" +
                "        <flowable:formProperty id=\"approvalResult\" name=\"审批结果\" type=\"enum\" required=\"true\">\n" +
                "          <flowable:value id=\"approve\" name=\"同意\"/>\n" +
                "          <flowable:value id=\"reject\" name=\"退回\"/>\n" +
                "        </flowable:formProperty>\n" +
                "        <flowable:formProperty id=\"approvalOpinion\" name=\"审批意见\" type=\"string\"/>\n" +
                "      </extensionElements>\n" +
                "    </userTask>\n" +
                "\n" +
                "    <!-- 审批完成 -->\n" +
                "    <serviceTask id=\"approvalComplete\" name=\"审批完成\" flowable:class=\"com.base.oa.workflow.listener.DocumentApprovalCompleteListener\">\n" +
                "      <documentation>发文审批完成，更新文档状态</documentation>\n" +
                "    </serviceTask>\n" +
                "\n" +
                "    <!-- 审批退回 -->\n" +
                "    <serviceTask id=\"approvalReject\" name=\"审批退回\" flowable:class=\"com.base.oa.workflow.listener.DocumentApprovalRejectListener\">\n" +
                "      <documentation>发文审批退回，更新文档状态</documentation>\n" +
                "    </serviceTask>\n" +
                "\n" +
                "    <endEvent id=\"endEvent\" name=\"结束\"></endEvent>\n" +
                "\n" +
                "    <!-- 流程连线 -->\n" +
                "    <sequenceFlow id=\"flow1\" sourceRef=\"startEvent\" targetRef=\"deptManagerApproval\"/>\n" +
                "\n" +
                "    <!-- 科室负责人审批后的判断 -->\n" +
                "    <exclusiveGateway id=\"deptApprovalGateway\"/>\n" +
                "    <sequenceFlow id=\"flow2\" sourceRef=\"deptManagerApproval\" targetRef=\"deptApprovalGateway\"/>\n" +
                "    <sequenceFlow id=\"flow3\" sourceRef=\"deptApprovalGateway\" targetRef=\"officeClerkReview\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'approve'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow4\" sourceRef=\"deptApprovalGateway\" targetRef=\"approvalReject\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'reject'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 办公室科员审核后的判断 -->\n" +
                "    <exclusiveGateway id=\"officeClerkGateway\"/>\n" +
                "    <sequenceFlow id=\"flow5\" sourceRef=\"officeClerkReview\" targetRef=\"officeClerkGateway\"/>\n" +
                "    <sequenceFlow id=\"flow6\" sourceRef=\"officeClerkGateway\" targetRef=\"officeManagerReview\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'approve'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow7\" sourceRef=\"officeClerkGateway\" targetRef=\"approvalReject\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'reject'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 办公室负责人审核后的判断 -->\n" +
                "    <exclusiveGateway id=\"officeManagerGateway\"/>\n" +
                "    <sequenceFlow id=\"flow8\" sourceRef=\"officeManagerReview\" targetRef=\"officeManagerGateway\"/>\n" +
                "    <sequenceFlow id=\"flow9\" sourceRef=\"officeManagerGateway\" targetRef=\"leaderDistributionGateway\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'approve'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow10\" sourceRef=\"officeManagerGateway\" targetRef=\"approvalReject\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'reject'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 包含网关到各分管领导的条件连线 -->\n" +
                "    <sequenceFlow id=\"flow_to_zhangsan\" sourceRef=\"leaderDistributionGateway\" targetRef=\"leader_zhangsan\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${selectedLeaders != null and selectedLeaders.contains('zhangsan')}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow_to_wutao\" sourceRef=\"leaderDistributionGateway\" targetRef=\"leader_wutao\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${selectedLeaders != null and selectedLeaders.contains('wutao')}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow_to_liuer\" sourceRef=\"leaderDistributionGateway\" targetRef=\"leader_liuer\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${selectedLeaders != null and selectedLeaders.contains('liuer')}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 各分管领导到汇聚网关的连线 -->\n" +
                "    <sequenceFlow id=\"flow_zhangsan_merge\" sourceRef=\"leader_zhangsan\" targetRef=\"leaderMergeGateway\"/>\n" +
                "    <sequenceFlow id=\"flow_wutao_merge\" sourceRef=\"leader_wutao\" targetRef=\"leaderMergeGateway\"/>\n" +
                "    <sequenceFlow id=\"flow_liuer_merge\" sourceRef=\"leader_liuer\" targetRef=\"leaderMergeGateway\"/>\n" +
                "\n" +
                "    <!-- 汇聚网关到书记审批 -->\n" +
                "    <sequenceFlow id=\"flow_merge_to_secretary\" sourceRef=\"leaderMergeGateway\" targetRef=\"secretaryApproval\"/>\n" +
                "\n" +
                "    <!-- 默认路径：如果没有选择分管领导，直接到书记审批 -->\n" +
                "    <sequenceFlow id=\"flow_direct_to_secretary\" sourceRef=\"leaderDistributionGateway\" targetRef=\"secretaryApproval\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${selectedLeaders == null or selectedLeaders == ''}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 书记审批后的判断 -->\n" +
                "    <exclusiveGateway id=\"secretaryGateway\"/>\n" +
                "    <sequenceFlow id=\"flow14\" sourceRef=\"secretaryApproval\" targetRef=\"secretaryGateway\"/>\n" +
                "    <sequenceFlow id=\"flow15\" sourceRef=\"secretaryGateway\" targetRef=\"approvalComplete\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'approve'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "    <sequenceFlow id=\"flow16\" sourceRef=\"secretaryGateway\" targetRef=\"approvalReject\">\n" +
                "      <conditionExpression xsi:type=\"tFormalExpression\">${approvalResult == 'reject'}</conditionExpression>\n" +
                "    </sequenceFlow>\n" +
                "\n" +
                "    <!-- 结束连线 -->\n" +
                "    <sequenceFlow id=\"flow17\" sourceRef=\"approvalComplete\" targetRef=\"endEvent\"/>\n" +
                "    <sequenceFlow id=\"flow18\" sourceRef=\"approvalReject\" targetRef=\"endEvent\"/>\n" +
                "\n" +
                "  </process>\n" +
                "</definitions>";

            // 查找发文审批流程定义
            OaWorkflowDefinition definition = oaWorkflowService.selectOaWorkflowDefinitionByKey("send_document_approval");
            if (definition != null) {
                // 更新BPMN XML
                definition.setBpmnXml(bpmnXml);
                int result = oaWorkflowService.updateOaWorkflowDefinition(definition);
                if (result > 0) {
                    return AjaxResult.success("发文审批流程更新成功");
                } else {
                    return error("发文审批流程更新失败");
                }
            } else {
                return error("未找到发文审批流程定义");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return error("更新发文审批流程异常: " + e.getMessage());
        }
    }

    /**
     * 修复数据库中的无效BPMN属性
     */
    @Log(title = "修复BPMN属性", businessType = BusinessType.UPDATE)
    @PostMapping("/fixBpmnAttributes")
    public AjaxResult fixBpmnAttributes()
    {
        int fixedCount = oaWorkflowService.fixInvalidBpmnAttributesInDatabase();
        if (fixedCount >= 0) {
            return AjaxResult.success("修复完成，共修复 " + fixedCount + " 个工作流定义");
        } else {
            return error("修复失败");
        }
    }

    /**
     * 获取可用的工作流列表
     */
    @GetMapping("/definition/available")
    public AjaxResult getAvailableWorkflows(@RequestParam(required = false) String category)
    {
        OaWorkflowDefinition query = new OaWorkflowDefinition();
        query.setStatus("0"); // 只获取正常状态的工作流（0=正常，1=停用）

        List<OaWorkflowDefinition> list = oaWorkflowService.selectOaWorkflowDefinitionList(query);

        // 如果指定了category，进行过滤
        if (category != null && !category.isEmpty()) {
            list = list.stream()
                .filter(workflow -> {
                    String workflowKey = workflow.getWorkflowKey();
                    if ("receive".equals(category)) {
                        return workflowKey.contains("receive");
                    } else if ("send".equals(category)) {
                        return workflowKey.contains("send");
                    }
                    return true;
                })
                .collect(java.util.stream.Collectors.toList());
        }

        return success(list);
    }

    /**
     * 根据分类获取显示名称
     */
    private String getDisplayName(String category) {
        switch (category) {
            case "receive": return "收文";
            case "send": return "发文";
            default: return category;
        }
    }


}
