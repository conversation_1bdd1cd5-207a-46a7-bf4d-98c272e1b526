package com.base.oa.workflow.controller;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.enums.BusinessType;
import com.base.oa.workflow.service.WorkflowAssigneeService;
import com.base.oa.workflow.service.WorkflowDeploymentService;
import com.base.oa.workflow.service.WorkflowStartService;
import com.base.oa.workflow.service.IOaWorkflowService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工作流流程控制器
 * 提供新的基于部门角色的流程管理功能
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@RestController
@RequestMapping("/oa/workflow/process")
public class WorkflowProcessController extends BaseController {
    
    @Autowired
    private WorkflowStartService workflowStartService;
    
    @Autowired
    private WorkflowAssigneeService assigneeService;
    
    @Autowired
    private WorkflowDeploymentService deploymentService;

    @Autowired
    private IOaWorkflowService workflowService;
    
    /**
     * 启动收文审批流程
     */
    @Log(title = "启动收文审批流程", businessType = BusinessType.INSERT)
    @PostMapping("/start/receive/approval")
    public AjaxResult startReceiveApprovalProcess(@RequestBody Map<String, Object> params) {
        try {
            Long docId = Long.valueOf(params.get("docId").toString());
            Map<String, Object> variables = (Map<String, Object>) params.get("variables");
            
            String processInstanceId = workflowStartService.startDocumentReceiveApprovalProcess(docId, variables);
            
            return AjaxResult.success("收文审批流程启动成功")
                .put("processInstanceId", processInstanceId);
        } catch (Exception e) {
            return error("启动收文审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动收文特办流程
     */
    @Log(title = "启动收文特办流程", businessType = BusinessType.INSERT)
    @PostMapping("/start/receive/special")
    public AjaxResult startReceiveSpecialProcess(@RequestBody Map<String, Object> params) {
        try {
            Long docId = Long.valueOf(params.get("docId").toString());
            Map<String, Object> variables = (Map<String, Object>) params.get("variables");
            
            String processInstanceId = workflowStartService.startDocumentReceiveSpecialProcess(docId, variables);
            
            return AjaxResult.success("收文特办流程启动成功")
                .put("processInstanceId", processInstanceId);
        } catch (Exception e) {
            return error("启动收文特办流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 启动发文审批流程
     */
    @Log(title = "启动发文审批流程", businessType = BusinessType.INSERT)
    @PostMapping("/start/send/approval")
    public AjaxResult startSendApprovalProcess(@RequestBody Map<String, Object> params) {
        try {
            Long docId = Long.valueOf(params.get("docId").toString());
            Map<String, Object> variables = (Map<String, Object>) params.get("variables");
            
            String processInstanceId = workflowStartService.startDocumentSendApprovalProcess(docId, variables);
            
            return AjaxResult.success("发文审批流程启动成功")
                .put("processInstanceId", processInstanceId);
        } catch (Exception e) {
            return error("启动发文审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据候选组查询候选人
     */
    @GetMapping("/candidates/{candidateGroup}")
    public AjaxResult getCandidateUsers(@PathVariable String candidateGroup) {
        try {
            List<SysUser> candidates = assigneeService.getCandidateUsers(candidateGroup);
            
            // 转换为简化的用户信息
            List<Map<String, Object>> result = candidates.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("userId", user.getUserId());
                    userInfo.put("userName", user.getUserName());
                    userInfo.put("nickName", user.getNickName());
                    userInfo.put("deptId", user.getDeptId());
                    userInfo.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
                    return userInfo;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return AjaxResult.success(result)
                .put("candidateGroup", candidateGroup)
                .put("displayName", assigneeService.getCandidateGroupDisplayName(candidateGroup));
        } catch (Exception e) {
            return error("查询候选人失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据角色查询用户
     */
    @GetMapping("/users/role/{roleKey}")
    public AjaxResult getUsersByRole(@PathVariable String roleKey) {
        try {
            List<SysUser> users = assigneeService.getUsersByRole(roleKey);
            
            List<Map<String, Object>> result = users.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("userId", user.getUserId());
                    userInfo.put("userName", user.getUserName());
                    userInfo.put("nickName", user.getNickName());
                    userInfo.put("deptId", user.getDeptId());
                    userInfo.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
                    return userInfo;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            return error("查询用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据部门和角色查询用户
     */
    @GetMapping("/users/dept/{deptId}/role/{roleKey}")
    public AjaxResult getUsersByDeptAndRole(@PathVariable Long deptId, @PathVariable String roleKey) {
        try {
            List<SysUser> users = assigneeService.getUsersByDeptAndRole(deptId, roleKey);
            
            List<Map<String, Object>> result = users.stream()
                .map(user -> {
                    Map<String, Object> userInfo = new HashMap<>();
                    userInfo.put("userId", user.getUserId());
                    userInfo.put("userName", user.getUserName());
                    userInfo.put("nickName", user.getNickName());
                    userInfo.put("deptId", user.getDeptId());
                    userInfo.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
                    return userInfo;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            return error("查询用户失败: " + e.getMessage());
        }
    }
    
    /**
     * 查询部门负责人
     */
    @GetMapping("/dept/{deptId}/leader")
    public AjaxResult getDeptLeader(@PathVariable Long deptId) {
        try {
            SysUser leader = assigneeService.getDeptLeader(deptId);
            
            if (leader != null) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", leader.getUserId());
                userInfo.put("userName", leader.getUserName());
                userInfo.put("nickName", leader.getNickName());
                userInfo.put("deptId", leader.getDeptId());
                userInfo.put("deptName", leader.getDept() != null ? leader.getDept().getDeptName() : "");
                
                return AjaxResult.success(userInfo);
            } else {
                return AjaxResult.success().put("message", "未找到部门负责人");
            }
        } catch (Exception e) {
            return error("查询部门负责人失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用的流程定义
     */
    @GetMapping("/definitions/available")
    public AjaxResult getAvailableProcessDefinitions() {
        try {
            List<ProcessDefinition> definitions = workflowStartService.getAvailableProcessDefinitions();
            
            List<Map<String, Object>> result = definitions.stream()
                .map(def -> {
                    Map<String, Object> defInfo = new HashMap<>();
                    defInfo.put("id", def.getId());
                    defInfo.put("key", def.getKey());
                    defInfo.put("name", def.getName());
                    defInfo.put("version", def.getVersion());
                    defInfo.put("suspended", def.isSuspended());
                    return defInfo;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            return error("查询流程定义失败: " + e.getMessage());
        }
    }
    
    /**
     * 手动部署流程定义
     */
    @Log(title = "部署流程定义", businessType = BusinessType.INSERT)
    @PostMapping("/deploy")
    public AjaxResult deployProcess(@RequestBody Map<String, Object> params) {
        try {
            // 前端传递的是 workflowKey 和 workflowName，需要正确映射
            String processKey = (String) params.get("workflowKey");
            String processName = (String) params.get("workflowName");
            String bpmnXml = (String) params.get("bpmnXml");
            String description = (String) params.get("description");

            System.out.println("部署流程参数: processKey=" + processKey + ", processName=" + processName);

            boolean success = deploymentService.deployProcess(processKey, processName, bpmnXml, description);
            
            if (success) {
                return AjaxResult.success("流程定义部署成功");
            } else {
                return error("流程定义部署失败");
            }
        } catch (Exception e) {
            return error("部署流程定义失败: " + e.getMessage());
        }
    }

    /**
     * 获取可选择的分管领导列表
     */
    @GetMapping("/leaders")
    public AjaxResult getAvailableLeaders() {
        try {
            List<Map<String, Object>> leaders = workflowService.getAvailableLeaders();
            return AjaxResult.success(leaders);
        } catch (Exception e) {
            return error("获取分管领导列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定分管领导下的科室负责人列表
     */
    @GetMapping("/managers/{leaderId}")
    public AjaxResult getAvailableManagers(@PathVariable String leaderId) {
        try {
            List<Map<String, Object>> managers = workflowService.getAvailableManagers(leaderId);
            return AjaxResult.success(managers);
        } catch (Exception e) {
            return error("获取科室负责人列表失败: " + e.getMessage());
        }
    }
}
