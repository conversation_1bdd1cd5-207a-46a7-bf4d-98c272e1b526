package com.base.oa.workflow.listener;

import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * 动态科室负责人任务监听器
 * 根据分管领导选择的科室负责人动态分配任务
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Component("dynamicManagerTaskListener")
public class DynamicManagerTaskListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            String taskId = delegateTask.getId();
            String processInstanceId = delegateTask.getProcessInstanceId();
            
            System.out.println("动态科室负责人任务创建: " + taskId + ", 流程实例: " + processInstanceId);
            
            // 从流程变量中获取分管领导选择的科室负责人列表
            Map<String, Object> variables = delegateTask.getVariables();
            Object selectedManagersVar = variables.get("selectedManagers");

            if (selectedManagersVar != null) {
                List<String> selectedManagers = null;

                // 处理不同的数据格式
                if (selectedManagersVar instanceof List) {
                    selectedManagers = (List<String>) selectedManagersVar;
                } else if (selectedManagersVar instanceof String) {
                    String managersStr = (String) selectedManagersVar;
                    selectedManagers = java.util.Arrays.asList(managersStr.split(","));
                }

                if (selectedManagers != null && !selectedManagers.isEmpty()) {
                    System.out.println("分管领导选择的科室负责人: " + selectedManagers);

                    // 清除默认的候选组，确保权限隔离
                    delegateTask.getCandidates().clear();

                    // 只为选择的科室负责人分配任务
                    for (String managerId : selectedManagers) {
                        managerId = managerId.trim(); // 去除空格
                        if (!managerId.isEmpty()) {
                            delegateTask.addCandidateUser(managerId);
                            System.out.println("分配科室负责人任务给: " + managerId);
                        }
                    }

                    System.out.println("科室负责人任务权限隔离设置完成，只有选择的 " + selectedManagers.size() + " 个科室负责人能看到任务");
                } else {
                    System.out.println("selectedManagers为空，使用默认分配策略");
                    // 如果没有选择，分配给所有科室负责人
                    delegateTask.addCandidateGroup("ksfzr");
                }
            } else {
                System.out.println("未找到selectedManagers变量，使用默认分配策略");
                // 如果没有选择，分配给所有科室负责人
                delegateTask.addCandidateGroup("ksfzr");
            }
            
        } catch (Exception e) {
            System.err.println("动态科室负责人任务监听器执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
