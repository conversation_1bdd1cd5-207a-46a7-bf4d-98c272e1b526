package com.base.oa.workflow.service;

import java.util.List;
import java.util.Map;
import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.domain.OaWorkflowInstance;
import com.base.oa.workflow.dto.WorkflowTaskDTO;

/**
 * 工作流服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IOaWorkflowService 
{
    /**
     * 查询工作流定义列表
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 工作流定义集合
     */
    public List<OaWorkflowDefinition> selectOaWorkflowDefinitionList(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 查询工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 工作流定义
     */
    public OaWorkflowDefinition selectOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 根据流程标识查询工作流定义
     *
     * @param workflowKey 流程标识
     * @return 工作流定义
     */
    public OaWorkflowDefinition selectOaWorkflowDefinitionByKey(String workflowKey);

    /**
     * 新增工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int insertOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 修改工作流定义
     * 
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    public int updateOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition);

    /**
     * 删除工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowId(Long workflowId);

    /**
     * 批量删除工作流定义
     *
     * @param workflowIds 工作流定义主键数组
     * @return 结果
     */
    public int deleteOaWorkflowDefinitionByWorkflowIds(Long[] workflowIds);

    /**
     * 启动工作流程
     * 
     * @param workflowKey 流程标识
     * @param businessKey 业务主键
     * @param variables 流程变量
     * @return 流程实例ID
     */
    public String startProcess(String workflowKey, String businessKey, Map<String, Object> variables);

    /**
     * 完成任务
     * 
     * @param taskId 任务ID
     * @param variables 流程变量
     * @param comment 处理意见
     * @return 结果
     */
    public boolean completeTask(String taskId, Map<String, Object> variables, String comment);

    /**
     * 撤回流程
     *
     * @param businessKey 业务主键
     * @param reason 撤回原因
     * @return 结果
     */
    public boolean withdrawProcess(String businessKey, String reason);

    /**
     * 获取可选择的分管领导列表
     *
     * @return 分管领导列表
     */
    public List<Map<String, Object>> getAvailableLeaders();

    /**
     * 获取指定分管领导下的科室负责人列表
     *
     * @param leaderId 分管领导ID
     * @return 科室负责人列表
     */
    public List<Map<String, Object>> getAvailableManagers(String leaderId);

    /**
     * 获取指定科室的经办人列表
     *
     * @param deptId 科室ID
     * @return 经办人列表
     */
    public List<Map<String, Object>> getAvailableHandlers(String deptId);

    /**
     * 获取所有可分配的人员列表（用于特办流程）
     *
     * @return 人员列表
     */
    public List<Map<String, Object>> getAllAvailablePersonnel();

    /**
     * 催办任务
     *
     * @param taskId 任务ID
     * @param message 催办消息
     * @return 结果
     */
    public boolean urgeTask(String taskId, String message);

    /**
     * 自动完成第一个任务
     *
     * @param processInstanceId 流程实例ID
     * @param taskDefinitionKey 任务定义Key
     * @param assignee 任务执行人
     * @return 结果
     */
    public boolean autoCompleteFirstTask(String processInstanceId, String taskDefinitionKey, String assignee);

    /**
     * 自动完成书记收文任务（特批流程专用）
     *
     * @param processInstanceId 流程实例ID
     * @param taskDefinitionKey 任务定义Key
     * @param assignee 任务执行人（书记）
     * @param variables 任务变量（包含选择的处理人员）
     * @return 结果
     */
    public boolean autoCompleteSecretaryTask(String processInstanceId, String taskDefinitionKey, String assignee, Map<String, Object> variables);

    /**
     * 查询待办任务列表
     *
     * @param userId 用户ID
     * @param taskQuery 查询条件
     * @return 待办任务集合
     */
    public List<WorkflowTaskDTO> selectTodoTasks(Long userId, WorkflowTaskDTO taskQuery);

    /**
     * 查询已办任务列表
     *
     * @param userId 用户ID
     * @return 已办任务集合
     */
    public List<WorkflowTaskDTO> selectDoneTasks(Long userId);

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    public Map<String, Object> getTaskDetail(String taskId);

    /**
     * 查询流程实例列表
     * 
     * @param oaWorkflowInstance 流程实例
     * @return 流程实例集合
     */
    public List<OaWorkflowInstance> selectOaWorkflowInstanceList(OaWorkflowInstance oaWorkflowInstance);

    /**
     * 查询流程实例详情
     * 
     * @param instanceId 实例ID
     * @return 流程实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByInstanceId(Long instanceId);

    /**
     * 终止流程实例
     *
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @return 结果
     */
    public boolean terminateProcess(Long instanceId, String reason);

    /**
     * 通过流程实例ID终止流程
     *
     * @param processInstanceId 流程实例ID
     * @param reason 终止原因
     * @return 结果
     */
    public boolean terminateProcessByInstanceId(String processInstanceId, String reason);

    /**
     * 转办任务
     * 
     * @param taskId 任务ID
     * @param targetUserId 目标用户ID
     * @param comment 转办说明
     * @return 结果
     */
    public boolean delegateTask(String taskId, Long targetUserId, String comment);

    /**
     * 获取流程图
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程图字节数组
     */
    public byte[] getProcessDiagram(String processInstanceId);

    /**
     * 查询流程历史
     *
     * @param processInstanceId 流程实例ID
     * @return 历史任务列表
     */
    public List<WorkflowTaskDTO> selectProcessHistory(String processInstanceId);

    /**
     * 部署流程定义
     * 
     * @param workflowName 流程名称
     * @param workflowKey 流程标识
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    public String deployProcess(String workflowName, String workflowKey, String bpmnXml);

    /**
     * 挂起流程定义
     * 
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    public boolean suspendProcessDefinition(String processDefinitionId);

    /**
     * 激活流程定义
     * 
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    public boolean activateProcessDefinition(String processDefinitionId);

    /**
     * 删除流程部署
     * 
     * @param deploymentId 部署ID
     * @param cascade 是否级联删除
     * @return 结果
     */
    public boolean deleteDeployment(String deploymentId, boolean cascade);

    /**
     * 查询流程定义列表
     * 
     * @return 流程定义列表
     */
    public List<Map<String, Object>> selectProcessDefinitionList();

    /**
     * 根据业务主键查询流程实例
     * 
     * @param businessKey 业务主键
     * @return 流程实例
     */
    public OaWorkflowInstance selectOaWorkflowInstanceByBusinessKey(String businessKey);

    /**
     * 查询用户参与的流程实例
     * 
     * @param userId 用户ID
     * @return 流程实例列表
     */
    public List<OaWorkflowInstance> selectUserProcessInstances(Long userId);

    /**
     * 获取下一步处理人
     * 
     * @param taskId 任务ID
     * @return 处理人列表
     */
    public List<Map<String, Object>> getNextAssignees(String taskId);

    /**
     * 回退任务
     *
     * @param taskId 任务ID
     * @param targetActivityId 目标活动ID
     * @param comment 回退说明
     * @return 结果
     */
    public boolean rejectTask(String taskId, String targetActivityId, String comment);

    /**
     * 修复数据库中所有工作流定义的无效BPMN属性
     *
     * @return 修复的工作流定义数量
     */
    public int fixInvalidBpmnAttributesInDatabase();

    /**
     * 获取流程实例的详细审批信息
     *
     * @param processInstanceId 流程实例ID
     * @return 审批信息
     */
    public Map<String, Object> getProcessApprovalInfo(String processInstanceId);

    /**
     * 完成流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 结果
     */
    public boolean completeProcessInstance(String processInstanceId);

    /**
     * 获取可退回的节点列表
     *
     * @param taskId 任务ID
     * @return 可退回的节点列表
     */
    public List<Map<String, Object>> getReturnNodes(String taskId);


}
