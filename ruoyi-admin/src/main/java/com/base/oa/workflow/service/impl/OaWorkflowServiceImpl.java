package com.base.oa.workflow.service.impl;

import java.util.*;
import java.util.stream.Collectors;
import java.io.InputStream;

import com.base.oa.workflow.service.WorkflowAssigneeService;
import com.base.oa.workflow.service.WorkflowDeploymentService;
import com.base.oa.workflow.service.WorkflowStartService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.engine.task.Comment;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.UserTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.PostConstruct;

import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.entity.SysRole;
import com.base.system.service.ISysUserService;
import com.base.system.service.ISysRoleService;
import com.base.system.service.ISysRoleService;
import com.base.system.service.ISysDeptService;
import com.base.common.core.domain.entity.SysDept;
import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.domain.OaWorkflowInstance;
import com.base.oa.workflow.dto.WorkflowTaskDTO;
import com.base.oa.workflow.mapper.OaWorkflowDefinitionMapper;
import com.base.oa.workflow.mapper.OaWorkflowInstanceMapper;
import com.base.oa.workflow.service.IOaWorkflowService;

/**
 * 工作流服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaWorkflowServiceImpl implements IOaWorkflowService 
{
    @Autowired
    private OaWorkflowDefinitionMapper workflowDefinitionMapper;

    @Autowired
    private OaWorkflowInstanceMapper workflowInstanceMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private WorkflowAssigneeService assigneeService;

    @Autowired
    private WorkflowStartService workflowStartService;

    @Autowired
    private WorkflowDeploymentService deploymentService;

    @Autowired(required = false)
    private com.base.oa.document.service.IOaDocumentReceiveService documentReceiveService;

    @Autowired(required = false)
    private com.base.oa.document.service.IOaDocumentSendService documentSendService;

    // 移除 OaWorkflowTaskMapper 依赖，直接使用 Flowable 原生 API

    /**
     * 应用启动后自动部署数据库中的工作流定义
     */
    @PostConstruct
    public void deployWorkflowDefinitionsOnStartup() {
        try {
            // 查询所有启用状态的工作流定义
            List<OaWorkflowDefinition> definitions = workflowDefinitionMapper.selectOaWorkflowDefinitionList(new OaWorkflowDefinition());

            for (OaWorkflowDefinition definition : definitions) {
                if ("0".equals(definition.getStatus()) && definition.getBpmnXml() != null) {
                    // 检查是否已经部署
                    List<ProcessDefinition> existingDefinitions = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionKey(definition.getWorkflowKey())
                        .list();

                    if (existingDefinitions.isEmpty()) {
                        // 部署流程定义
                        String deploymentId = deployProcess(
                            definition.getWorkflowName(),
                            definition.getWorkflowKey(),
                            definition.getBpmnXml()
                        );

                        if (deploymentId != null) {
                            // 更新部署ID
                            definition.setDeploymentId(deploymentId);
                            workflowDefinitionMapper.updateOaWorkflowDefinition(definition);
                            System.out.println("自动部署工作流定义成功: " + definition.getWorkflowName() + " (Key: " + definition.getWorkflowKey() + ")");
                        } else {
                            System.err.println("自动部署工作流定义失败: " + definition.getWorkflowName() + " (Key: " + definition.getWorkflowKey() + ")");
                        }
                    } else {
                        System.out.println("工作流定义已存在，跳过部署: " + definition.getWorkflowName() + " (Key: " + definition.getWorkflowKey() + ")");
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("自动部署工作流定义时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    /**
     * 查询工作流定义列表
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 工作流定义
     */
    @Override
    public List<OaWorkflowDefinition> selectOaWorkflowDefinitionList(OaWorkflowDefinition oaWorkflowDefinition)
    {
        return workflowDefinitionMapper.selectOaWorkflowDefinitionList(oaWorkflowDefinition);
    }

    /**
     * 查询工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 工作流定义
     */
    @Override
    public OaWorkflowDefinition selectOaWorkflowDefinitionByWorkflowId(Long workflowId)
    {
        return workflowDefinitionMapper.selectOaWorkflowDefinitionByWorkflowId(workflowId);
    }

    /**
     * 根据流程标识查询工作流定义
     *
     * @param workflowKey 流程标识
     * @return 工作流定义
     */
    @Override
    public OaWorkflowDefinition selectOaWorkflowDefinitionByKey(String workflowKey)
    {
        return workflowDefinitionMapper.selectOaWorkflowDefinitionByKey(workflowKey);
    }

    /**
     * 新增工作流定义
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition)
    {
        oaWorkflowDefinition.setCreateTime(DateUtils.getNowDate());
        oaWorkflowDefinition.setCreateBy(SecurityUtils.getUsername());

        // 验证并设置必填字段的默认值
        if (oaWorkflowDefinition.getWorkflowName() == null || oaWorkflowDefinition.getWorkflowName().trim().isEmpty()) {
            oaWorkflowDefinition.setWorkflowName("未命名流程");
        }
        if (oaWorkflowDefinition.getWorkflowKey() == null || oaWorkflowDefinition.getWorkflowKey().trim().isEmpty()) {
            oaWorkflowDefinition.setWorkflowKey("unknown_process_" + System.currentTimeMillis());
        }
        if (oaWorkflowDefinition.getWorkflowVersion() == null) {
            oaWorkflowDefinition.setWorkflowVersion(1);
        }
        if (oaWorkflowDefinition.getStatus() == null) {
            oaWorkflowDefinition.setStatus("0");
        }

        // 部署流程到Flowable引擎
        if (oaWorkflowDefinition.getBpmnXml() != null) {
            String deploymentId = deployProcess(
                oaWorkflowDefinition.getWorkflowName(),
                oaWorkflowDefinition.getWorkflowKey(),
                oaWorkflowDefinition.getBpmnXml()
            );
            oaWorkflowDefinition.setDeploymentId(deploymentId);
        }

        return workflowDefinitionMapper.insertOaWorkflowDefinition(oaWorkflowDefinition);
    }

    /**
     * 修改工作流定义
     *
     * @param oaWorkflowDefinition 工作流定义
     * @return 结果
     */
    @Override
    public int updateOaWorkflowDefinition(OaWorkflowDefinition oaWorkflowDefinition)
    {
        oaWorkflowDefinition.setUpdateTime(DateUtils.getNowDate());
        oaWorkflowDefinition.setUpdateBy(SecurityUtils.getUsername());

        // 如果BPMN XML有更新，需要重新部署到Flowable引擎
        if (oaWorkflowDefinition.getBpmnXml() != null && !oaWorkflowDefinition.getBpmnXml().trim().isEmpty()) {
            try {
                // 重新部署流程定义
                String deploymentId = deployProcess(
                    oaWorkflowDefinition.getWorkflowName(),
                    oaWorkflowDefinition.getWorkflowKey(),
                    oaWorkflowDefinition.getBpmnXml()
                );

                if (deploymentId != null) {
                    oaWorkflowDefinition.setDeploymentId(deploymentId);
                    System.out.println("工作流定义重新部署成功: " + oaWorkflowDefinition.getWorkflowName() +
                                     " (Key: " + oaWorkflowDefinition.getWorkflowKey() +
                                     ", DeploymentId: " + deploymentId + ")");
                } else {
                    System.err.println("工作流定义重新部署失败: " + oaWorkflowDefinition.getWorkflowName());
                }
            } catch (Exception e) {
                System.err.println("工作流定义重新部署异常: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return workflowDefinitionMapper.updateOaWorkflowDefinition(oaWorkflowDefinition);
    }

    /**
     * 删除工作流定义
     *
     * @param workflowId 工作流定义主键
     * @return 结果
     */
    @Override
    public int deleteOaWorkflowDefinitionByWorkflowId(Long workflowId)
    {
        return workflowDefinitionMapper.deleteOaWorkflowDefinitionByWorkflowId(workflowId);
    }

    /**
     * 批量删除工作流定义
     *
     * @param workflowIds 工作流定义主键数组
     * @return 结果
     */
    @Override
    public int deleteOaWorkflowDefinitionByWorkflowIds(Long[] workflowIds)
    {
        return workflowDefinitionMapper.deleteOaWorkflowDefinitionByWorkflowIds(workflowIds);
    }

    /**
     * 启动工作流程
     *
     * @param workflowKey 流程标识
     * @param businessKey 业务主键
     * @param variables 流程变量
     * @return 流程实例ID
     */
    @Override
    @Transactional
    public String startProcess(String workflowKey, String businessKey, Map<String, Object> variables)
    {
        try {
            System.out.println("启动工作流程 - workflowKey: " + workflowKey + ", businessKey: " + businessKey);

            // 检查流程定义是否存在
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionKey(workflowKey)
                .latestVersion()
                .list();

            if (processDefinitions.isEmpty()) {
                System.err.println("未找到流程定义，workflowKey: " + workflowKey);
                throw new RuntimeException("未找到流程定义: " + workflowKey);
            }

            // 启动流程实例
            ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(workflowKey, businessKey, variables);
            
            // 保存流程实例信息到数据库
            OaWorkflowInstance instance = new OaWorkflowInstance();
            instance.setProcessInstanceId(processInstance.getId());
            instance.setBusinessKey(businessKey);
            instance.setStartUserId(SecurityUtils.getUserId());
            instance.setStartUserName(SecurityUtils.getUsername());
            instance.setStatus("1"); // 进行中
            instance.setStartTime(new Date());
            instance.setCreateTime(new Date());
            instance.setCreateBy(SecurityUtils.getUsername());
            
            // 查询流程定义信息
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(processInstance.getProcessDefinitionId())
                .singleResult();
            
            if (processDefinition != null) {
                instance.setDefinitionId(processDefinition.getId());
                instance.setDefinitionKey(processDefinition.getKey());
                
                OaWorkflowDefinition definition = workflowDefinitionMapper.selectOaWorkflowDefinitionByKey(workflowKey);
                if (definition != null) {
                    instance.setWorkflowId(definition.getWorkflowId());
                    instance.setTitle(definition.getWorkflowName());
                }
            }
            
            // 获取当前任务信息
            List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstance.getId())
                .list();
            
            if (!tasks.isEmpty()) {
                Task currentTask = tasks.get(0);
                instance.setCurrentTaskId(currentTask.getId());
                instance.setCurrentTaskName(currentTask.getName());
                instance.setCurrentAssignee(currentTask.getAssignee());
            }
            
            workflowInstanceMapper.insertOaWorkflowInstance(instance);
            
            return processInstance.getId();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("启动流程失败: " + e.getMessage());
        }
    }

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param variables 流程变量
     * @param comment 处理意见
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeTask(String taskId, Map<String, Object> variables, String comment)
    {
        try {
            // 获取任务信息
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                // 检查任务是否已经完成
                HistoricTaskInstance historicTask = historyService.createHistoricTaskInstanceQuery()
                    .taskId(taskId)
                    .singleResult();

                if (historicTask != null) {
                    throw new RuntimeException("任务已完成，无法重复操作。请刷新页面获取最新任务列表。");
                } else {
                    throw new RuntimeException("任务不存在或已被删除: " + taskId + "。请刷新页面获取最新任务列表。");
                }
            }

            // 获取当前用户
            String currentUser = SecurityUtils.getUsername();
            System.out.println("用户 " + currentUser + " 尝试完成任务: " + taskId);

            // TODO: 暂时禁用权限验证，避免影响基本功能
            // 验证用户是否有权限完成此任务
            // if (!validateTaskPermission(task, currentUser)) {
            //     System.err.println("用户 " + currentUser + " 没有权限完成任务: " + taskId);
            //     throw new RuntimeException("用户 " + currentUser + " 没有权限完成任务: " + taskId);
            // }

            String processInstanceId = task.getProcessInstanceId();

            // 检查是否是拒绝操作 - 从variables中获取result参数
            String result = (String) variables.get("result");
            if (result == null) {
                // 如果variables中没有result，检查approvalResult
                String approvalResult = (String) variables.get("approvalResult");
                if ("reject".equals(approvalResult)) {
                    result = "reject";
                } else if ("return".equals(approvalResult)) {
                    result = "return";
                } else if ("approve".equals(approvalResult)) {
                    result = "approve";
                }
            }

            System.out.println("任务处理结果: " + result);

            if ("reject".equals(result)) {
                // 拒绝：直接结束流程并恢复文档状态为草稿
                System.out.println("执行拒绝操作，结束流程并恢复文档状态为草稿");
                return handleRejectTask(taskId, processInstanceId, comment);
            } else if ("return".equals(result)) {
                // 回退：回退到上一个审批节点
                System.out.println("执行回退操作，回退到上一个审批节点");
                return handleReturnTask(taskId, comment);
            } else {
                // 正常审批：继续流程
                System.out.println("执行正常审批操作，继续流程");
                return handleApproveTask(taskId, variables, comment);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理拒绝任务：直接结束流程并恢复文档状态为草稿
     */
    private boolean handleRejectTask(String taskId, String processInstanceId, String comment) {
        try {
            // 添加拒绝评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, "拒绝：" + comment);
            }

            // 获取业务主键
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance != null) {
                String businessKey = processInstance.getBusinessKey();

                // 删除流程实例（拒绝结束）
                runtimeService.deleteProcessInstance(processInstanceId, "审批拒绝：" + comment);

                // 更新oa_workflow_instance表状态为已终止
                updateWorkflowInstanceStatusToTerminated(processInstanceId, comment);

                // 恢复文档状态为草稿
                restoreDocumentToDraft(businessKey, comment);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理回退任务：回退到上一个审批节点
     */
    private boolean handleReturnTask(String taskId, String comment) {
        try {
            // 添加回退评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, "回退：" + comment);
            }

            // 获取当前任务信息
            Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
            String processInstanceId = currentTask.getProcessInstanceId();

            // 查找上一个用户任务节点
            String previousActivityId = findPreviousUserTaskActivity(processInstanceId, currentTask.getTaskDefinitionKey());

            if (previousActivityId != null) {
                // 使用Flowable的活动跳转功能回退到上一个节点
                runtimeService.createChangeActivityStateBuilder()
                    .processInstanceId(processInstanceId)
                    .moveActivityIdTo(currentTask.getTaskDefinitionKey(), previousActivityId)
                    .changeState();

                return true;
            } else {
                throw new RuntimeException("未找到可回退的上一个节点");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 处理正常审批任务：继续流程
     */
    private boolean handleApproveTask(String taskId, Map<String, Object> variables, String comment) {
        try {
            // 获取任务信息
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new RuntimeException("任务不存在: " + taskId);
            }

            String processInstanceId = task.getProcessInstanceId();

            // 添加任务评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, comment);
            }

            // 完成任务 - 直接使用 Flowable API
            taskService.complete(taskId, variables);

            // 检查流程是否已结束
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                // 流程已结束，更新文档状态为已归档
                String businessKey = task.getProcessDefinitionId().contains("receive") ?
                    "receive_" + processInstanceId : "send_" + processInstanceId;

                // 从历史流程实例中获取业务主键
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();

                if (historicProcessInstance != null && historicProcessInstance.getBusinessKey() != null) {
                    businessKey = historicProcessInstance.getBusinessKey();
                    updateDocumentStatusAfterComplete(businessKey);
                    System.out.println("流程已结束，更新文档状态为已归档: " + businessKey);
                }
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 撤回流程
     *
     * @param businessKey 业务主键
     * @param reason 撤回原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean withdrawProcess(String businessKey, String reason)
    {
        try {
            // 根据业务主键查找流程实例
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceBusinessKey(businessKey)
                .singleResult();

            if (processInstance == null) {
                throw new RuntimeException("未找到对应的流程实例");
            }

            // 检查流程是否已结束
            if (processInstance.isEnded()) {
                throw new RuntimeException("流程已结束，无法撤回");
            }

            String processInstanceId = processInstance.getId();

            // 删除流程实例（撤回）
            runtimeService.deleteProcessInstance(processInstanceId, reason);

            // 更新oa_workflow_instance表状态为已终止
            updateWorkflowInstanceStatusToTerminated(processInstanceId, "流程撤回：" + reason);

            // 更新文档状态为草稿
            updateDocumentStatusAfterWithdraw(businessKey);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 撤回后更新文档状态
     */
    private void updateDocumentStatusAfterWithdraw(String businessKey) {
        restoreDocumentToDraft(businessKey, "流程撤回");
    }

    /**
     * 更新工作流实例状态为已终止
     */
    private void updateWorkflowInstanceStatusToTerminated(String processInstanceId, String reason) {
        try {
            OaWorkflowInstance instance = workflowInstanceMapper.selectOaWorkflowInstanceByProcessInstanceId(processInstanceId);
            if (instance != null) {
                instance.setStatus("3"); // 3=已终止
                instance.setEndTime(new Date());
                instance.setUpdateTime(new Date());
                instance.setUpdateBy("system");
                instance.setRemark("审批拒绝：" + reason);
                workflowInstanceMapper.updateOaWorkflowInstance(instance);

                System.out.println("流程实例状态已更新为已终止: " + processInstanceId + " (实例ID: " + instance.getInstanceId() + ")");
            } else {
                System.err.println("未找到对应的工作流实例记录: " + processInstanceId);
            }
        } catch (Exception e) {
            System.err.println("更新工作流实例状态失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 恢复文档状态为草稿
     */
    private void restoreDocumentToDraft(String businessKey, String reason) {
        try {
            if (businessKey.startsWith("receive_doc_") || businessKey.startsWith("doc_receive_")) {
                // 收文拒绝/撤回
                Long docId = null;
                if (businessKey.startsWith("receive_doc_")) {
                    docId = Long.parseLong(businessKey.replace("receive_doc_", ""));
                } else {
                    docId = Long.parseLong(businessKey.replace("doc_receive_", ""));
                }
                if (documentReceiveService != null && docId != null) {
                    documentReceiveService.rejectDocument(docId, reason);
                }
            } else if (businessKey.startsWith("send_doc_") || businessKey.startsWith("doc_send_")) {
                // 发文拒绝/撤回
                Long docId = null;
                if (businessKey.startsWith("send_doc_")) {
                    docId = Long.parseLong(businessKey.replace("send_doc_", ""));
                } else {
                    docId = Long.parseLong(businessKey.replace("doc_send_", ""));
                }
                if (documentSendService != null && docId != null) {
                    documentSendService.rejectDocument(docId, reason);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查找上一个用户任务活动节点
     */
    private String findPreviousUserTaskActivity(String processInstanceId, String currentActivityId) {
        try {
            // 获取流程定义
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

            if (processInstance == null) {
                return null;
            }

            // 查询历史任务，找到当前任务之前的用户任务
            List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .desc()
                .list();

            // 找到最近完成的用户任务作为回退目标
            for (HistoricTaskInstance historicTask : historicTasks) {
                if (!currentActivityId.equals(historicTask.getTaskDefinitionKey())) {
                    return historicTask.getTaskDefinitionKey();
                }
            }

            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 查询待办任务列表
     *
     * @param userId 用户ID
     * @param taskQuery 查询条件
     * @return 待办任务集合
     */
    @Override
    public List<WorkflowTaskDTO> selectTodoTasks(Long userId, WorkflowTaskDTO taskQuery)
    {
        // 获取当前用户名和用户信息
        String username = SecurityUtils.getUsername();
        SysUser currentUser = userService.selectUserByUserName(username);

        if (currentUser == null) {
            return new ArrayList<>();
        }

        // 使用Set去重，避免重复任务
        Set<String> taskIds = new HashSet<>();
        List<Task> allTasks = new ArrayList<>();

        // 查询1：使用用户名查询直接分配的任务
        List<Task> tasksByUsername = taskService.createTaskQuery()
            .taskAssignee(username)
            .active()
            .orderByTaskCreateTime()
            .desc()
            .listPage(0, 100); // 限制查询数量

        for (Task task : tasksByUsername) {
            if (taskIds.add(task.getId())) {
                allTasks.add(task);
            }
        }

        // 查询2：使用用户ID查询直接分配的任务（处理数据不一致问题）
        List<Task> tasksByUserId = taskService.createTaskQuery()
            .taskAssignee(currentUser.getUserId().toString())
            .active()
            .orderByTaskCreateTime()
            .desc()
            .listPage(0, 100); // 限制查询数量

        for (Task task : tasksByUserId) {
            if (taskIds.add(task.getId())) {
                allTasks.add(task);
            }
        }

        // 查询3：检查候选任务（直接指定用户）
        List<Task> candidateTasks = taskService.createTaskQuery()
            .taskCandidateUser(username)
            .active()
            .listPage(0, 100); // 限制查询数量

        for (Task task : candidateTasks) {
            if (taskIds.add(task.getId())) {
                allTasks.add(task);
            }
        }

        // 查询4：批量查询候选组任务
        // 获取用户的角色列表
        Set<String> userRoles = roleService.selectRolePermissionByUserId(currentUser.getUserId());

        if (!userRoles.isEmpty()) {
            // 构建候选组列表
            List<String> candidateGroups = new ArrayList<>();
            Long deptId = currentUser.getDeptId();

            for (String roleKey : userRoles) {
                candidateGroups.add("role_" + roleKey);
                candidateGroups.add(roleKey);

                if (deptId != null) {
                    candidateGroups.add("dept_" + deptId + "_role_" + roleKey);
                    candidateGroups.add("dept_" + deptId + "_" + roleKey);
                    candidateGroups.add("dept_dynamic_" + roleKey);
                }
            }


            // 查询部门负责人候选组任务（如果当前用户是部门负责人）
            if (deptId != null) {
                SysDept dept = deptService.selectDeptById(deptId);
                if (dept != null && dept.getLeader() != null &&
                    dept.getLeader().equals(currentUser.getUserId().toString())) {
                    candidateGroups.add("dept_" + deptId + "_leader");
                }
            }

            // 批量查询候选组任务
            if (!candidateGroups.isEmpty()) {
                List<Task> candidateGroupTasks = taskService.createTaskQuery()
                    .taskCandidateGroupIn(candidateGroups)
                    .active()
                    .listPage(0, 100); // 限制查询数量

                for (Task task : candidateGroupTasks) {
                    if (taskIds.add(task.getId())) {
                        allTasks.add(task);
                    }
                }
            }
        }

        // 按创建时间排序，最新的在前
        allTasks.sort((t1, t2) -> t2.getCreateTime().compareTo(t1.getCreateTime()));

        // 批量补充任务信息，优化性能
        return enrichTaskInfoBatch(allTasks);
    }

    /**
     * 查询已办任务列表
     *
     * @param userId 用户ID
     * @return 已办任务集合
     */
    @Override
    public List<WorkflowTaskDTO> selectDoneTasks(Long userId)
    {
        // 获取当前用户名
        String username = SecurityUtils.getUsername();

        // 从Flowable查询已办任务，使用用户名而不是用户ID，限制查询数量
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
            .taskAssignee(username)
            .finished()
            .orderByHistoricTaskInstanceEndTime()
            .desc()
            .listPage(0, 100); // 限制查询数量，提高性能

        return enrichHistoricTaskInfoBatch(historicTasks);
    }

    /**
     * 批量补充任务信息，优化性能
     */
    private List<WorkflowTaskDTO> enrichTaskInfoBatch(List<Task> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            return new ArrayList<>();
        }

        List<WorkflowTaskDTO> taskDTOs = new ArrayList<>();

        // 批量查询流程实例
        Set<String> processInstanceIds = tasks.stream()
            .map(Task::getProcessInstanceId)
            .collect(Collectors.toSet());

        Map<String, ProcessInstance> processInstanceMap = new HashMap<>();
        if (!processInstanceIds.isEmpty()) {
            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery()
                .processInstanceIds(processInstanceIds)
                .list();

            for (ProcessInstance pi : processInstances) {
                processInstanceMap.put(pi.getId(), pi);
            }
        }

        // 批量查询流程定义
        Set<String> processDefinitionIds = processInstanceMap.values().stream()
            .map(ProcessInstance::getProcessDefinitionId)
            .collect(Collectors.toSet());

        Map<String, ProcessDefinition> processDefinitionMap = new HashMap<>();
        if (!processDefinitionIds.isEmpty()) {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionIds(processDefinitionIds)
                .list();

            for (ProcessDefinition pd : processDefinitions) {
                processDefinitionMap.put(pd.getId(), pd);
            }
        }

        // 批量查询用户信息
        Set<String> usernames = tasks.stream()
            .map(Task::getAssignee)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<String, SysUser> userMap = new HashMap<>();
        if (!usernames.isEmpty()) {
            for (String username : usernames) {
                SysUser user = userService.selectUserByUserName(username);
                if (user != null) {
                    userMap.put(username, user);
                }
            }
        }

        // 组装DTO
        for (Task task : tasks) {
            WorkflowTaskDTO taskDTO = WorkflowTaskDTO.fromTask(task);

            // 设置处理人昵称
            if (task.getAssignee() != null) {
                SysUser user = userMap.get(task.getAssignee());
                if (user != null) {
                    taskDTO.setAssigneeName(user.getNickName());
                }
            }

            // 设置流程信息
            ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            if (processInstance != null) {
                taskDTO.setBusinessKey(processInstance.getBusinessKey());

                ProcessDefinition processDefinition = processDefinitionMap.get(processInstance.getProcessDefinitionId());
                if (processDefinition != null) {
                    taskDTO.setWorkflowName(processDefinition.getName());
                    taskDTO.setWorkflowKey(processDefinition.getKey());
                }
            }

            taskDTOs.add(taskDTO);
        }

        return taskDTOs;
    }

    /**
     * 批量补充历史任务信息，优化性能
     */
    private List<WorkflowTaskDTO> enrichHistoricTaskInfoBatch(List<HistoricTaskInstance> historicTasks) {
        if (historicTasks == null || historicTasks.isEmpty()) {
            return new ArrayList<>();
        }

        List<WorkflowTaskDTO> taskDTOs = new ArrayList<>();

        // 批量查询历史流程实例
        Set<String> processInstanceIds = historicTasks.stream()
            .map(HistoricTaskInstance::getProcessInstanceId)
            .collect(Collectors.toSet());

        Map<String, HistoricProcessInstance> historicProcessInstanceMap = new HashMap<>();
        if (!processInstanceIds.isEmpty()) {
            List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceIds(processInstanceIds)
                .list();

            for (HistoricProcessInstance hpi : historicProcessInstances) {
                historicProcessInstanceMap.put(hpi.getId(), hpi);
            }
        }

        // 批量查询流程定义
        Set<String> processDefinitionIds = historicProcessInstanceMap.values().stream()
            .map(HistoricProcessInstance::getProcessDefinitionId)
            .collect(Collectors.toSet());

        Map<String, ProcessDefinition> processDefinitionMap = new HashMap<>();
        if (!processDefinitionIds.isEmpty()) {
            List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
                .processDefinitionIds(processDefinitionIds)
                .list();

            for (ProcessDefinition pd : processDefinitions) {
                processDefinitionMap.put(pd.getId(), pd);
            }
        }

        // 批量查询用户信息
        Set<String> usernames = historicTasks.stream()
            .map(HistoricTaskInstance::getAssignee)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        Map<String, SysUser> userMap = new HashMap<>();
        if (!usernames.isEmpty()) {
            for (String username : usernames) {
                SysUser user = userService.selectUserByUserName(username);
                if (user != null) {
                    userMap.put(username, user);
                }
            }
        }

        // 组装DTO
        for (HistoricTaskInstance historicTask : historicTasks) {
            WorkflowTaskDTO taskDTO = WorkflowTaskDTO.fromHistoricTask(historicTask);

            // 设置处理人昵称
            if (historicTask.getAssignee() != null) {
                SysUser user = userMap.get(historicTask.getAssignee());
                if (user != null) {
                    taskDTO.setAssigneeName(user.getNickName());
                }
            }

            // 设置流程信息
            HistoricProcessInstance historicProcessInstance = historicProcessInstanceMap.get(historicTask.getProcessInstanceId());
            if (historicProcessInstance != null) {
                taskDTO.setBusinessKey(historicProcessInstance.getBusinessKey());

                ProcessDefinition processDefinition = processDefinitionMap.get(historicProcessInstance.getProcessDefinitionId());
                if (processDefinition != null) {
                    taskDTO.setWorkflowName(processDefinition.getName());
                    taskDTO.setWorkflowKey(processDefinition.getKey());
                }
            }

            taskDTOs.add(taskDTO);
        }

        return taskDTOs;
    }

    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Override
    public Map<String, Object> getTaskDetail(String taskId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取任务信息
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task != null) {
                WorkflowTaskDTO taskDTO = WorkflowTaskDTO.fromTask(task);
                enrichTaskInfo(taskDTO, task);
                result.put("task", taskDTO);

                // 获取任务变量
                Map<String, Object> variables = taskService.getVariables(taskId);
                result.put("variables", variables);

                // 获取流程变量
                Map<String, Object> processVariables = runtimeService.getVariables(task.getProcessInstanceId());
                result.put("processVariables", processVariables);
            }
        } catch (Exception e) {
            throw new RuntimeException("获取任务详情失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 查询流程实例列表
     *
     * @param oaWorkflowInstance 流程实例
     * @return 流程实例
     */
    @Override
    public List<OaWorkflowInstance> selectOaWorkflowInstanceList(OaWorkflowInstance oaWorkflowInstance)
    {
        List<OaWorkflowInstance> instances = workflowInstanceMapper.selectOaWorkflowInstanceList(oaWorkflowInstance);

        // 优化：批量查询流程定义，避免N+1查询问题
        enrichInstanceInfoBatch(instances);

        return instances;
    }

    /**
     * 批量补充流程实例信息，优化性能
     *
     * @param instances 流程实例列表
     */
    private void enrichInstanceInfoBatch(List<OaWorkflowInstance> instances) {
        if (instances == null || instances.isEmpty()) {
            return;
        }

        try {
            // 批量查询流程定义
            Map<String, String> definitionKeyToNameMap = new HashMap<>();
            Set<String> definitionKeys = instances.stream()
                .map(OaWorkflowInstance::getDefinitionKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            if (!definitionKeys.isEmpty()) {
                // 由于Flowable API限制，需要逐个查询流程定义
                for (String definitionKey : definitionKeys) {
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionKey(definitionKey)
                        .latestVersion()
                        .singleResult();
                    if (processDefinition != null) {
                        definitionKeyToNameMap.put(processDefinition.getKey(), processDefinition.getName());
                    }
                }
            }

            // 批量查询当前任务
            Map<String, List<Task>> processInstanceToTasksMap = new HashMap<>();
            Set<String> activeProcessInstanceIds = instances.stream()
                .filter(instance -> "1".equals(instance.getStatus()) && instance.getProcessInstanceId() != null)
                .map(OaWorkflowInstance::getProcessInstanceId)
                .collect(Collectors.toSet());

            if (!activeProcessInstanceIds.isEmpty()) {
                List<Task> allActiveTasks = taskService.createTaskQuery()
                    .processInstanceIdIn(activeProcessInstanceIds)
                    .active()
                    .list();

                for (Task task : allActiveTasks) {
                    processInstanceToTasksMap.computeIfAbsent(task.getProcessInstanceId(), k -> new ArrayList<>()).add(task);
                }
            }

            // 批量查询历史流程实例以获取发起人信息
            Set<String> processInstanceIds = instances.stream()
                .map(OaWorkflowInstance::getProcessInstanceId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            Map<String, HistoricProcessInstance> historicProcessMap = new HashMap<>();
            if (!processInstanceIds.isEmpty()) {
                List<HistoricProcessInstance> historicProcesses = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceIds(processInstanceIds)
                    .list();
                for (HistoricProcessInstance historicProcess : historicProcesses) {
                    historicProcessMap.put(historicProcess.getId(), historicProcess);
                }
            }

            // 补充实例信息
            for (OaWorkflowInstance instance : instances) {
                // 补充流程名称
                if (instance.getWorkflowName() == null && instance.getDefinitionKey() != null) {
                    String workflowName = definitionKeyToNameMap.get(instance.getDefinitionKey());
                    if (workflowName != null) {
                        instance.setWorkflowName(workflowName);
                    }
                }

                // 补充发起人姓名
                if (instance.getStartUserName() == null && instance.getProcessInstanceId() != null) {
                    HistoricProcessInstance historicProcess = historicProcessMap.get(instance.getProcessInstanceId());
                    if (historicProcess != null && historicProcess.getStartUserId() != null) {
                        try {
                            // 根据发起人用户名查询用户信息
                            SysUser user = userService.selectUserByUserName(historicProcess.getStartUserId());
                            if (user != null) {
                                instance.setStartUserName(user.getNickName());
                                instance.setStartUserId(user.getUserId());
                            } else {
                                // 如果找不到用户，显示用户名
                                instance.setStartUserName(historicProcess.getStartUserId());
                            }
                        } catch (Exception e) {
                            // 如果查询失败，显示用户名
                            instance.setStartUserName(historicProcess.getStartUserId());
                        }
                    }
                }

                // 补充当前任务信息
                if (instance.getProcessInstanceId() != null && "1".equals(instance.getStatus())) {
                    List<Task> currentTasks = processInstanceToTasksMap.get(instance.getProcessInstanceId());
                    if (currentTasks != null && !currentTasks.isEmpty()) {
                        Task currentTask = currentTasks.get(0);
                        instance.setCurrentTaskName(currentTask.getName());

                        // 设置当前处理人，处理直接分配和候选组的情况
                        String assigneeDisplayName = getTaskAssigneeDisplayName(currentTask);
                        instance.setCurrentAssignee(assigneeDisplayName);

                        // 如果有多个并行任务，显示所有处理人
                        if (currentTasks.size() > 1) {
                            String assignees = currentTasks.stream()
                                .map(this::getTaskAssigneeDisplayName)
                                .filter(assignee -> assignee != null && !assignee.isEmpty())
                                .collect(Collectors.joining(","));
                            instance.setCurrentAssignee(assignees);
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，避免影响列表查询
            e.printStackTrace();
        }
    }

    /**
     * 补充流程实例信息（保留原方法用于单个实例查询）
     *
     * @param instance 流程实例
     */
    private void enrichInstanceInfo(OaWorkflowInstance instance) {
        try {
            // 补充流程名称
            if (instance.getWorkflowName() == null && instance.getDefinitionKey() != null) {
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(instance.getDefinitionKey())
                    .latestVersion()
                    .singleResult();
                if (processDefinition != null) {
                    instance.setWorkflowName(processDefinition.getName());
                }
            }

            // 补充当前任务信息
            if (instance.getProcessInstanceId() != null && "1".equals(instance.getStatus())) {
                List<Task> currentTasks = taskService.createTaskQuery()
                    .processInstanceId(instance.getProcessInstanceId())
                    .active()
                    .list();

                if (!currentTasks.isEmpty()) {
                    Task currentTask = currentTasks.get(0);
                    instance.setCurrentTaskName(currentTask.getName());

                    // 设置当前处理人，处理直接分配和候选组的情况
                    String assigneeDisplayName = getTaskAssigneeDisplayName(currentTask);
                    instance.setCurrentAssignee(assigneeDisplayName);

                    // 如果有多个并行任务，显示所有处理人
                    if (currentTasks.size() > 1) {
                        StringBuilder assignees = new StringBuilder();
                        for (Task task : currentTasks) {
                            if (assignees.length() > 0) {
                                assignees.append(",");
                            }
                            String taskAssignee = getTaskAssigneeDisplayName(task);
                            if (taskAssignee != null && !taskAssignee.isEmpty()) {
                                assignees.append(taskAssignee);
                            }
                        }
                        instance.setCurrentAssignee(assignees.toString());
                    }
                }
            }
        } catch (Exception e) {
            // 忽略异常，避免影响列表查询
            e.printStackTrace();
        }
    }

    /**
     * 查询流程实例详情
     * 
     * @param instanceId 实例ID
     * @return 流程实例
     */
    @Override
    public OaWorkflowInstance selectOaWorkflowInstanceByInstanceId(Long instanceId)
    {
        return workflowInstanceMapper.selectOaWorkflowInstanceByInstanceId(instanceId);
    }

    /**
     * 终止流程实例
     *
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean terminateProcess(Long instanceId, String reason)
    {
        try {
            OaWorkflowInstance instance = workflowInstanceMapper.selectOaWorkflowInstanceByInstanceId(instanceId);
            if (instance != null && instance.getProcessInstanceId() != null) {
                // 删除流程实例
                runtimeService.deleteProcessInstance(instance.getProcessInstanceId(), reason);

                // 更新实例状态
                instance.setStatus("3"); // 已终止
                instance.setEndTime(new Date());
                workflowInstanceMapper.updateOaWorkflowInstance(instance);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 通过流程实例ID终止流程
     *
     * @param processInstanceId 流程实例ID
     * @param reason 终止原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean terminateProcessByInstanceId(String processInstanceId, String reason)
    {
        try {
            // 直接删除流程实例
            runtimeService.deleteProcessInstance(processInstanceId, reason);

            // 查找并更新对应的工作流实例记录
            OaWorkflowInstance instance = workflowInstanceMapper.selectOaWorkflowInstanceByProcessInstanceId(processInstanceId);
            if (instance != null) {
                instance.setStatus("3"); // 已终止
                instance.setEndTime(new Date());
                workflowInstanceMapper.updateOaWorkflowInstance(instance);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 转办任务
     *
     * @param taskId 任务ID
     * @param targetUserId 目标用户ID
     * @param comment 转办说明
     * @return 结果
     */
    @Override
    public boolean delegateTask(String taskId, Long targetUserId, String comment)
    {
        try {
            // 根据用户ID获取用户名，因为Flowable使用用户名作为assignee
            SysUser targetUser = userService.selectUserById(targetUserId);
            if (targetUser == null) {
                throw new RuntimeException("目标用户不存在: " + targetUserId);
            }

            // 使用setAssignee重新分配任务，使用用户名而不是用户ID
            taskService.setAssignee(taskId, targetUser.getUserName());

            // 添加转办评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, "转办：" + comment);
            }

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取流程图
     * 
     * @param processInstanceId 流程实例ID
     * @return 流程图字节数组
     */
    @Override
    public byte[] getProcessDiagram(String processInstanceId)
    {
        try {
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
            
            if (processInstance != null) {
                InputStream inputStream = repositoryService.getProcessDiagram(processInstance.getProcessDefinitionId());
                if (inputStream != null) {
                    byte[] buffer = new byte[inputStream.available()];
                    inputStream.read(buffer);
                    inputStream.close();
                    return buffer;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 部署流程定义
     *
     * @param workflowName 流程名称
     * @param workflowKey 流程标识
     * @param bpmnXml BPMN XML内容
     * @return 部署ID
     */
    @Override
    public String deployProcess(String workflowName, String workflowKey, String bpmnXml)
    {
        try {
            // 清理BPMN XML中的无效属性
            String cleanedBpmnXml = cleanInvalidBpmnAttributes(bpmnXml);

            Deployment deployment = repositoryService.createDeployment()
                .name(workflowName)
                .addString(workflowKey + ".bpmn20.xml", cleanedBpmnXml)
                .deploy();
            return deployment.getId();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 清理BPMN XML中的无效属性
     *
     * @param bpmnXml 原始BPMN XML
     * @return 清理后的BPMN XML
     */
    private String cleanInvalidBpmnAttributes(String bpmnXml) {
        if (bpmnXml == null) {
            return null;
        }

        // 移除无效的dueDate=""属性
        String cleaned = bpmnXml.replaceAll("\\s+dueDate=\"\"", "");

        // 可以在这里添加其他无效属性的清理逻辑

        return cleaned;
    }

    /**
     * 修复数据库中所有工作流定义的无效BPMN属性
     *
     * @return 修复的工作流定义数量
     */
    public int fixInvalidBpmnAttributesInDatabase() {
        try {
            List<OaWorkflowDefinition> allDefinitions = workflowDefinitionMapper.selectOaWorkflowDefinitionList(new OaWorkflowDefinition());
            int fixedCount = 0;

            for (OaWorkflowDefinition definition : allDefinitions) {
                String originalXml = definition.getBpmnXml();
                if (originalXml != null && originalXml.contains("dueDate=\"\"")) {
                    String cleanedXml = cleanInvalidBpmnAttributes(originalXml);
                    definition.setBpmnXml(cleanedXml);
                    definition.setUpdateTime(DateUtils.getNowDate());
                    definition.setUpdateBy("system_fix");

                    workflowDefinitionMapper.updateOaWorkflowDefinition(definition);
                    fixedCount++;

                    System.out.println("修复工作流定义: " + definition.getWorkflowName() + " (ID: " + definition.getWorkflowId() + ")");
                }
            }

            System.out.println("总共修复了 " + fixedCount + " 个工作流定义");
            return fixedCount;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 获取流程实例的详细审批信息
     *
     * @param processInstanceId 流程实例ID
     * @return 审批信息
     */
    public Map<String, Object> getProcessApprovalInfo(String processInstanceId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 获取流程实例信息
            OaWorkflowInstance processInfo = workflowInstanceMapper.selectOaWorkflowInstanceByProcessInstanceId(processInstanceId);
            if (processInfo != null) {
                enrichInstanceInfo(processInfo);
                result.put("processInfo", processInfo);
            }

            // 获取已完成的任务（审批历史）
            List<HistoricTaskInstance> completedTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByHistoricTaskInstanceEndTime()
                .asc()
                .list();

            List<Map<String, Object>> approvalHistory = new ArrayList<>();
            for (HistoricTaskInstance task : completedTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskName", task.getName());
                taskInfo.put("assignee", task.getAssignee());
                taskInfo.put("startTime", task.getStartTime());
                taskInfo.put("endTime", task.getEndTime());

                // 获取审批意见
                List<Comment> comments = taskService.getTaskComments(task.getId());
                if (!comments.isEmpty()) {
                    taskInfo.put("comment", comments.get(0).getFullMessage());
                }

                approvalHistory.add(taskInfo);
            }

            // 获取当前待处理的任务
            List<Task> currentTasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .active()
                .list();

            List<Map<String, Object>> currentApprovers = new ArrayList<>();
            for (Task task : currentTasks) {
                Map<String, Object> taskInfo = new HashMap<>();
                taskInfo.put("taskName", task.getName());
                taskInfo.put("assignee", task.getAssignee());
                taskInfo.put("createTime", task.getCreateTime());
                currentApprovers.add(taskInfo);
            }

            result.put("approvalHistory", approvalHistory);
            result.put("currentApprovers", currentApprovers);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    // 转换方法已移至 WorkflowTaskDTO.fromTask() 和 WorkflowTaskDTO.fromHistoricTask()

    /**
     * 补充任务信息
     */
    private void enrichTaskInfo(WorkflowTaskDTO taskDTO, Task task) {
        try {
            // 设置处理人昵称
            if (taskDTO.getAssignee() != null) {
                SysUser assigneeUser = userService.selectUserByUserName(taskDTO.getAssignee());
                if (assigneeUser != null) {
                    taskDTO.setAssigneeName(assigneeUser.getNickName());
                }
            }

            // 获取流程实例信息
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId())
                .singleResult();

            if (processInstance != null) {
                taskDTO.setBusinessKey(processInstance.getBusinessKey());

                // 获取流程定义信息
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processInstance.getProcessDefinitionId())
                    .singleResult();

                if (processDefinition != null) {
                    taskDTO.setWorkflowName(processDefinition.getName());
                    taskDTO.setWorkflowKey(processDefinition.getKey());
                }

                // 根据 assignee（用户名）查询用户昵称
                if (taskDTO.getAssignee() != null) {
                    SysUser user = userService.selectUserByUserName(taskDTO.getAssignee());
                    if (user != null) {
                        taskDTO.setAssigneeName(user.getNickName());
                    }
                }
            }

            // 获取任务评论
            List<Comment> comments = taskService.getTaskComments(task.getId());
            if (!comments.isEmpty()) {
                taskDTO.setComment(comments.get(comments.size() - 1).getFullMessage());
            }
        } catch (Exception e) {
            // 忽略异常，避免影响任务查询
            e.printStackTrace();
        }
    }

    /**
     * 补充历史任务信息
     */
    private void enrichTaskInfo(WorkflowTaskDTO taskDTO, HistoricTaskInstance historicTask) {
        try {
            // 设置处理人昵称
            if (taskDTO.getAssignee() != null) {
                SysUser assigneeUser = userService.selectUserByUserName(taskDTO.getAssignee());
                if (assigneeUser != null) {
                    taskDTO.setAssigneeName(assigneeUser.getNickName());
                }
            }

            // 获取历史流程实例信息
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(historicTask.getProcessInstanceId())
                .singleResult();

            if (historicProcessInstance != null) {
                taskDTO.setBusinessKey(historicProcessInstance.getBusinessKey());

                // 获取流程定义信息
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(historicProcessInstance.getProcessDefinitionId())
                    .singleResult();

                if (processDefinition != null) {
                    taskDTO.setWorkflowName(processDefinition.getName());
                    taskDTO.setWorkflowKey(processDefinition.getKey());
                }

                // 根据 assignee（用户名）查询用户昵称
                if (taskDTO.getAssignee() != null) {
                    SysUser user = userService.selectUserByUserName(taskDTO.getAssignee());
                    if (user != null) {
                        taskDTO.setAssigneeName(user.getNickName());
                    }
                }
            }

            // 获取任务评论
            List<Comment> comments = taskService.getTaskComments(historicTask.getId());
            if (!comments.isEmpty()) {
                taskDTO.setComment(comments.get(comments.size() - 1).getFullMessage());
            }
        } catch (Exception e) {
            // 忽略异常，避免影响任务查询
            e.printStackTrace();
        }
    }

    @Override
    public List<WorkflowTaskDTO> selectProcessHistory(String processInstanceId) {
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
            .processInstanceId(processInstanceId)
            .orderByHistoricTaskInstanceStartTime()
            .asc()
            .list();

        List<WorkflowTaskDTO> historyTasks = new ArrayList<>();
        for (HistoricTaskInstance historicTask : historicTasks) {
            WorkflowTaskDTO taskDTO = WorkflowTaskDTO.fromHistoricTask(historicTask);
            enrichTaskInfo(taskDTO, historicTask);
            historyTasks.add(taskDTO);
        }
        return historyTasks;
    }

    @Override
    public boolean suspendProcessDefinition(String processDefinitionId) {
        try {
            repositoryService.suspendProcessDefinitionById(processDefinitionId);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean activateProcessDefinition(String processDefinitionId) {
        try {
            repositoryService.activateProcessDefinitionById(processDefinitionId);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean deleteDeployment(String deploymentId, boolean cascade) {
        try {
            if (cascade) {
                repositoryService.deleteDeployment(deploymentId, true);
            } else {
                repositoryService.deleteDeployment(deploymentId);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> selectProcessDefinitionList() {
        List<ProcessDefinition> processDefinitions = repositoryService.createProcessDefinitionQuery()
            .latestVersion()
            .orderByProcessDefinitionName()
            .asc()
            .list();
        
        List<Map<String, Object>> result = new ArrayList<>();
        for (ProcessDefinition processDefinition : processDefinitions) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", processDefinition.getId());
            map.put("key", processDefinition.getKey());
            map.put("name", processDefinition.getName());
            map.put("version", processDefinition.getVersion());
            map.put("deploymentId", processDefinition.getDeploymentId());
            map.put("suspended", processDefinition.isSuspended());
            result.add(map);
        }
        return result;
    }

    @Override
    public OaWorkflowInstance selectOaWorkflowInstanceByBusinessKey(String businessKey) {
        return workflowInstanceMapper.selectOaWorkflowInstanceByBusinessKey(businessKey);
    }

    @Override
    public List<OaWorkflowInstance> selectUserProcessInstances(Long userId) {
        // 使用现有的查询方法
        OaWorkflowInstance queryParam = new OaWorkflowInstance();
        queryParam.setStartUserId(userId);
        return workflowInstanceMapper.selectOaWorkflowInstanceList(queryParam);
    }

    @Override
    public List<Map<String, Object>> getNextAssignees(String taskId) {
        List<Map<String, Object>> assignees = new ArrayList<>();

        try {
            // 获取当前任务信息
            Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (currentTask == null) {
                return assignees;
            }

            // 获取流程定义
            ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(currentTask.getProcessDefinitionId())
                .singleResult();

            // 获取BPMN模型
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());

            // 获取当前任务的流程元素
            FlowElement currentElement = bpmnModel.getMainProcess().getFlowElement(currentTask.getTaskDefinitionKey());

            if (currentElement instanceof UserTask) {
                UserTask currentUserTask = (UserTask) currentElement;

                // 获取当前任务的出口连线
                List<SequenceFlow> outgoingFlows = currentUserTask.getOutgoingFlows();

                boolean needSelectNextAssignee = false;

                // 检查下一个节点是否需要选择处理人
                for (SequenceFlow flow : outgoingFlows) {
                    FlowElement nextElement = flow.getTargetFlowElement();

                    if (nextElement instanceof UserTask) {
                        UserTask nextUserTask = (UserTask) nextElement;

                        // 检查下一个任务是否是"任意审批人"节点
                        // 判断条件：assignee为变量表达式且包含nextAssignee
                        String assignee = nextUserTask.getAssignee();
                        if (assignee != null && assignee.contains("${nextAssignee}")) {
                            needSelectNextAssignee = true;
                            break;
                        }
                    }
                }

                // 只有当下一个节点是任意审批人节点时，才返回用户列表
                if (needSelectNextAssignee) {
                    List<SysUser> allUsers = userService.selectAllActiveUsers();

                    for (SysUser user : allUsers) {
                        Map<String, Object> assignee = new HashMap<>();
                        assignee.put("userId", user.getUserId());
                        assignee.put("userName", user.getUserName());
                        assignee.put("nickName", user.getNickName());
                        assignee.put("activityId", "");
                        assignee.put("activityName", "任意审批人");
                        assignees.add(assignee);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return assignees;
    }



    @Override
    public boolean rejectTask(String taskId, String targetActivityId, String comment) {
        try {
            // 添加回退评论
            if (comment != null && !comment.isEmpty()) {
                taskService.addComment(taskId, null, "回退：" + comment);
            }

            // 使用Flowable的任务回退功能
            // 注意：这需要特定的配置和权限
            runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(taskService.createTaskQuery().taskId(taskId).singleResult().getProcessInstanceId())
                .moveActivityIdTo(taskService.createTaskQuery().taskId(taskId).singleResult().getTaskDefinitionKey(), targetActivityId)
                .changeState();

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getReturnNodes(String taskId) {
        List<Map<String, Object>> returnNodes = new ArrayList<>();

        try {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task != null) {
                // 获取流程实例的历史任务，作为可退回的节点
                List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId())
                    .finished()
                    .orderByHistoricTaskInstanceEndTime()
                    .desc()
                    .list();

                // 去重并构建可退回节点列表
                Set<String> addedNodes = new HashSet<>();
                for (HistoricTaskInstance historicTask : historicTasks) {
                    String nodeKey = historicTask.getTaskDefinitionKey();
                    if (!addedNodes.contains(nodeKey) && !nodeKey.equals(task.getTaskDefinitionKey())) {
                        Map<String, Object> node = new HashMap<>();
                        node.put("nodeId", nodeKey);
                        node.put("nodeName", historicTask.getName());
                        node.put("assignee", historicTask.getAssignee());
                        node.put("endTime", historicTask.getEndTime());
                        returnNodes.add(node);
                        addedNodes.add(nodeKey);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return returnNodes;
    }

    /**
     * 完成流程实例
     *
     * @param processInstanceId 流程实例ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean completeProcessInstance(String processInstanceId) {
        try {
            // 查找并更新对应的工作流实例记录
            OaWorkflowInstance instance = workflowInstanceMapper.selectOaWorkflowInstanceByProcessInstanceId(processInstanceId);
            if (instance != null) {
                instance.setStatus("2"); // 已完成
                instance.setEndTime(new Date());
                instance.setUpdateTime(new Date());
                instance.setUpdateBy("system");
                workflowInstanceMapper.updateOaWorkflowInstance(instance);

                System.out.println("流程实例已完成: " + processInstanceId + " (实例ID: " + instance.getInstanceId() + ")");
                return true;
            } else {
                System.err.println("未找到对应的工作流实例记录: " + processInstanceId);
                return false;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取处理人显示名称（昵称）
     *
     * @param assignee 处理人用户名
     * @return 显示名称
     */
    private String getAssigneeDisplayName(String assignee) {
        if (assignee == null || assignee.isEmpty()) {
            return "";
        }

        try {
            SysUser user = userService.selectUserByUserName(assignee);
            if (user != null) {
                return user.getNickName() + "(" + assignee + ")";
            }
        } catch (Exception e) {
            // 忽略异常，返回原始用户名
        }

        return assignee;
    }

    /**
     * 获取任务分配人显示名称，处理直接分配和候选组的情况
     *
     * @param task 任务对象
     * @return 显示名称
     */
    private String getTaskAssigneeDisplayName(Task task) {
        // 如果任务已经分配给具体用户
        if (task.getAssignee() != null && !task.getAssignee().isEmpty()) {
            return getAssigneeDisplayName(task.getAssignee());
        }

        // 如果任务分配给候选用户或候选组，显示对应的用户名称
        try {
            List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
            List<String> candidateUsers = new ArrayList<>();
            List<String> candidateGroups = new ArrayList<>();

            for (IdentityLink identityLink : identityLinks) {
                if ("candidate".equals(identityLink.getType())) {
                    if (identityLink.getUserId() != null) {
                        candidateUsers.add(identityLink.getUserId());
                    } else if (identityLink.getGroupId() != null) {
                        candidateGroups.add(identityLink.getGroupId());
                    }
                }
            }

            // 优先显示候选用户
            if (!candidateUsers.isEmpty()) {
                if (candidateUsers.size() <= 3) {
                    return candidateUsers.stream()
                        .map(this::getAssigneeDisplayName)
                        .collect(Collectors.joining("、"));
                } else {
                    String firstThree = candidateUsers.stream()
                        .limit(3)
                        .map(this::getAssigneeDisplayName)
                        .collect(Collectors.joining("、"));
                    return firstThree + "等" + candidateUsers.size() + "人";
                }
            }

            // 如果没有候选用户，显示候选组
            if (!candidateGroups.isEmpty()) {
                for (String groupId : candidateGroups) {
                    String groupDisplayName = getCandidateGroupDisplayName(groupId);
                    if (groupDisplayName != null && !groupDisplayName.isEmpty()) {
                        return groupDisplayName;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return "待分配";
    }

    /**
     * 获取候选组显示名称
     *
     * @param groupId 候选组ID
     * @return 显示名称
     */
    private String getCandidateGroupDisplayName(String groupId) {
        try {
            // 使用WorkflowAssigneeService获取候选用户，这样会正确处理角色前缀
            List<SysUser> users = assigneeService.getCandidateUsers(groupId);

            if (users != null && !users.isEmpty()) {
                // 如果有用户，显示用户名称（限制显示数量，避免过长）
                if (users.size() <= 3) {
                    return users.stream()
                        .map(user -> user.getNickName())
                        .collect(Collectors.joining("、"));
                } else {
                    // 如果用户太多，显示前3个用户加上"等"
                    String firstThree = users.stream()
                        .limit(3)
                        .map(user -> user.getNickName())
                        .collect(Collectors.joining("、"));
                    return firstThree + "等" + users.size() + "人";
                }
            } else {
                // 如果没有用户，尝试查询部门角色组
                return getDeptRoleDisplayName(groupId);
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 异常情况下返回部门角色组名称
            return getDeptRoleDisplayName(groupId);
        }
    }

    /**
     * 获取部门角色组显示名称
     */
    private String getDeptRoleDisplayName(String groupId) {
        // 处理部门角色组格式：dept_xxx_role_yyy
        if (groupId.startsWith("dept_") && groupId.contains("_role_")) {
            try {
                String[] parts = groupId.split("_role_");
                if (parts.length == 2) {
                    String deptPart = parts[0]; // dept_xxx
                    String roleKey = parts[1];  // yyy

                    // 提取部门ID
                    String deptIdStr = deptPart.substring(5); // 去掉"dept_"前缀
                    Long deptId = Long.parseLong(deptIdStr);

                    // 查询部门信息
                    SysDept dept = deptService.selectDeptById(deptId);
                    String deptName = dept != null ? dept.getDeptName() : "未知部门";

                    // 查询该部门该角色的用户
                    List<SysUser> deptRoleUsers = userService.selectUsersByRoleKey(roleKey)
                        .stream()
                        .filter(user -> deptId.equals(user.getDeptId()))
                        .collect(Collectors.toList());

                    if (!deptRoleUsers.isEmpty()) {
                        if (deptRoleUsers.size() <= 3) {
                            return deptRoleUsers.stream()
                                .map(user -> user.getNickName())
                                .collect(Collectors.joining("、"));
                        } else {
                            String firstThree = deptRoleUsers.stream()
                                .limit(3)
                                .map(user -> user.getNickName())
                                .collect(Collectors.joining("、"));
                            return firstThree + "等" + deptRoleUsers.size() + "人";
                        }
                    } else {
                        // 根据角色key返回角色名称
                        String roleName = getRoleDisplayName(roleKey);
                        return deptName + roleName;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 处理普通角色组
        return getRoleDisplayName(groupId);
    }

    /**
     * 获取角色显示名称
     */
    private String getRoleDisplayName(String roleKey) {
        switch (roleKey) {
            case "clerk":
                return "办公室科员";
            case "dept_manager":
                return "科室负责人";
            case "office_manager":
                return "办公室负责人";
            case "fgld":
                return "分管领导";
            case "sj":
                return "书记";
            default:
                return roleKey + "(角色组)";
        }
    }

    /**
     * 获取可选择的分管领导列表
     */
    @Override
    public List<Map<String, Object>> getAvailableLeaders() {
        try {
            List<Map<String, Object>> result = new ArrayList<>();

            // 查询具有分管领导角色(fgld)的用户
            List<SysUser> leaders = userService.selectUsersByRoleKey("fgld");

            for (SysUser user : leaders) {
                if ("0".equals(user.getStatus())) { // 只返回启用的用户
                    Map<String, Object> leader = new HashMap<>();
                    leader.put("userId", user.getUserName());
                    leader.put("userName", user.getUserName());
                    leader.put("nickName", user.getNickName());
                    leader.put("deptName", user.getDept() != null ? user.getDept().getDeptName() : "");
                    result.add(leader);
                }
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定分管领导下的科室负责人列表
     */
    @Override
    public List<Map<String, Object>> getAvailableManagers(String leaderId) {
        try {
            List<Map<String, Object>> result = new ArrayList<>();

            // 根据分管领导用户名查询用户ID
            SysUser leader = userService.selectUserByUserName(leaderId);
            if (leader == null) {
                return result;
            }

            // 查询该分管领导负责的部门（通过supervising_leader_id字段）
            // 使用selectDeptList方法查询所有部门，然后筛选出分管领导为指定用户的部门
            SysDept queryDept = new SysDept();
            queryDept.setStatus("0"); // 只查询启用的部门
            List<SysDept> allDepts = deptService.selectDeptList(queryDept);

            List<SysDept> depts = allDepts.stream()
                .filter(dept -> leader.getUserId().equals(dept.getSupervisingLeaderId()))
                .collect(Collectors.toList());

            for (SysDept dept : depts) {
                if (dept.getLeader() != null) {
                    // 查询部门负责人信息（leader字段存储的是用户名）
                    SysUser manager = userService.selectUserByUserName(dept.getLeader());
                    if (manager != null && "0".equals(manager.getStatus())) {
                        Map<String, Object> managerInfo = new HashMap<>();
                        managerInfo.put("userId", manager.getUserId());
                        managerInfo.put("userName", manager.getUserName());
                        managerInfo.put("nickName", manager.getNickName());
                        managerInfo.put("deptId", dept.getDeptId().toString());
                        managerInfo.put("deptName", dept.getDeptName());
                        result.add(managerInfo);
                    }
                }
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定科室负责人下的经办人列表
     */
    @Override
    public List<Map<String, Object>> getAvailableHandlers(String managerId) {
        try {
            List<Map<String, Object>> result = new ArrayList<>();

            // 根据科室负责人用户名查询用户信息
            SysUser manager = userService.selectUserByUserName(managerId);
            if (manager == null) {
                System.out.println("未找到科室负责人: " + managerId);
                return result;
            }

            // 使用不受数据权限限制的查询方法
            // 查询所有启用的用户，然后筛选出该科长负责部门下的人员
            List<SysUser> allActiveUsers = userService.selectAllActiveUsers();

            // 从所有用户中筛选出该科长负责的部门
            // 通过用户的部门信息来确定该科长负责的部门
            Set<Long> managedDeptIds = new HashSet<>();
            Map<Long, String> deptNameMap = new HashMap<>();

            for (SysUser user : allActiveUsers) {
                if (user.getDept() != null &&
                    manager.getUserId().toString().equals(user.getDept().getLeader())) {
                    managedDeptIds.add(user.getDeptId());
                    deptNameMap.put(user.getDeptId(), user.getDept().getDeptName());
                }
            }

            if (managedDeptIds.isEmpty()) {
                System.out.println("科室负责人 " + manager.getNickName() + " 未负责任何部门");
                return result;
            }

            // 筛选出这些部门下的所有用户（排除科长自己）
            List<SysUser> handlers = allActiveUsers.stream()
                .filter(user -> managedDeptIds.contains(user.getDeptId()) &&
                               !user.getUserId().equals(manager.getUserId()))
                .collect(Collectors.toList());

            System.out.println("科室负责人 " + manager.getNickName() + " 负责 " + managedDeptIds.size() + " 个部门，共有 " + handlers.size() + " 个经办人");

            for (String deptName : deptNameMap.values()) {
                System.out.println("  - " + deptName);
            }

            // 转换为返回格式
            for (SysUser user : handlers) {
                Map<String, Object> handler = new HashMap<>();
                handler.put("userId", user.getUserId());
                handler.put("userName", user.getUserName());
                handler.put("nickName", user.getNickName());
                handler.put("deptId", user.getDeptId());
                handler.put("deptName", deptNameMap.get(user.getDeptId()));
                handler.put("phonenumber", user.getPhonenumber());
                handler.put("email", user.getEmail());
                result.add(handler);
            }

            System.out.println("科室负责人 " + manager.getNickName() + " 负责 " + managedDeptIds.size() +
                             " 个部门，共有 " + result.size() + " 个经办人");

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有可分配的人员列表（用于特办流程）
     */
    @Override
    public List<Map<String, Object>> getAllAvailablePersonnel() {
        try {
            // 暂时返回模拟数据
            List<Map<String, Object>> result = new ArrayList<>();

            // 添加一些常用人员
            String[][] users = {
                {"clerk_office1", "办公室小王", "办公室", "科员"},
                {"clerk_office2", "办公室小李", "办公室", "科员"},
                {"manager_office", "办公室主任", "办公室", "科室负责人"},
                {"clerk_hr1", "人事科小赵", "人事科", "科员"},
                {"manager_hr", "人事科长", "人事科", "科室负责人"},
                {"clerk_operation1", "运管科小钱", "运管科", "科员"},
                {"manager_operation", "运管科长", "运管科", "科室负责人"},
                {"leader_admin", "张副主任", "行政管理", "分管领导"},
                {"leader_operation", "赵副主任", "运营管理", "分管领导"},
                {"secretary", "王书记", "领导班子", "书记"}
            };

            for (String[] user : users) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", user[0]);
                userInfo.put("userName", user[0]);
                userInfo.put("nickName", user[1]);
                userInfo.put("deptName", user[2]);
                userInfo.put("roles", java.util.Arrays.asList(user[3]));
                result.add(userInfo);
            }

            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    /**
     * 催办任务
     */
    @Override
    public boolean urgeTask(String taskId, String message) {
        try {
            // 获取任务信息
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                throw new RuntimeException("任务不存在: " + taskId);
            }

            // 添加催办评论
            String urgeComment = "【催办】" + (message != null ? message : "请及时处理此任务");
            taskService.addComment(taskId, null, urgeComment);

            // 这里可以添加发送通知的逻辑
            // 例如：发送邮件、短信、系统通知等
            System.out.println("催办任务: " + task.getName() + ", 处理人: " + task.getAssignee() + ", 消息: " + urgeComment);

            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 自动完成第一个任务
     */
    @Override
    public boolean autoCompleteFirstTask(String processInstanceId, String taskDefinitionKey, String assignee) {
        try {
            // 查询指定的任务
            List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .taskDefinitionKey(taskDefinitionKey)
                .list();

            if (!tasks.isEmpty()) {
                Task firstTask = tasks.get(0);

                // 将任务分配给指定人员
                taskService.setAssignee(firstTask.getId(), assignee);

                // 添加自动完成的评论
                taskService.addComment(firstTask.getId(), processInstanceId,
                    "系统自动完成：收文登记已完成，提交审批。");

                // 自动完成任务
                Map<String, Object> variables = new HashMap<>();
                variables.put("result", "approve");
                variables.put("startUserId", assignee); // 记录流程创建人

                taskService.complete(firstTask.getId(), variables);

                System.out.println("自动完成任务成功: " + firstTask.getName() + " (" + firstTask.getId() + ")");
                return true;
            } else {
                System.err.println("未找到指定任务: " + taskDefinitionKey + ", 流程实例: " + processInstanceId);
                return false;
            }
        } catch (Exception e) {
            System.err.println("自动完成任务失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 自动完成书记收文任务（特批流程专用）
     */
    public boolean autoCompleteSecretaryTask(String processInstanceId, String taskDefinitionKey, String assignee, Map<String, Object> variables) {
        try {
            // 查询指定的任务
            List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .taskDefinitionKey(taskDefinitionKey)
                .list();

            if (!tasks.isEmpty()) {
                Task secretaryTask = tasks.get(0);

                // 将任务分配给指定人员（书记）
                taskService.setAssignee(secretaryTask.getId(), assignee);

                // 添加自动完成的评论
                String selectedAssignee = (String) variables.get("selectedAssignee");
                taskService.addComment(secretaryTask.getId(), processInstanceId,
                    "系统自动完成：书记收文处理完成，已指定 " + selectedAssignee + " 处理。");

                // 自动完成任务，传递选择的处理人员
                taskService.complete(secretaryTask.getId(), variables);

                System.out.println("自动完成书记收文任务成功: " + secretaryTask.getName() + " (" + secretaryTask.getId() + ")，指定处理人员: " + selectedAssignee);
                return true;
            } else {
                System.err.println("未找到书记收文任务: " + taskDefinitionKey + ", 流程实例: " + processInstanceId);
                return false;
            }
        } catch (Exception e) {
            System.err.println("自动完成书记收文任务失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 流程完成后更新文档状态为已归档
     */
    private void updateDocumentStatusAfterComplete(String businessKey) {
        try {
            System.out.println("流程完成，准备更新文档状态，业务主键: " + businessKey);

            if (businessKey.startsWith("receive_doc_")) {
                // 收文流程完成
                String docId = businessKey.substring("receive_doc_".length());
                System.out.println("收文流程完成，更新文档状态，文档ID: " + docId);

                // 使用Spring上下文获取收文服务
                try {
                    Object receiveService = com.base.common.utils.spring.SpringUtils.getBean("oaDocumentReceiveServiceImpl");
                    if (receiveService != null) {
                        // 通过反射调用更新状态方法
                        java.lang.reflect.Method method = receiveService.getClass()
                            .getMethod("updateStatusAfterComplete", String.class);
                        method.invoke(receiveService, docId);
                        System.out.println("收文状态更新调用成功，文档ID: " + docId);
                    } else {
                        System.err.println("未找到收文服务Bean");
                    }
                } catch (Exception e) {
                    System.err.println("更新收文状态失败: " + e.getMessage());
                    e.printStackTrace();
                }

            } else if (businessKey.startsWith("send_doc_")) {
                // 发文流程完成
                String docId = businessKey.substring("send_doc_".length());
                System.out.println("发文流程完成，更新文档状态，文档ID: " + docId);

                // 使用Spring上下文获取发文服务
                try {
                    Object sendService = com.base.common.utils.spring.SpringUtils.getBean("oaDocumentSendServiceImpl");
                    if (sendService != null) {
                        // 通过反射调用更新状态方法
                        java.lang.reflect.Method method = sendService.getClass()
                            .getMethod("updateStatusAfterComplete", String.class);
                        method.invoke(sendService, docId);
                        System.out.println("发文状态更新调用成功，文档ID: " + docId);
                    } else {
                        System.err.println("未找到发文服务Bean");
                    }
                } catch (Exception e) {
                    System.err.println("更新发文状态失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                System.out.println("未识别的业务主键格式: " + businessKey);
            }
        } catch (Exception e) {
            System.err.println("更新文档状态失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证用户是否有权限完成任务
     */
    private boolean validateTaskPermission(Task task, String username) {
        try {
            System.out.println("验证用户权限: " + username + " -> 任务: " + task.getId());

            // 检查任务是否直接分配给当前用户
            if (username.equals(task.getAssignee())) {
                System.out.println("任务直接分配给用户: " + username);
                return true;
            }

            // 检查用户是否在候选用户列表中
            List<org.flowable.identitylink.api.IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
            for (org.flowable.identitylink.api.IdentityLink link : identityLinks) {
                if ("candidate".equals(link.getType()) && username.equals(link.getUserId())) {
                    System.out.println("用户在候选用户列表中: " + username);
                    return true;
                }
            }

            // 检查用户是否在候选组中
            SysUser currentUser = userService.selectUserByUserName(username);
            if (currentUser != null) {
                List<SysRole> userRoles = roleService.selectRolesByUserId(currentUser.getUserId());

                for (SysRole role : userRoles) {
                    for (org.flowable.identitylink.api.IdentityLink link : identityLinks) {
                        if ("candidate".equals(link.getType()) && role.getRoleKey().equals(link.getGroupId())) {
                            System.out.println("用户通过角色组有权限: " + username + " -> " + role.getRoleKey());
                            return true;
                        }
                    }
                }
            }

            System.out.println("用户没有权限完成任务: " + username + " -> " + task.getId());
            return false;

        } catch (Exception e) {
            System.err.println("验证任务权限失败: " + e.getMessage());
            e.printStackTrace();
            // 权限验证失败时，为了不影响基本功能，返回true
            return true;
        }
    }


}
