package com.base.oa.workflow.config;

import com.base.oa.workflow.service.WorkflowAssigneeService;
import com.base.oa.workflow.service.WorkflowDeploymentService;
import com.base.oa.workflow.service.WorkflowStartService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 工作流启动检查器
 * 在应用启动后检查工作流系统的完整性
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Component
public class WorkflowStartupChecker implements ApplicationRunner {
    
    @Autowired
    private RepositoryService repositoryService;
    
    @Autowired
    private WorkflowAssigneeService assigneeService;
    
    @Autowired
    private WorkflowStartService workflowStartService;
    
    @Autowired
    private WorkflowDeploymentService deploymentService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("\n=== 工作流系统启动检查 ===");
        
        try {
            // 延迟一段时间，确保所有服务都已初始化
            Thread.sleep(3000);
            
            checkProcessDefinitions();
            checkCandidateGroups();
            checkServices();
            
            System.out.println("=== 工作流系统检查完成 ===\n");
            
        } catch (Exception e) {
            System.err.println("工作流系统检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查流程定义
     */
    private void checkProcessDefinitions() {
        System.out.println("1. 检查流程定义...");
        
        String[] expectedProcesses = {
            "document_receive_approval_v2",
            "document_receive_special_v2", 
            "document_send_approval_v2"
        };
        
        for (String processKey : expectedProcesses) {
            try {
                List<ProcessDefinition> definitions = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionKey(processKey)
                    .latestVersion()
                    .list();
                
                if (!definitions.isEmpty()) {
                    ProcessDefinition def = definitions.get(0);
                    System.out.println("   ✓ " + processKey + " (版本: " + def.getVersion() + 
                                     ", ID: " + def.getId() + ")");
                } else {
                    System.out.println("   ✗ " + processKey + " - 未找到");
                }
            } catch (Exception e) {
                System.out.println("   ✗ " + processKey + " - 检查失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查候选组
     */
    private void checkCandidateGroups() {
        System.out.println("2. 检查候选组...");
        
        String[] candidateGroups = {
            "role_sj", "role_fgld", "role_ksfzr", "role_clerk",
            "dept_104_clerk", "dept_104_leader"
        };
        
        for (String group : candidateGroups) {
            try {
                int count = assigneeService.getCandidateUsers(group).size();
                String displayName = assigneeService.getCandidateGroupDisplayName(group);
                System.out.println("   ✓ " + group + " (" + displayName + ") - " + count + " 个候选人");
            } catch (Exception e) {
                System.out.println("   ✗ " + group + " - 检查失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查服务
     */
    private void checkServices() {
        System.out.println("3. 检查服务...");
        
        // 检查WorkflowAssigneeService
        try {
            assigneeService.getCandidateUsers("role_sj");
            System.out.println("   ✓ WorkflowAssigneeService - 正常");
        } catch (Exception e) {
            System.out.println("   ✗ WorkflowAssigneeService - 异常: " + e.getMessage());
        }
        
        // 检查WorkflowStartService
        try {
            boolean canStart = workflowStartService.canStartProcess("document_receive_approval_v2");
            System.out.println("   ✓ WorkflowStartService - " + (canStart ? "正常" : "流程不可用"));
        } catch (Exception e) {
            System.out.println("   ✗ WorkflowStartService - 异常: " + e.getMessage());
        }
        
        // 检查RepositoryService
        try {
            long count = repositoryService.createProcessDefinitionQuery().count();
            System.out.println("   ✓ RepositoryService - 正常 (共 " + count + " 个流程定义)");
        } catch (Exception e) {
            System.out.println("   ✗ RepositoryService - 异常: " + e.getMessage());
        }
    }
}
