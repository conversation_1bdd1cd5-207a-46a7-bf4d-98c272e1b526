package com.base.oa.workflow.constants;

/**
 * 工作流候选组常量
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
public class CandidateGroupConstants {
    
    // ==================== 基于角色的候选组 ====================
    
    /** 书记角色 */
    public static final String ROLE_SECRETARY = "role_sj";
    
    /** 分管领导角色 */
    public static final String ROLE_LEADER = "role_fgld";
    
    /** 科室负责人角色 */
    public static final String ROLE_DEPT_MANAGER = "role_ksfzr";
    
    /** 科员角色 */
    public static final String ROLE_CLERK = "role_clerk";
    
    // ==================== 基于部门的候选组 ====================
    
    /** 办公室科员 */
    public static final String DEPT_104_CLERK = "dept_104_clerk";
    
    /** 办公室负责人 */
    public static final String DEPT_104_LEADER = "dept_104_leader";
    
    // ==================== 动态候选组 ====================
    
    /** 动态指定人员 */
    public static final String DYNAMIC_ASSIGNEE = "dynamic_assignee";
    
    /** 多实例动态分配 */
    public static final String MULTI_INSTANCE_DYNAMIC = "multi_instance_dynamic";
    
    // ==================== 候选组解析方法 ====================
    
    /**
     * 判断是否为角色候选组
     */
    public static boolean isRoleGroup(String candidateGroup) {
        return candidateGroup != null && candidateGroup.startsWith("role_");
    }
    
    /**
     * 判断是否为部门候选组
     */
    public static boolean isDeptGroup(String candidateGroup) {
        return candidateGroup != null && candidateGroup.startsWith("dept_");
    }
    
    /**
     * 判断是否为动态候选组
     */
    public static boolean isDynamicGroup(String candidateGroup) {
        return candidateGroup != null && 
               (candidateGroup.equals(DYNAMIC_ASSIGNEE) || candidateGroup.equals(MULTI_INSTANCE_DYNAMIC));
    }
    
    /**
     * 从角色候选组中提取角色标识
     */
    public static String extractRoleKey(String candidateGroup) {
        if (isRoleGroup(candidateGroup)) {
            return candidateGroup.substring(5); // 去掉 "role_" 前缀
        }
        return null;
    }
    
    /**
     * 从部门候选组中提取部门ID和角色标识
     */
    public static DeptRoleInfo extractDeptRoleInfo(String candidateGroup) {
        if (isDeptGroup(candidateGroup)) {
            String[] parts = candidateGroup.split("_");
            if (parts.length >= 3) {
                try {
                    Long deptId = Long.parseLong(parts[1]);
                    String roleKey = parts[2];
                    return new DeptRoleInfo(deptId, roleKey);
                } catch (NumberFormatException e) {
                    // 忽略解析错误
                }
            }
        }
        return null;
    }
    
    /**
     * 部门角色信息
     */
    public static class DeptRoleInfo {
        private Long deptId;
        private String roleKey;
        
        public DeptRoleInfo(Long deptId, String roleKey) {
            this.deptId = deptId;
            this.roleKey = roleKey;
        }
        
        public Long getDeptId() {
            return deptId;
        }
        
        public String getRoleKey() {
            return roleKey;
        }
    }
}
