package com.base.oa.workflow.listener;

import com.base.oa.document.service.IOaDocumentReceiveService;
import com.base.oa.document.service.IOaDocumentSendService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 任务完成监听器
 * 用于处理收文审批流程中的任务完成事件
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Component
public class TaskCompleteListener implements TaskListener {

    @Autowired
    private IOaDocumentReceiveService documentReceiveService;

    @Autowired
    private IOaDocumentSendService documentSendService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            // 获取流程变量
            String processInstanceId = delegateTask.getProcessInstanceId();

            // 尝试从流程变量中获取文档ID
            Long docId = null;
            Object docIdVar = delegateTask.getVariable("docId");
            if (docIdVar != null) {
                docId = Long.valueOf(docIdVar.toString());
            }

            // 如果从变量中获取不到，尝试解析业务主键
            if (docId == null) {
                Object businessKeyVar = delegateTask.getVariable("businessKey");
                if (businessKeyVar != null) {
                    docId = parseDocumentId(businessKeyVar.toString());
                }
            }

            if (docId == null) {
                return;
            }

            String taskName = delegateTask.getName();
            String assignee = delegateTask.getAssignee();

            // 处理书记审批的动态分管领导指派（收文审批流程）
            if ("书记审批".equals(taskName)) {
                handleSecretaryApprovalAssignment(delegateTask);
            }

            // 处理书记收文的任意人员指派（收文特批流程）
            if ("书记收文".equals(taskName)) {
                handleSecretaryReceiveAssignment(delegateTask);
            }

            // 根据任务名称更新文档状态
            updateDocumentStatus(docId, taskName, assignee, processInstanceId, delegateTask);

        } catch (Exception e) {
            e.printStackTrace();
            // 记录日志但不影响流程继续执行
        }
    }

    /**
     * 从业务主键中解析文档ID
     */
    private Long parseDocumentId(String businessKey) {
        try {
            if (businessKey != null && businessKey.startsWith("receive_doc_")) {
                String docIdStr = businessKey.replace("receive_doc_", "").replace("_special", "");
                return Long.parseLong(docIdStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据任务名称更新文档状态
     */
    private void updateDocumentStatus(Long docId, String taskName, String assignee, String processInstanceId, DelegateTask delegateTask) {
        try {
            String currentStep = taskName;
            String currentAssignee = assignee;

            // 从流程变量中获取业务主键判断是收文还是发文
            Object businessKeyVar = delegateTask.getVariable("businessKey");
            String businessKey = businessKeyVar != null ? businessKeyVar.toString() : "";
            boolean isReceiveDocument = businessKey != null && businessKey.startsWith("receive_doc_");
            boolean isSendDocument = businessKey != null && businessKey.startsWith("send_doc_");

            if (isReceiveDocument) {
                // 收文流程的任务节点处理
                switch (taskName) {
                    case "办公室主任登记":
                    case "办公室主任初审":
                        currentStep = "办公室主任处理中";
                        break;
                    case "书记审批":
                    case "书记特批审批":
                    case "书记收文":
                        currentStep = "书记审批中";
                        break;
                    case "办公室归档":
                    case "办公室登记处理":
                        currentStep = "办公室归档中";
                        break;
                    case "分发给任意人员":
                        currentStep = "分发给任意人员处理中";
                        break;
                    default:
                        currentStep = taskName;
                        break;
                }

                // 更新收文的工作流状态
                documentReceiveService.updateWorkflowStatus(docId,
                    processInstanceId, currentStep, currentAssignee);

            } else if (isSendDocument) {
                // 发文流程的任务节点处理
                switch (taskName) {
                    case "部门负责人审批":
                        currentStep = "部门负责人审批中";
                        break;
                    case "办公室审核":
                        currentStep = "办公室审核中";
                        break;
                    case "领导审批":
                        currentStep = "领导审批中";
                        break;
                    case "分管领导审批":
                        currentStep = "分管领导审批中";
                        break;
                    case "主要领导审批":
                        currentStep = "主要领导审批中";
                        break;
                    default:
                        currentStep = taskName;
                        break;
                }

                // 更新发文的工作流状态
                documentSendService.updateWorkflowStatus(docId,
                    processInstanceId, currentStep, currentAssignee);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理书记审批时的动态分管领导指派
     */
    private void handleSecretaryApprovalAssignment(DelegateTask delegateTask) {
        try {
            String processInstanceId = delegateTask.getProcessInstanceId();

            // 获取书记选择的分管领导列表
            Object selectedLeadersVar = delegateTask.getVariable("selectedLeaders");
            if (selectedLeadersVar == null) {
                System.out.println("书记未选择分管领导，将启用所有分管领导审批");
                // 设置默认值，启用所有分管领导
                runtimeService.setVariable(processInstanceId, "selectedLeaders", "zhangsan,liuer,wutao,wangyu,yangyang");
                return;
            }

            String selectedLeadersStr;
            if (selectedLeadersVar instanceof String) {
                selectedLeadersStr = (String) selectedLeadersVar;
            } else if (selectedLeadersVar instanceof List) {
                List<String> selectedLeaders = (List<String>) selectedLeadersVar;
                selectedLeadersStr = String.join(",", selectedLeaders);
            } else {
                System.out.println("selectedLeaders变量格式不正确: " + selectedLeadersVar);
                selectedLeadersStr = "zhangsan,liuer,wutao,wangyu,yangyang"; // 默认启用所有
            }

            System.out.println("书记选择的分管领导: " + selectedLeadersStr);

            // 设置流程变量，用于包含网关的条件判断
            runtimeService.setVariable(processInstanceId, "selectedLeaders", selectedLeadersStr);

            // 为多实例任务设置分管领导列表
            List<String> selectedLeadersList = java.util.Arrays.asList(selectedLeadersStr.split(","));
            runtimeService.setVariable(processInstanceId, "selectedLeadersList", selectedLeadersList);
            System.out.println("设置多实例分管领导列表: " + selectedLeadersList);

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("处理书记审批动态指派时出错: " + e.getMessage());
        }
    }

    /**
     * 处理书记收文时的任意人员指派（收文特批流程）
     */
    private void handleSecretaryReceiveAssignment(DelegateTask delegateTask) {
        try {
            String processInstanceId = delegateTask.getProcessInstanceId();

            // 获取书记选择的处理人员
            Object nextAssigneeVar = delegateTask.getVariable("nextAssignee");
            if (nextAssigneeVar == null) {
                System.out.println("书记未选择处理人员，使用默认处理人");
                // 设置默认处理人
                runtimeService.setVariable(processInstanceId, "nextAssignee", "admin");
                return;
            }

            String nextAssignee = nextAssigneeVar.toString();
            System.out.println("书记选择的处理人员: " + nextAssignee);

            // 设置流程变量，用于下一个任务的动态分配
            runtimeService.setVariable(processInstanceId, "nextAssignee", nextAssignee);

        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("处理书记收文动态指派时出错: " + e.getMessage());
        }
    }
}
