package com.base.oa.workflow.service;

import com.base.oa.workflow.domain.OaWorkflowDefinition;
import com.base.oa.workflow.mapper.OaWorkflowDefinitionMapper;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 工作流部署服务
 * 负责部署新的BPMN流程定义
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Service
public class WorkflowDeploymentService {
    
    @Autowired
    private RepositoryService repositoryService;
    
    @Autowired
    private OaWorkflowDefinitionMapper workflowDefinitionMapper;
    
    /**
     * 应用启动后自动部署新的流程定义
     */
    @PostConstruct
    public void deployNewProcessDefinitions() {
        try {
            // 部署收文审批流程V2
            deployProcessFromClasspath(
                "document_receive_approval_v2",
                "收文审批流程V2",
                "processes/document_receive_approval_v2.bpmn20.xml",
                "基于部门角色的收文审批流程"
            );
            
            // 部署收文特办流程V2
            deployProcessFromClasspath(
                "document_receive_special_v2",
                "收文特办流程V2",
                "processes/document_receive_special_v2.bpmn20.xml",
                "书记直接处理的特办流程"
            );
            
            // 部署发文审批流程V2
            deployProcessFromClasspath(
                "document_send_approval_v2",
                "发文审批流程V2",
                "processes/document_send_approval_v2.bpmn20.xml",
                "基于部门角色的发文审批流程"
            );
            
            System.out.println("新流程定义部署完成");
            
        } catch (Exception e) {
            System.err.println("部署新流程定义时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 从classpath部署流程定义
     */
    private void deployProcessFromClasspath(String processKey, String processName, 
                                          String resourcePath, String description) {
        try {
            // 检查是否已经存在该流程定义
            OaWorkflowDefinition existing = workflowDefinitionMapper.selectOaWorkflowDefinitionByKey(processKey);
            if (existing != null) {
                System.out.println("流程定义已存在，跳过部署: " + processKey);
                return;
            }
            
            // 读取BPMN文件内容
            ClassPathResource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                System.out.println("BPMN文件不存在，跳过部署: " + resourcePath);
                return;
            }
            
            String bpmnXml;
            try (InputStream inputStream = resource.getInputStream()) {
                bpmnXml = StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
            }
            
            // 部署到Flowable引擎
            Deployment deployment = repositoryService.createDeployment()
                .name(processName)
                .addString(processKey + ".bpmn20.xml", bpmnXml)
                .deploy();
            
            // 获取流程定义ID
            String processDefinitionId = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deployment.getId())
                .singleResult()
                .getId();
            
            // 保存到数据库
            OaWorkflowDefinition workflowDefinition = new OaWorkflowDefinition();
            // 确保必填字段不为空
            workflowDefinition.setWorkflowName(processName != null ? processName : "未命名流程");
            workflowDefinition.setWorkflowKey(processKey != null ? processKey : "unknown_process");
            workflowDefinition.setWorkflowVersion(1);
            workflowDefinition.setDescription(description != null ? description : "");
            workflowDefinition.setBpmnXml(bpmnXml);
            workflowDefinition.setDeploymentId(deployment.getId());
            workflowDefinition.setProcessDefinitionId(processDefinitionId);
            workflowDefinition.setStatus("1"); // 启用状态
            workflowDefinition.setCreateTime(new Date());
            workflowDefinition.setCreateBy("system");

            System.out.println("准备插入工作流定义: " + workflowDefinition.getWorkflowName() +
                             " (Key: " + workflowDefinition.getWorkflowKey() + ")");

            workflowDefinitionMapper.insertOaWorkflowDefinition(workflowDefinition);
            
            System.out.println("成功部署流程定义: " + processName + " [" + processKey + "]");
            
        } catch (IOException e) {
            System.err.println("读取BPMN文件失败: " + resourcePath + ", 错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("部署流程定义失败: " + processKey + ", 错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 手动部署流程定义
     */
    public boolean deployProcess(String processKey, String processName, String bpmnXml, String description) {
        try {
            // 参数验证
            if (processKey == null || processKey.trim().isEmpty()) {
                System.err.println("流程标识不能为空");
                return false;
            }
            if (processName == null || processName.trim().isEmpty()) {
                System.err.println("流程名称不能为空");
                return false;
            }
            if (bpmnXml == null || bpmnXml.trim().isEmpty()) {
                System.err.println("BPMN XML不能为空");
                return false;
            }

            // 部署到Flowable引擎
            Deployment deployment = repositoryService.createDeployment()
                .name(processName)
                .addString(processKey + ".bpmn20.xml", bpmnXml)
                .deploy();
            
            // 获取流程定义ID
            String processDefinitionId = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deployment.getId())
                .singleResult()
                .getId();
            
            // 检查是否已存在，如果存在则更新，否则新增
            OaWorkflowDefinition existing = workflowDefinitionMapper.selectOaWorkflowDefinitionByKey(processKey);
            if (existing != null) {
                existing.setWorkflowName(processName);
                existing.setDescription(description);
                existing.setBpmnXml(bpmnXml);
                existing.setDeploymentId(deployment.getId());
                existing.setProcessDefinitionId(processDefinitionId);
                existing.setWorkflowVersion(existing.getWorkflowVersion() + 1);
                existing.setUpdateTime(new Date());
                existing.setUpdateBy("system");
                
                workflowDefinitionMapper.updateOaWorkflowDefinition(existing);
            } else {
                OaWorkflowDefinition workflowDefinition = new OaWorkflowDefinition();
                // 确保必填字段不为空
                workflowDefinition.setWorkflowName(processName != null ? processName : "未命名流程");
                workflowDefinition.setWorkflowKey(processKey != null ? processKey : "unknown_process");
                workflowDefinition.setWorkflowVersion(1);
                workflowDefinition.setDescription(description != null ? description : "");
                workflowDefinition.setBpmnXml(bpmnXml);
                workflowDefinition.setDeploymentId(deployment.getId());
                workflowDefinition.setProcessDefinitionId(processDefinitionId);
                workflowDefinition.setStatus("1");
                workflowDefinition.setCreateTime(new Date());
                workflowDefinition.setCreateBy("system");

                System.out.println("准备插入工作流定义: " + workflowDefinition.getWorkflowName() +
                                 " (Key: " + workflowDefinition.getWorkflowKey() + ")");

                workflowDefinitionMapper.insertOaWorkflowDefinition(workflowDefinition);
            }
            
            System.out.println("手动部署流程定义成功: " + processName + " [" + processKey + "]");
            return true;
            
        } catch (Exception e) {
            System.err.println("手动部署流程定义失败: " + processKey + ", 错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
