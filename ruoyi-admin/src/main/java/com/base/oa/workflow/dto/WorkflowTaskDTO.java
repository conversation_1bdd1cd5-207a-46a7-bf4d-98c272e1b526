package com.base.oa.workflow.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

import java.util.Date;

/**
 * 工作流任务数据传输对象
 * 用于替代 OaWorkflowTask 实体类，直接基于 Flowable 原生 Task API
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public class WorkflowTaskDTO {
    
    /** 任务ID (Flowable 任务ID) */
    private String taskId;
    
    /** 任务名称 */
    private String taskName;
    
    /** 任务定义Key */
    private String taskKey;
    
    /** 流程实例ID */
    private String processInstanceId;
    
    /** 业务主键 */
    private String businessKey;
    
    /** 处理人 */
    private String assignee;

    /** 处理人姓名 */
    private String assigneeName;
    
    /** 任务状态 (active/completed) */
    private String status;
    
    /** 优先级 */
    private Integer priority;
    
    /** 到期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;
    
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;
    
    /** 流程名称 */
    private String workflowName;
    
    /** 流程定义Key */
    private String workflowKey;
    
    /** 审批意见 */
    private String comment;
    
    // 构造函数
    public WorkflowTaskDTO() {}
    
    /**
     * 从 Flowable Task 创建 DTO
     */
    public static WorkflowTaskDTO fromTask(Task task) {
        WorkflowTaskDTO dto = new WorkflowTaskDTO();
        dto.setTaskId(task.getId());
        dto.setTaskName(task.getName());
        dto.setTaskKey(task.getTaskDefinitionKey());
        dto.setProcessInstanceId(task.getProcessInstanceId());
        dto.setAssignee(task.getAssignee());
        dto.setStatus("active");
        dto.setPriority(task.getPriority());
        dto.setDueDate(task.getDueDate());
        dto.setCreateTime(task.getCreateTime());
        return dto;
    }
    
    /**
     * 从 Flowable HistoricTaskInstance 创建 DTO
     */
    public static WorkflowTaskDTO fromHistoricTask(HistoricTaskInstance historicTask) {
        WorkflowTaskDTO dto = new WorkflowTaskDTO();
        dto.setTaskId(historicTask.getId());
        dto.setTaskName(historicTask.getName());
        dto.setTaskKey(historicTask.getTaskDefinitionKey());
        dto.setProcessInstanceId(historicTask.getProcessInstanceId());
        dto.setAssignee(historicTask.getAssignee());
        dto.setStatus("completed");
        dto.setPriority(historicTask.getPriority());
        dto.setDueDate(historicTask.getDueDate());
        dto.setCreateTime(historicTask.getStartTime());
        dto.setCompleteTime(historicTask.getEndTime());
        return dto;
    }
    
    // Getter 和 Setter 方法
    public String getTaskId() {
        return taskId;
    }
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    public String getTaskName() {
        return taskName;
    }
    
    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    
    public String getTaskKey() {
        return taskKey;
    }
    
    public void setTaskKey(String taskKey) {
        this.taskKey = taskKey;
    }
    
    public String getProcessInstanceId() {
        return processInstanceId;
    }
    
    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }
    
    public String getBusinessKey() {
        return businessKey;
    }
    
    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }
    
    public String getAssignee() {
        return assignee;
    }
    
    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }
    
    public String getAssigneeName() {
        return assigneeName;
    }

    public void setAssigneeName(String assigneeName) {
        this.assigneeName = assigneeName;
    }

    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Date getDueDate() {
        return dueDate;
    }
    
    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Date getCompleteTime() {
        return completeTime;
    }
    
    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }
    
    public String getWorkflowName() {
        return workflowName;
    }
    
    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }
    
    public String getWorkflowKey() {
        return workflowKey;
    }
    
    public void setWorkflowKey(String workflowKey) {
        this.workflowKey = workflowKey;
    }
    
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
    
    @Override
    public String toString() {
        return "WorkflowTaskDTO{" +
                "taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", taskKey='" + taskKey + '\'' +
                ", processInstanceId='" + processInstanceId + '\'' +
                ", businessKey='" + businessKey + '\'' +
                ", assignee='" + assignee + '\'' +
                ", assigneeName='" + assigneeName + '\'' +
                ", status='" + status + '\'' +
                ", priority=" + priority +
                ", dueDate=" + dueDate +
                ", createTime=" + createTime +
                ", completeTime=" + completeTime +
                ", workflowName='" + workflowName + '\'' +
                ", workflowKey='" + workflowKey + '\'' +
                ", comment='" + comment + '\'' +
                '}';
    }
}
