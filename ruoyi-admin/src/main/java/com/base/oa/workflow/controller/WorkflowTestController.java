package com.base.oa.workflow.controller;

import com.base.common.annotation.Anonymous;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.oa.workflow.service.WorkflowStartService;
import com.base.oa.workflow.service.IOaWorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 工作流测试控制器
 * 用于测试新的工作流功能
 *
 * <AUTHOR>
 * @date 2025-07-25
 */
@Anonymous
@RestController
@RequestMapping("/oa/workflow/test")
public class WorkflowTestController extends BaseController {
    
    @Autowired
    private WorkflowStartService workflowStartService;

    @Autowired
    private IOaWorkflowService oaWorkflowService;
    
    /**
     * 测试启动收文审批流程
     */
    @PostMapping("/receive/approval")
    public AjaxResult testReceiveApprovalProcess() {
        try {
            Long docId = 999L; // 测试文档ID
            
            Map<String, Object> variables = new HashMap<>();
            variables.put("selectedLeaders", Arrays.asList("zhangsan", "liuer", "wutao"));
            variables.put("urgencyLevel", "urgent");
            variables.put("receiveOpinion", "测试收文意见");
            
            String processInstanceId = workflowStartService.startDocumentReceiveApprovalProcess(docId, variables);
            
            return AjaxResult.success("测试收文审批流程启动成功")
                .put("processInstanceId", processInstanceId)
                .put("docId", docId);
        } catch (Exception e) {
            return error("测试收文审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试启动收文特办流程
     */
    @PostMapping("/receive/special")
    public AjaxResult testReceiveSpecialProcess() {
        try {
            Long docId = 998L; // 测试文档ID
            
            Map<String, Object> variables = new HashMap<>();
            variables.put("selectedAssignee", "liulei");
            variables.put("processingRequirement", "紧急处理此特办事项");
            variables.put("urgencyLevel", "very_urgent");
            variables.put("secretaryOpinion", "书记特办意见");
            
            String processInstanceId = workflowStartService.startDocumentReceiveSpecialProcess(docId, variables);
            
            return AjaxResult.success("测试收文特办流程启动成功")
                .put("processInstanceId", processInstanceId)
                .put("docId", docId);
        } catch (Exception e) {
            return error("测试收文特办流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试启动发文审批流程
     */
    @PostMapping("/send/approval")
    public AjaxResult testSendApprovalProcess() {
        try {
            Long docId = 997L; // 测试文档ID
            
            Map<String, Object> variables = new HashMap<>();
            variables.put("docTitle", "测试发文标题");
            variables.put("docType", "notice");
            variables.put("urgencyLevel", "normal");
            variables.put("docContent", "这是一个测试发文内容");
            
            String processInstanceId = workflowStartService.startDocumentSendApprovalProcess(docId, variables);
            
            return AjaxResult.success("测试发文审批流程启动成功")
                .put("processInstanceId", processInstanceId)
                .put("docId", docId);
        } catch (Exception e) {
            return error("测试发文审批流程失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试流程状态检查
     */
    @GetMapping("/check/{processKey}")
    public AjaxResult checkProcessStatus(@PathVariable String processKey) {
        try {
            boolean canStart = workflowStartService.canStartProcess(processKey);
            
            return AjaxResult.success()
                .put("processKey", processKey)
                .put("canStart", canStart)
                .put("message", canStart ? "流程可以启动" : "流程不可用");
        } catch (Exception e) {
            return error("检查流程状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量测试所有流程
     */
    @PostMapping("/all")
    public AjaxResult testAllProcesses() {
        Map<String, Object> results = new HashMap<>();
        
        try {
            // 测试收文审批流程
            try {
                Long docId1 = 996L;
                Map<String, Object> variables1 = new HashMap<>();
                variables1.put("selectedLeaders", Arrays.asList("zhangsan", "liuer"));
                variables1.put("urgencyLevel", "normal");
                
                String processInstanceId1 = workflowStartService.startDocumentReceiveApprovalProcess(docId1, variables1);
                Map<String, Object> result1 = new HashMap<>();
                result1.put("success", true);
                result1.put("processInstanceId", processInstanceId1);
                result1.put("docId", docId1);
                results.put("receiveApproval", result1);
            } catch (Exception e) {
                Map<String, Object> error1 = new HashMap<>();
                error1.put("success", false);
                error1.put("error", e.getMessage());
                results.put("receiveApproval", error1);
            }
            
            // 测试收文特办流程
            try {
                Long docId2 = 995L;
                Map<String, Object> variables2 = new HashMap<>();
                variables2.put("selectedAssignee", "zhangfeifei");
                variables2.put("processingRequirement", "特办处理");
                
                String processInstanceId2 = workflowStartService.startDocumentReceiveSpecialProcess(docId2, variables2);
                Map<String, Object> result2 = new HashMap<>();
                result2.put("success", true);
                result2.put("processInstanceId", processInstanceId2);
                result2.put("docId", docId2);
                results.put("receiveSpecial", result2);
            } catch (Exception e) {
                Map<String, Object> error2 = new HashMap<>();
                error2.put("success", false);
                error2.put("error", e.getMessage());
                results.put("receiveSpecial", error2);
            }
            
            // 测试发文审批流程
            try {
                Long docId3 = 994L;
                Map<String, Object> variables3 = new HashMap<>();
                variables3.put("docTitle", "批量测试发文");
                variables3.put("docType", "report");
                
                String processInstanceId3 = workflowStartService.startDocumentSendApprovalProcess(docId3, variables3);
                Map<String, Object> result3 = new HashMap<>();
                result3.put("success", true);
                result3.put("processInstanceId", processInstanceId3);
                result3.put("docId", docId3);
                results.put("sendApproval", result3);
            } catch (Exception e) {
                Map<String, Object> error3 = new HashMap<>();
                error3.put("success", false);
                error3.put("error", e.getMessage());
                results.put("sendApproval", error3);
            }
            
            return AjaxResult.success("批量测试完成").put("results", results);
            
        } catch (Exception e) {
            return error("批量测试失败: " + e.getMessage()).put("results", results);
        }
    }

    /**
     * 测试获取分管领导列表
     */
    @GetMapping("/leaders")
    public AjaxResult testGetLeaders() {
        try {
            List<Map<String, Object>> leaders = oaWorkflowService.getAvailableLeaders();

            return AjaxResult.success("获取分管领导列表成功")
                .put("leaders", leaders)
                .put("count", leaders.size());
        } catch (Exception e) {
            return error("获取分管领导列表失败: " + e.getMessage());
        }
    }

    /**
     * 测试获取科室负责人列表
     */
    @GetMapping("/managers/{leaderId}")
    public AjaxResult testGetManagers(@PathVariable String leaderId) {
        try {
            List<Map<String, Object>> managers = oaWorkflowService.getAvailableManagers(leaderId);

            return AjaxResult.success("获取科室负责人列表成功")
                .put("leaderId", leaderId)
                .put("managers", managers)
                .put("count", managers.size());
        } catch (Exception e) {
            return error("获取科室负责人列表失败: " + e.getMessage());
        }
    }

    /**
     * 测试分管领导指派功能完整性
     */
    @GetMapping("/leader-assignment-test")
    public AjaxResult testLeaderAssignment() {
        Map<String, Object> results = new HashMap<>();

        try {
            // 1. 测试获取分管领导列表
            List<Map<String, Object>> leaders = oaWorkflowService.getAvailableLeaders();
            results.put("leaders", leaders);
            results.put("leadersCount", leaders.size());

            // 验证分管领导是否只包含fgld角色的用户
            String[] expectedLeaders = {"leader_admin", "leader_finance", "leader_operation"};
            boolean leadersValid = true;
            for (Map<String, Object> leader : leaders) {
                String userName = (String) leader.get("userName");
                boolean found = false;
                for (String expected : expectedLeaders) {
                    if (expected.equals(userName)) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    leadersValid = false;
                    break;
                }
            }
            results.put("leadersValid", leadersValid);

            // 2. 测试每个分管领导的科室负责人
            Map<String, Object> managersResults = new HashMap<>();
            for (Map<String, Object> leader : leaders) {
                String leaderId = (String) leader.get("userName");
                try {
                    List<Map<String, Object>> managers = oaWorkflowService.getAvailableManagers(leaderId);
                    Map<String, Object> managerInfo = new HashMap<>();
                    managerInfo.put("managers", managers);
                    managerInfo.put("count", managers.size());
                    managersResults.put(leaderId, managerInfo);
                } catch (Exception e) {
                    Map<String, Object> errorInfo = new HashMap<>();
                    errorInfo.put("error", e.getMessage());
                    managersResults.put(leaderId, errorInfo);
                }
            }
            results.put("managersResults", managersResults);

            return AjaxResult.success("分管领导指派功能测试完成").put("results", results);

        } catch (Exception e) {
            return error("分管领导指派功能测试失败: " + e.getMessage()).put("results", results);
        }
    }
}
