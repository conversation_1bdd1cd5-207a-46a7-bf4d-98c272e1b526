package com.base.oa.workflow.listener;

import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;

/**
 * 办公室汇总任务监听器
 * 将汇总任务分配给流程创建人员（办公室人员）
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Component("officeSummaryTaskListener")
public class OfficeSummaryTaskListener implements TaskListener {

    @Override
    public void notify(DelegateTask delegateTask) {
        try {
            String taskId = delegateTask.getId();
            String processInstanceId = delegateTask.getProcessInstanceId();
            
            System.out.println("办公室汇总任务创建: " + taskId + ", 流程实例: " + processInstanceId);
            
            // 获取流程创建人
            String startUserId = delegateTask.getVariable("startUserId", String.class);
            
            if (startUserId != null && !startUserId.isEmpty()) {
                // 将汇总任务分配给流程创建人
                delegateTask.setAssignee(startUserId);
                System.out.println("办公室汇总任务分配给流程创建人: " + startUserId);
            } else {
                // 如果没有找到创建人，分配给办公室人员
                System.out.println("未找到流程创建人，分配给办公室人员");
                // 任务已经通过candidateGroups="clerk"分配给办公室人员
            }
            
        } catch (Exception e) {
            System.err.println("办公室汇总任务监听器执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
