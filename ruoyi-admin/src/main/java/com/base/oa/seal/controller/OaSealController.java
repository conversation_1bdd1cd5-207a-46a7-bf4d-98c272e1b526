package com.base.oa.seal.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.oa.seal.domain.OaSeal;
import com.base.oa.seal.domain.OaSealApplication;
import com.base.oa.seal.domain.OaSealCertificate;
import com.base.oa.seal.service.IOaSealService;
import com.base.oa.common.service.FileService;

import java.util.Map;

/**
 * 印章管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/oa/seal")
public class OaSealController extends BaseController
{
    @Autowired
    private IOaSealService oaSealService;

    @Autowired
    private FileService fileService;

    // ==================== 印章管理 ====================

    /**
     * 查询印章列表
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaSeal oaSeal)
    {
        startPage();
        List<OaSeal> list = oaSealService.selectOaSealList(oaSeal);
        return getDataTable(list);
    }

    /**
     * 导出印章列表
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:export')")
    @Log(title = "印章管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaSeal oaSeal)
    {
        List<OaSeal> list = oaSealService.selectOaSealList(oaSeal);
        ExcelUtil<OaSeal> util = new ExcelUtil<OaSeal>(OaSeal.class);
        util.exportExcel(response, list, "印章数据");
    }

    // ==================== 编辑器印章功能 ====================

    /**
     * 获取可用印章列表（用于编辑器）
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:query')")
    @GetMapping("/available")
    public AjaxResult getAvailableSeals()
    {
        Long userId = getUserId();
        List<OaSeal> availableSeals = oaSealService.getAvailableSeals(userId);
        return success(availableSeals);
    }

    /**
     * 获取印章图片（Base64格式）
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:query')")
    @GetMapping("/image/{sealId}")
    public AjaxResult getSealImage(@PathVariable("sealId") Long sealId)
    {
        OaSeal seal = oaSealService.selectOaSealBySealId(sealId);
        if (seal == null) {
            return error("印章不存在");
        }

        // 直接返回数据库中存储的Base64图片数据
        String sealImageBase64 = seal.getSealImage();
        if (sealImageBase64 == null || sealImageBase64.trim().isEmpty()) {
            return error("印章图片数据不存在");
        }

        // 确保Base64数据格式正确
        if (!sealImageBase64.startsWith("data:image/")) {
            sealImageBase64 = "data:image/png;base64," + sealImageBase64;
        }

        // 使用success方法确保数据在data字段中返回
        return AjaxResult.success().put("data", sealImageBase64);
    }

    /**
     * 获取印章详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:query')")
    @GetMapping(value = "/{sealId}")
    public AjaxResult getInfo(@PathVariable("sealId") Long sealId)
    {
        return success(oaSealService.selectOaSealBySealId(sealId));
    }

    /**
     * 新增印章
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:add')")
    @Log(title = "印章管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaSeal oaSeal)
    {
        oaSeal.setCreateBy(getUserId().toString());
        return toAjax(oaSealService.insertOaSeal(oaSeal));
    }

    /**
     * 修改印章
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:edit')")
    @Log(title = "印章管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaSeal oaSeal)
    {
        return toAjax(oaSealService.updateOaSeal(oaSeal));
    }

    /**
     * 删除印章
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:remove')")
    @Log(title = "印章管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sealIds}")
    public AjaxResult remove(@PathVariable Long[] sealIds)
    {
        return toAjax(oaSealService.deleteOaSealBySealIds(sealIds));
    }

    /**
     * 启用印章
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:enable')")
    @Log(title = "启用印章", businessType = BusinessType.UPDATE)
    @PostMapping("/enable/{sealId}")
    public AjaxResult enable(@PathVariable Long sealId)
    {
        return toAjax(oaSealService.enableSeal(sealId));
    }

    /**
     * 停用印章
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:disable')")
    @Log(title = "停用印章", businessType = BusinessType.UPDATE)
    @PostMapping("/disable/{sealId}")
    public AjaxResult disable(@PathVariable Long sealId, @RequestParam String reason)
    {
        return toAjax(oaSealService.disableSeal(sealId, reason));
    }

    /**
     * 修改印章状态
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:edit')")
    @Log(title = "修改印章状态", businessType = BusinessType.UPDATE)
    @PostMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody Map<String, Object> params)
    {
        try {
            Long sealId = Long.valueOf(params.get("sealId").toString());
            String status = params.get("status").toString();

            if ("0".equals(status)) {
                // 停用
                String reason = (String) params.get("reason");
                return toAjax(oaSealService.disableSeal(sealId, reason != null ? reason : ""));
            } else if ("1".equals(status)) {
                // 启用
                return toAjax(oaSealService.enableSeal(sealId));
            } else if ("2".equals(status)) {
                // 损坏
                String reason = (String) params.get("reason");
                return toAjax(oaSealService.disableSeal(sealId, "印章损坏：" + (reason != null ? reason : "")));
            } else if ("3".equals(status)) {
                // 遗失
                String reason = (String) params.get("reason");
                return toAjax(oaSealService.disableSeal(sealId, "印章遗失：" + (reason != null ? reason : "")));
            }

            return error("无效的状态值");
        } catch (Exception e) {
            logger.error("修改印章状态失败", e);
            return error("修改印章状态失败: " + e.getMessage());
        }
    }

    /**
     * 上传印章图片（直接转换为Base64存储）
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:upload')")
    @Log(title = "上传印章图片", businessType = BusinessType.UPDATE)
    @PostMapping("/upload")
    public AjaxResult uploadSealImage(@RequestParam("file") MultipartFile file)
    {
        try {
            // 验证文件类型
            if (!isImageFile(file)) {
                return error("只能上传图片文件（jpg、jpeg、png、gif）");
            }

            // 验证文件大小（限制为2MB）
            if (file.getSize() > 2 * 1024 * 1024) {
                return error("图片文件大小不能超过2MB");
            }

            // 将图片转换为Base64
            byte[] imageBytes = file.getBytes();
            String base64Image = java.util.Base64.getEncoder().encodeToString(imageBytes);

            // 添加数据URL前缀
            String contentType = file.getContentType();
            String base64Data = "data:" + contentType + ";base64," + base64Image;

            return AjaxResult.success("上传成功").put("base64Data", base64Data);
        } catch (IOException e) {
            return error("上传失败：" + e.getMessage());
        }
    }

    /**
     * 验证是否为图片文件
     */
    private boolean isImageFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && (
            contentType.equals("image/jpeg") ||
            contentType.equals("image/jpg") ||
            contentType.equals("image/png") ||
            contentType.equals("image/gif")
        );
    }

    // ==================== 印章申请 ====================

    /**
     * 查询印章申请列表
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:list')")
    @GetMapping("/application/list")
    public TableDataInfo applicationList(OaSealApplication oaSealApplication)
    {
        startPage();
        List<OaSealApplication> list = oaSealService.selectOaSealApplicationList(oaSealApplication);
        return getDataTable(list);
    }

    /**
     * 查询我的印章申请列表
     */
    @GetMapping("/application/my-list")
    public TableDataInfo myApplicationList(OaSealApplication oaSealApplication)
    {
        startPage();
        Long userId = getUserId();
        List<OaSealApplication> list = oaSealService.selectMySealApplicationList(userId, oaSealApplication);
        return getDataTable(list);
    }

    /**
     * 查询待审批申请列表
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:approve')")
    @GetMapping("/application/pending")
    public TableDataInfo pendingApplicationList(OaSealApplication oaSealApplication)
    {
        startPage();
        List<OaSealApplication> list = oaSealService.selectPendingApplicationList(oaSealApplication);
        return getDataTable(list);
    }

    /**
     * 获取印章申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:query')")
    @GetMapping("/application/{applicationId}")
    public AjaxResult getApplicationInfo(@PathVariable("applicationId") Long applicationId)
    {
        return success(oaSealService.selectOaSealApplicationByApplicationId(applicationId));
    }

    /**
     * 新增印章申请
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:add')")
    @Log(title = "印章申请", businessType = BusinessType.INSERT)
    @PostMapping("/application")
    public AjaxResult addApplication(@RequestBody OaSealApplication oaSealApplication)
    {
        oaSealApplication.setApplicantId(getUserId());
        oaSealApplication.setApplicantName(getUsername());
        oaSealApplication.setCreateBy(getUserId().toString());
        return toAjax(oaSealService.insertOaSealApplication(oaSealApplication));
    }

    /**
     * 修改印章申请
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:edit')")
    @Log(title = "印章申请", businessType = BusinessType.UPDATE)
    @PutMapping("/application")
    public AjaxResult editApplication(@RequestBody OaSealApplication oaSealApplication)
    {
        return toAjax(oaSealService.updateOaSealApplication(oaSealApplication));
    }

    /**
     * 删除印章申请
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:remove')")
    @Log(title = "印章申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/application/{applicationIds}")
    public AjaxResult removeApplication(@PathVariable Long[] applicationIds)
    {
        return toAjax(oaSealService.deleteOaSealApplicationByApplicationIds(applicationIds));
    }

    /**
     * 提交印章申请
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:submit')")
    @Log(title = "提交印章申请", businessType = BusinessType.UPDATE)
    @PostMapping("/application/submit/{applicationId}")
    public AjaxResult submitApplication(@PathVariable Long applicationId)
    {
        return toAjax(oaSealService.submitSealApplication(applicationId));
    }

    /**
     * 审批印章申请
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:application:approve')")
    @Log(title = "审批印章申请", businessType = BusinessType.UPDATE)
    @PostMapping("/application/approve/{applicationId}")
    public AjaxResult approveApplication(@PathVariable Long applicationId,
                                       @RequestParam String approvalResult,
                                       @RequestParam String approvalComment)
    {
        Long approverId = getUserId();
        return toAjax(oaSealService.approveSealApplication(applicationId, approvalResult, approvalComment, approverId));
    }

    // ==================== 印章证书 ====================

    /**
     * 查询印章证书列表
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:certificate:list')")
    @GetMapping("/certificate/list")
    public TableDataInfo certificateList(OaSealCertificate oaSealCertificate)
    {
        startPage();
        List<OaSealCertificate> list = oaSealService.selectOaSealCertificateList(oaSealCertificate);
        return getDataTable(list);
    }

    /**
     * 获取印章证书详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:certificate:query')")
    @GetMapping("/certificate/{certificateId}")
    public AjaxResult getCertificateInfo(@PathVariable("certificateId") Long certificateId)
    {
        return success(oaSealService.selectOaSealCertificateByCertificateId(certificateId));
    }

    /**
     * 生成印章证书
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:certificate:generate')")
    @Log(title = "生成印章证书", businessType = BusinessType.INSERT)
    @PostMapping("/certificate/generate")
    public AjaxResult generateCertificate(@RequestParam Long sealId, @RequestParam Long applicationId)
    {
        return toAjax(oaSealService.generateSealCertificate(sealId, applicationId));
    }

    /**
     * 吊销印章证书
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:certificate:revoke')")
    @Log(title = "吊销印章证书", businessType = BusinessType.UPDATE)
    @PostMapping("/certificate/revoke/{certificateId}")
    public AjaxResult revokeCertificate(@PathVariable Long certificateId, @RequestParam String revokeReason)
    {
        return toAjax(oaSealService.revokeSealCertificate(certificateId, revokeReason));
    }

    /**
     * 验证印章证书
     */
    @GetMapping("/certificate/verify")
    public AjaxResult verifyCertificate(@RequestParam String certificateNumber)
    {
        Object result = oaSealService.verifySealCertificate(certificateNumber);
        return success(result);
    }

    /**
     * 印章使用统计
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:statistics')")
    @GetMapping("/statistics")
    public AjaxResult getSealStatistics(@RequestParam Long sealId,
                                       @RequestParam(required = false) String startDate,
                                       @RequestParam(required = false) String endDate)
    {
        Object statistics = oaSealService.getSealUsageStatistics(sealId, startDate, endDate);
        return success(statistics);
    }

    /**
     * 记录印章使用
     */
    @PreAuthorize("@ss.hasPermi('oa:seal:use')")
    @Log(title = "记录印章使用", businessType = BusinessType.INSERT)
    @PostMapping("/record-usage")
    public AjaxResult recordUsage(@RequestParam Long sealId,
                                 @RequestParam Long applicationId,
                                 @RequestParam String useReason)
    {
        Long userId = getUserId();
        return toAjax(oaSealService.recordSealUsage(sealId, userId, applicationId, useReason));
    }
}