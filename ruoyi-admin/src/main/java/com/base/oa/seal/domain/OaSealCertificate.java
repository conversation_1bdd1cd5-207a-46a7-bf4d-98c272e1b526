package com.base.oa.seal.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;

/**
 * 印章证书对象 oa_seal_certificate
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class OaSealCertificate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 证书ID */
    private Long certificateId;

    /** 印章ID */
    @Excel(name = "印章ID")
    private Long sealId;

    /** 证书名称 */
    @Excel(name = "证书名称")
    private String certificateName;

    /** 证书类型 */
    @Excel(name = "证书类型")
    private String certificateType;

    /** 证书文件路径 */
    private String certificateFile;

    /** 颁发机构 */
    @Excel(name = "颁发机构")
    private String issuer;

    /** 颁发日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "颁发日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issueDate;

    /** 有效期开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期开始", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validFrom;

    /** 有效期结束 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "有效期结束", width = 30, dateFormat = "yyyy-MM-dd")
    private Date validTo;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    /** 描述 */
    private String description;

    public void setCertificateId(Long certificateId) 
    {
        this.certificateId = certificateId;
    }

    public Long getCertificateId() 
    {
        return certificateId;
    }
    public void setSealId(Long sealId) 
    {
        this.sealId = sealId;
    }

    public Long getSealId() 
    {
        return sealId;
    }
    public void setCertificateName(String certificateName) 
    {
        this.certificateName = certificateName;
    }

    public String getCertificateName() 
    {
        return certificateName;
    }
    public void setCertificateType(String certificateType) 
    {
        this.certificateType = certificateType;
    }

    public String getCertificateType() 
    {
        return certificateType;
    }
    public void setCertificateFile(String certificateFile) 
    {
        this.certificateFile = certificateFile;
    }

    public String getCertificateFile() 
    {
        return certificateFile;
    }
    public void setIssuer(String issuer) 
    {
        this.issuer = issuer;
    }

    public String getIssuer() 
    {
        return issuer;
    }
    public void setIssueDate(Date issueDate) 
    {
        this.issueDate = issueDate;
    }

    public Date getIssueDate() 
    {
        return issueDate;
    }
    public void setValidFrom(Date validFrom) 
    {
        this.validFrom = validFrom;
    }

    public Date getValidFrom() 
    {
        return validFrom;
    }
    public void setValidTo(Date validTo) 
    {
        this.validTo = validTo;
    }

    public Date getValidTo() 
    {
        return validTo;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("certificateId", getCertificateId())
            .append("sealId", getSealId())
            .append("certificateName", getCertificateName())
            .append("certificateType", getCertificateType())
            .append("certificateFile", getCertificateFile())
            .append("issuer", getIssuer())
            .append("issueDate", getIssueDate())
            .append("validFrom", getValidFrom())
            .append("validTo", getValidTo())
            .append("status", getStatus())
            .append("description", getDescription())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
