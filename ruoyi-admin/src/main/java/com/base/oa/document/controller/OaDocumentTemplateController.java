package com.base.oa.document.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.oa.document.domain.OaDocumentTemplate;
import com.base.oa.document.service.IOaDocumentTemplateService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;

/**
 * 公文模板（红头）Controller
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/oa/document/template")
public class OaDocumentTemplateController extends BaseController
{
    @Autowired
    private IOaDocumentTemplateService oaDocumentTemplateService;

    /**
     * 查询公文模板（红头）列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaDocumentTemplate oaDocumentTemplate)
    {
        startPage();
        List<OaDocumentTemplate> list = oaDocumentTemplateService.selectOaDocumentTemplateList(oaDocumentTemplate);
        return getDataTable(list);
    }

    /**
     * 导出公文模板（红头）列表
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:export')")
    @Log(title = "公文模板（红头）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaDocumentTemplate oaDocumentTemplate)
    {
        List<OaDocumentTemplate> list = oaDocumentTemplateService.selectOaDocumentTemplateList(oaDocumentTemplate);
        ExcelUtil<OaDocumentTemplate> util = new ExcelUtil<OaDocumentTemplate>(OaDocumentTemplate.class);
        util.exportExcel(response, list, "公文模板（红头）数据");
    }

    /**
     * 获取公文模板（红头）详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return success(oaDocumentTemplateService.selectOaDocumentTemplateByTemplateId(templateId));
    }

    /**
     * 新增公文模板（红头）
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:add')")
    @Log(title = "公文模板（红头）", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaDocumentTemplate oaDocumentTemplate)
    {
        return toAjax(oaDocumentTemplateService.insertOaDocumentTemplate(oaDocumentTemplate));
    }

    /**
     * 修改公文模板（红头）
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:edit')")
    @Log(title = "公文模板（红头）", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaDocumentTemplate oaDocumentTemplate)
    {
        return toAjax(oaDocumentTemplateService.updateOaDocumentTemplate(oaDocumentTemplate));
    }

    /**
     * 删除公文模板（红头）
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:remove')")
    @Log(title = "公文模板（红头）", businessType = BusinessType.DELETE)
	@DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(oaDocumentTemplateService.deleteOaDocumentTemplateByTemplateIds(templateIds));
    }

    /**
     * 获取可用的红头模板列表
     */
    @GetMapping("/available")
    public AjaxResult getAvailableTemplates(String templateType)
    {
        List<OaDocumentTemplate> list = oaDocumentTemplateService.getAvailableTemplates(templateType);
        return success(list);
    }

    /**
     * 获取默认红头模板
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:query')")
    @GetMapping("/default")
    public AjaxResult getDefaultTemplate(String templateType, String issuingOrgan)
    {
        OaDocumentTemplate template = oaDocumentTemplateService.getDefaultTemplate(templateType, issuingOrgan);
        return success(template);
    }

    /**
     * 根据机关和文种获取适用模板
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:query')")
    @GetMapping("/by-organ-genre")
    public AjaxResult getTemplatesByOrganAndGenre(String issuingOrgan, String docGenre)
    {
        List<OaDocumentTemplate> list = oaDocumentTemplateService.getTemplatesByOrganAndGenre(issuingOrgan, docGenre);
        return success(list);
    }

    /**
     * 设置默认模板
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:edit')")
    @Log(title = "设置默认模板", businessType = BusinessType.UPDATE)
    @PutMapping("/default/{templateId}")
    public AjaxResult setDefaultTemplate(@PathVariable Long templateId, String templateType, String issuingOrgan)
    {
        return toAjax(oaDocumentTemplateService.setDefaultTemplate(templateId, templateType, issuingOrgan));
    }

    /**
     * 生成公文HTML内容预览
     */
    @PreAuthorize("@ss.hasPermi('oa:document:template:query')")
    @PostMapping("/generate-html")
    public AjaxResult generateDocumentHtml(@RequestBody OaDocumentTemplate template)
    {
        String html = oaDocumentTemplateService.generateDocumentHtml(
            template.getTemplateId(),
            "示例文档标题",
            "示例发文字号",
            "这是示例正文内容",
            "示例签发人",
            template.getIssuingOrgan()
        );
        return success(html);
    }
}