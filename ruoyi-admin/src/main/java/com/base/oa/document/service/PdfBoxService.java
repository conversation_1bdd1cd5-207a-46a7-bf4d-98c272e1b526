package com.base.oa.document.service;


import com.base.common.exception.ServiceException;
import com.base.common.utils.DateUtils;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.domain.OaFileAttachment;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.ArrayList;

/**
 * PDFBox PDF生成服务
 * 使用Apache PDFBox生成真正的PDF文件
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class PdfBoxService {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfBoxService.class);
    
    // 页面设置
    private static final float MARGIN = 50;
    private static final float FONT_SIZE = 12;
    private static final float TITLE_FONT_SIZE = 18;
    private static final float HEADER_FONT_SIZE = 24;
    private static final float LINE_HEIGHT = 20;
    
    /**
     * 生成发文PDF
     * 
     * @param sendDoc 发文文档
     * @return PDF字节数组
     */
    public byte[] generateSendDocumentPdf(OaDocumentSend sendDoc) {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);
            
            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                float yPosition = page.getMediaBox().getHeight() - MARGIN;
                
                // 设置字体 - 使用支持中文的字体
                PDFont font = getChineseFont(document);
                PDFont boldFont = getChineseBoldFont(document);
                
                // 红头标题
                yPosition = addCenteredText(contentStream, "红河哈尼族彝族自治州住房和城乡建设局", 
                    boldFont, HEADER_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
                yPosition -= 30;
                
                // 发文字号
                if (StringUtils.isNotEmpty(sendDoc.getDocNumber())) {
                    yPosition = addRightAlignedText(contentStream, sendDoc.getDocNumber(), 
                        font, FONT_SIZE, yPosition, page.getMediaBox().getWidth() - MARGIN);
                    yPosition -= 20;
                }
                
                // 文档标题
                if (StringUtils.isNotEmpty(sendDoc.getDocTitle())) {
                    yPosition = addCenteredText(contentStream, sendDoc.getDocTitle(), 
                        boldFont, TITLE_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
                    yPosition -= 30;
                }
                
                // 收文单位
                if (StringUtils.isNotEmpty(sendDoc.getReceiverUnit())) {
                    yPosition = addLeftAlignedText(contentStream, sendDoc.getReceiverUnit() + ":", 
                        font, FONT_SIZE, yPosition, MARGIN);
                    yPosition -= 30;
                }
                
                // 文档内容
                if (StringUtils.isNotEmpty(sendDoc.getDocContent())) {
                    String content = cleanContent(sendDoc.getDocContent());
                    yPosition = addWrappedText(contentStream, content, font, FONT_SIZE, 
                        yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
                    yPosition -= 30;
                }
                
                // 签发信息
                if (StringUtils.isNotEmpty(sendDoc.getSenderName())) {
                    yPosition = addRightAlignedText(contentStream, sendDoc.getSenderName(), 
                        font, FONT_SIZE, yPosition, page.getMediaBox().getWidth() - MARGIN);
                    yPosition -= 15;
                }
                
                if (sendDoc.getSendTime() != null) {
                    String dateStr = DateUtils.parseDateToStr("yyyy年MM月dd日", sendDoc.getSendTime());
                    addRightAlignedText(contentStream, dateStr, font, FONT_SIZE, 
                        yPosition, page.getMediaBox().getWidth() - MARGIN);
                }
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            logger.error("生成发文PDF失败", e);
            throw new ServiceException("生成发文PDF失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成合并PDF
     * 
     * @param sendDoc 发文文档
     * @param attachments 附件列表
     * @return PDF字节数组
     */
    public byte[] generateMergedDocumentPdf(OaDocumentSend sendDoc, List<OaFileAttachment> attachments) {
        try (PDDocument document = new PDDocument()) {
            
            // 1. 审批单页面
            addApprovalPage(document, sendDoc);
            
            // 2. 红头文件页面
            addRedHeaderPage(document);
            
            // 3. 底稿页面
            addDraftPage(document, sendDoc);
            
            // 4. 附件页面
            if (attachments != null && !attachments.isEmpty()) {
                addAttachmentPage(document, attachments);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            logger.error("生成合并PDF失败", e);
            throw new ServiceException("生成合并PDF失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加审批单页面
     */
    private void addApprovalPage(PDDocument document, OaDocumentSend sendDoc) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 标题
            yPosition = addCenteredText(contentStream, "发文审批单", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 审批信息表格
            yPosition = addTableRow(contentStream, "文件标题:", sendDoc.getDocTitle(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "发文字号:", sendDoc.getDocNumber(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "收文单位:", sendDoc.getReceiverUnit(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "创建时间:", 
                sendDoc.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", sendDoc.getCreateTime()) : "", 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "创建人:", sendDoc.getCreateBy(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            
            // 审批流程
            yPosition -= 30;
            yPosition = addLeftAlignedText(contentStream, "审批流程:", boldFont, FONT_SIZE, yPosition, MARGIN);
            yPosition -= 20;
            yPosition = addLeftAlignedText(contentStream, "1. 发起申请 - " + (sendDoc.getCreateBy() != null ? sendDoc.getCreateBy() : "") + " - 提交", 
                font, FONT_SIZE, yPosition, MARGIN + 20);
            yPosition -= 15;
            yPosition = addLeftAlignedText(contentStream, "2. 审批完成 - 系统 - 同意", 
                font, FONT_SIZE, yPosition, MARGIN + 20);
        }
    }
    
    /**
     * 添加红头文件页面
     */
    private void addRedHeaderPage(PDDocument document) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont boldFont = getChineseBoldFont(document);
            
            yPosition = addCenteredText(contentStream, "红头文件", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 100;
            
            addCenteredText(contentStream, "（此处应为上传的红头文件内容）",
                getChineseFont(document), FONT_SIZE, yPosition, page.getMediaBox().getWidth());
        }
    }
    
    /**
     * 添加底稿页面
     */
    private void addDraftPage(PDDocument document, OaDocumentSend sendDoc) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 红头标题
            yPosition = addCenteredText(contentStream, "红河哈尼族彝族自治州住房和城乡建设局", 
                boldFont, HEADER_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 文档标题
            if (StringUtils.isNotEmpty(sendDoc.getDocTitle())) {
                yPosition = addCenteredText(contentStream, sendDoc.getDocTitle(), 
                    boldFont, TITLE_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
                yPosition -= 30;
            }
            
            // 文档内容
            if (StringUtils.isNotEmpty(sendDoc.getDocContent())) {
                String content = cleanContent(sendDoc.getDocContent());
                yPosition = addWrappedText(contentStream, content, font, FONT_SIZE, 
                    yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            }
        }
    }
    
    /**
     * 添加附件页面
     */
    private void addAttachmentPage(PDDocument document, List<OaFileAttachment> attachments) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            yPosition = addCenteredText(contentStream, "附件", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            for (int i = 0; i < attachments.size(); i++) {
                OaFileAttachment attachment = attachments.get(i);
                yPosition = addLeftAlignedText(contentStream, (i + 1) + ". " + attachment.getFileName(), 
                    font, FONT_SIZE, yPosition, MARGIN);
                yPosition -= 20;
            }
        }
    }
    
    // 辅助方法
    private float addCenteredText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float pageWidth) throws IOException {
        if (text == null) text = "";

        // 处理中文字符
        String safeText = processChinese(text);

        float textWidth = font.getStringWidth(safeText) / 1000 * fontSize;
        float xPosition = (pageWidth - textWidth) / 2;

        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(safeText);
        contentStream.endText();

        return yPosition - LINE_HEIGHT;
    }
    
    private float addLeftAlignedText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float xPosition) throws IOException {
        if (text == null) text = "";

        // 处理中文字符
        String safeText = processChinese(text);

        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(safeText);
        contentStream.endText();

        return yPosition - LINE_HEIGHT;
    }
    
    private float addRightAlignedText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float rightMargin) throws IOException {
        if (text == null) text = "";

        // 处理中文字符
        String safeText = processChinese(text);

        float textWidth = font.getStringWidth(safeText) / 1000 * fontSize;
        float xPosition = rightMargin - textWidth;

        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(safeText);
        contentStream.endText();

        return yPosition - LINE_HEIGHT;
    }
    
    private float addTableRow(PDPageContentStream contentStream, String label, String value,
            PDFont font, PDFont boldFont, float fontSize, float yPosition, float xPosition, float width) throws IOException {
        if (value == null) value = "";

        // 处理中文字符
        String safeLabel = processChinese(label);
        String safeValue = processChinese(value);

        // 标签
        contentStream.beginText();
        contentStream.setFont(boldFont, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(safeLabel);
        contentStream.endText();

        // 值
        float labelWidth = boldFont.getStringWidth(safeLabel) / 1000 * fontSize;
        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition + labelWidth + 10, yPosition);
        contentStream.showText(safeValue);
        contentStream.endText();

        return yPosition - LINE_HEIGHT;
    }
    
    private float addWrappedText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float xPosition, float width) throws IOException {
        if (text == null || text.trim().isEmpty()) return yPosition;

        // 处理中文字符
        String safeText = processChinese(text);

        // 对于中文文本，按字符分割而不是按单词
        String[] chars = safeText.split("");
        StringBuilder line = new StringBuilder();

        for (String ch : chars) {
            String testLine = line.toString() + ch;
            float textWidth = font.getStringWidth(testLine) / 1000 * fontSize;

            if (textWidth > width && line.length() > 0) {
                // 输出当前行
                contentStream.beginText();
                contentStream.setFont(font, fontSize);
                contentStream.newLineAtOffset(xPosition, yPosition);
                contentStream.showText(line.toString());
                contentStream.endText();

                yPosition -= LINE_HEIGHT;
                line = new StringBuilder(ch);
            } else {
                line.append(ch);
            }
        }

        // 输出最后一行
        if (line.length() > 0) {
            contentStream.beginText();
            contentStream.setFont(font, fontSize);
            contentStream.newLineAtOffset(xPosition, yPosition);
            contentStream.showText(line.toString());
            contentStream.endText();
            yPosition -= LINE_HEIGHT;
        }

        return yPosition;
    }
    
    private String cleanContent(String content) {
        if (content == null) return "";
        return content.replaceAll("<[^>]+>", "")
                     .replaceAll("&nbsp;", " ")
                     .replaceAll("&quot;", "\"")
                     .replaceAll("&amp;", "&")
                     .replaceAll("&lt;", "<")
                     .replaceAll("&gt;", ">")
                     .trim();
    }

    /**
     * 获取支持中文的字体
     */
    private PDFont getChineseFont(PDDocument document) throws IOException {
        try {
            // 尝试加载系统中文字体
            java.io.File fontFile = findSystemChineseFont();
            if (fontFile != null && fontFile.exists()) {
                logger.info("找到中文字体: " + fontFile.getAbsolutePath());
                return PDType0Font.load(document, fontFile);
            }
        } catch (Exception e) {
            logger.warn("无法加载系统中文字体: " + e.getMessage());
        }

        // 尝试加载内置中文字体资源
        try {
            return loadEmbeddedChineseFont(document);
        } catch (Exception e) {
            logger.warn("无法加载内置中文字体: " + e.getMessage());
        }

        // 最后备选方案：抛出异常而不是使用不支持中文的字体
        throw new IOException("无法找到支持中文的字体，请确保系统中安装了中文字体");
    }

    /**
     * 获取支持中文的粗体字体
     */
    private PDFont getChineseBoldFont(PDDocument document) throws IOException {
        try {
            // 尝试加载系统中文粗体字体
            java.io.File fontFile = findSystemChineseBoldFont();
            if (fontFile != null && fontFile.exists()) {
                logger.info("找到中文粗体字体: " + fontFile.getAbsolutePath());
                return PDType0Font.load(document, fontFile);
            }
        } catch (Exception e) {
            logger.warn("无法加载系统中文粗体字体: " + e.getMessage());
        }

        // 尝试加载内置中文粗体字体
        try {
            return loadEmbeddedChineseBoldFont(document);
        } catch (Exception e) {
            logger.warn("无法加载内置中文粗体字体: " + e.getMessage());
        }

        // 最后备选方案：使用普通中文字体
        try {
            return getChineseFont(document);
        } catch (Exception e) {
            logger.warn("无法加载中文字体作为粗体备选: " + e.getMessage());
            throw new IOException("无法找到支持中文的粗体字体");
        }
    }

    /**
     * 查找系统中文字体
     */
    private java.io.File findSystemChineseFont() {
        String[] fontPaths = {
            // macOS - 使用实际存在的字体
            "/System/Library/Fonts/STHeiti Medium.ttc",
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
            "/System/Library/Fonts/AppleSDGothicNeo.ttc",
            "/Library/Fonts/Arial Unicode MS.ttf",
            // Windows
            "C:/Windows/Fonts/simsun.ttc",
            "C:/Windows/Fonts/msyh.ttc",
            "C:/Windows/Fonts/simhei.ttf",
            // Linux
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
        };

        for (String path : fontPaths) {
            java.io.File file = new java.io.File(path);
            if (file.exists()) {
                logger.info("找到字体文件: " + path);
                return file;
            }
        }
        logger.warn("未找到任何系统中文字体");
        return null;
    }

    /**
     * 查找系统中文粗体字体
     */
    private java.io.File findSystemChineseBoldFont() {
        String[] fontPaths = {
            // macOS
            "/System/Library/Fonts/STHeiti Medium.ttc",
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
            "/Library/Fonts/Arial Unicode MS.ttf",
            // Windows
            "C:/Windows/Fonts/simhei.ttf",
            "C:/Windows/Fonts/msyhbd.ttc",
            // Linux
            "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf"
        };

        for (String path : fontPaths) {
            java.io.File file = new java.io.File(path);
            if (file.exists()) {
                return file;
            }
        }
        return null;
    }

    /**
     * 加载内置中文字体
     */
    private PDFont loadEmbeddedChineseFont(PDDocument document) throws IOException {
        // 尝试加载项目中的中文字体文件
        String[] fontFiles = {
            "/fonts/楷体_GB2312.ttf",
            "/fonts/仿宋_GB2312.ttf",
            "/fonts/方正小标宋简体.ttf"
        };

        for (String fontPath : fontFiles) {
            try (java.io.InputStream fontStream = getClass().getResourceAsStream(fontPath)) {
                if (fontStream != null) {
                    logger.info("成功加载内置字体: " + fontPath);
                    return PDType0Font.load(document, fontStream);
                }
            } catch (Exception e) {
                logger.warn("无法加载内置字体 " + fontPath + ": " + e.getMessage());
            }
        }

        throw new IOException("无法加载任何内置中文字体");
    }

    /**
     * 加载内置中文粗体字体
     */
    private PDFont loadEmbeddedChineseBoldFont(PDDocument document) throws IOException {
        // 使用方正小标宋作为粗体字体
        try (java.io.InputStream fontStream = getClass().getResourceAsStream("/fonts/方正小标宋简体.ttf")) {
            if (fontStream != null) {
                logger.info("成功加载内置粗体字体: 方正小标宋简体.ttf");
                return PDType0Font.load(document, fontStream);
            }
        } catch (Exception e) {
            logger.warn("无法加载内置粗体字体: " + e.getMessage());
        }

        // 备选方案：使用普通字体
        return loadEmbeddedChineseFont(document);
    }

    /**
     * 处理中文字符，保持原始中文内容
     */
    private String processChinese(String text) {
        if (text == null) return "";

        // 进行HTML实体解码
        text = text.replaceAll("&mdash;", "—")
                  .replaceAll("&nbsp;", " ")
                  .replaceAll("&quot;", "\"")
                  .replaceAll("&amp;", "&")
                  .replaceAll("&lt;", "<")
                  .replaceAll("&gt;", ">")
                  .replaceAll("&hellip;", "…")
                  .replaceAll("&ldquo;", "")
                  .replaceAll("&rdquo;", "");

        // 清理多余的空格，但保持中文字符
        text = text.replaceAll("\\s+", " ").trim();

        return text;
    }
}
