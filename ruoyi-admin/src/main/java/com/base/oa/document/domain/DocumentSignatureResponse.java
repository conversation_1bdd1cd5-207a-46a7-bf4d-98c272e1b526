package com.base.oa.document.domain;

import java.util.Date;

/**
 * 文档签章响应
 *
 * <AUTHOR> Team
 * @date 2025-01-09
 */
public class DocumentSignatureResponse {
    
    /** 操作是否成功 */
    private boolean success;
    
    /** 消息 */
    private String message;
    
    /** 签章后的文档路径 */
    private String signedDocumentPath;
    
    /** 签章ID */
    private String signatureId;
    
    /** 签章时间 */
    private Date signTime;
    
    /** 证书信息 */
    private String certificateInfo;
    
    /** 验证状态 */
    private String verificationStatus;
    
    // 构造函数
    public DocumentSignatureResponse() {
    }
    
    public DocumentSignatureResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    public DocumentSignatureResponse(boolean success, String signedDocumentPath, String signatureId) {
        this.success = success;
        this.signedDocumentPath = signedDocumentPath;
        this.signatureId = signatureId;
    }
    
    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public String getSignedDocumentPath() {
        return signedDocumentPath;
    }
    
    public void setSignedDocumentPath(String signedDocumentPath) {
        this.signedDocumentPath = signedDocumentPath;
    }
    
    public String getSignatureId() {
        return signatureId;
    }
    
    public void setSignatureId(String signatureId) {
        this.signatureId = signatureId;
    }
    
    public Date getSignTime() {
        return signTime;
    }
    
    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }
    
    public String getCertificateInfo() {
        return certificateInfo;
    }
    
    public void setCertificateInfo(String certificateInfo) {
        this.certificateInfo = certificateInfo;
    }
    
    public String getVerificationStatus() {
        return verificationStatus;
    }
    
    public void setVerificationStatus(String verificationStatus) {
        this.verificationStatus = verificationStatus;
    }
}
