package com.base.oa.document.service.impl;

import com.base.oa.document.domain.OaFileAttachment;
import com.base.oa.document.mapper.OaFileAttachmentMapper;
import com.base.oa.document.service.IOaFileAttachmentService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 文件附件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class OaFileAttachmentServiceImpl implements IOaFileAttachmentService {
    
    @Autowired
    private OaFileAttachmentMapper oaFileAttachmentMapper;
    
    /**
     * 根据业务ID和业务类型查询附件列表
     */
    @Override
    public List<OaFileAttachment> selectByBusinessIdAndType(Long businessId, String businessType) {
        QueryWrapper<OaFileAttachment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("business_id", businessId)
                   .eq("business_type", businessType)
                   .eq("status", "1") // 只查询正常状态的附件
                   .orderByAsc("create_time"); // 使用create_time字段排序
        
        return oaFileAttachmentMapper.selectList(queryWrapper);
    }
    
    /**
     * 根据业务ID查询发文附件
     */
    @Override
    public List<OaFileAttachment> selectByDocumentId(Long documentId) {
        return selectByBusinessIdAndType(documentId, "document_send");
    }
    
    /**
     * 查询PDF类型的附件
     */
    @Override
    public List<OaFileAttachment> selectPdfAttachments(Long businessId, String businessType) {
        List<OaFileAttachment> allAttachments = selectByBusinessIdAndType(businessId, businessType);
        
        return allAttachments.stream()
                .filter(this::isPdfFile)
                .collect(Collectors.toList());
    }
    
    /**
     * 查询非PDF类型的附件
     */
    @Override
    public List<OaFileAttachment> selectNonPdfAttachments(Long businessId, String businessType) {
        List<OaFileAttachment> allAttachments = selectByBusinessIdAndType(businessId, businessType);
        
        return allAttachments.stream()
                .filter(attachment -> !isPdfFile(attachment))
                .collect(Collectors.toList());
    }
    
    /**
     * 判断是否为PDF文件
     */
    private boolean isPdfFile(OaFileAttachment attachment) {
        if (attachment.getFileExt() != null) {
            return "pdf".equalsIgnoreCase(attachment.getFileExt());
        }
        if (attachment.getFileName() != null) {
            return attachment.getFileName().toLowerCase().endsWith(".pdf");
        }
        return false;
    }
}
