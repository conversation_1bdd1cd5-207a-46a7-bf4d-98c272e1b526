package com.base.oa.document.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.base.oa.common.service.BaseService;
import com.base.oa.document.domain.OaDocumentReceive;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.mapper.OaDocumentReceiveMapper;
import com.base.oa.document.mapper.OaDocumentSendMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 文档管理快速实现 Service
 * 展示如何在不修改现有 Mapper 的情况下快速实现功能
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaDocumentQuickService {

    // 使用 BaseService 封装，避免修改现有 Mapper
    private final BaseService<OaDocumentSend, OaDocumentSendMapper> sendService;
    private final BaseService<OaDocumentReceive, OaDocumentReceiveMapper> receiveService;

    @Autowired
    public OaDocumentQuickService(OaDocumentSendMapper sendMapper, OaDocumentReceiveMapper receiveMapper) {
        this.sendService = new BaseService<OaDocumentSend, OaDocumentSendMapper>(sendMapper) {};
        this.receiveService = new BaseService<OaDocumentReceive, OaDocumentReceiveMapper>(receiveMapper) {};
    }

    // ==================== 发文管理快速实现 ====================

    /**
     * 快速查询发文列表
     */
    public List<OaDocumentSend> getSendList(String keyword, String status) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = sendService.buildQuery();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(OaDocumentSend::getDocTitle, keyword)
                       .or()
                       .like(OaDocumentSend::getDocNumber, keyword);
        }
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq(OaDocumentSend::getStatus, status);
        }
        
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return sendService.executeQuery(queryWrapper);
    }

    /**
     * 快速分页查询发文
     */
    public IPage<OaDocumentSend> getSendPage(int pageNum, int pageSize, String keyword) {
        Page<OaDocumentSend> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = sendService.buildQuery();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(OaDocumentSend::getDocTitle, keyword);
        }
        
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return sendService.page(page, queryWrapper);
    }

    /**
     * 快速保存发文
     */
    public boolean saveSend(OaDocumentSend document) {
        document.setCreateTime(new Date());
        document.setStatus("草稿");
        return sendService.save(document);
    }

    /**
     * 快速更新发文状态
     */
    public boolean updateSendStatus(Long docId, String status, String updateBy) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = sendService.buildUpdate();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, status);
        updateWrapper.set(OaDocumentSend::getUpdateBy, updateBy);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        
        return sendService.executeUpdate(updateWrapper);
    }

    /**
     * 快速删除发文
     */
    public boolean deleteSend(Long docId) {
        return sendService.removeById(docId);
    }

    /**
     * 快速查询待审批发文
     */
    public List<OaDocumentSend> getPendingSendList() {
        return sendService.list(OaDocumentSend::getStatus, "待审批");
    }

    /**
     * 快速查询我的发文
     */
    public List<OaDocumentSend> getMySendList(Long userId) {
        return sendService.list(OaDocumentSend::getSenderId, userId);
    }

    // ==================== 收文管理快速实现 ====================

    /**
     * 快速查询收文列表
     */
    public List<OaDocumentReceive> getReceiveList(String keyword, String status) {
        LambdaQueryWrapper<OaDocumentReceive> queryWrapper = receiveService.buildQuery();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(OaDocumentReceive::getDocTitle, keyword)
                       .or()
                       .like(OaDocumentReceive::getDocNumber, keyword);
        }
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq(OaDocumentReceive::getStatus, status);
        }
        
        queryWrapper.orderByDesc(OaDocumentReceive::getCreateTime);
        return receiveService.executeQuery(queryWrapper);
    }

    /**
     * 快速分页查询收文
     */
    public IPage<OaDocumentReceive> getReceivePage(int pageNum, int pageSize, String keyword) {
        Page<OaDocumentReceive> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OaDocumentReceive> queryWrapper = receiveService.buildQuery();
        
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.like(OaDocumentReceive::getDocTitle, keyword);
        }
        
        queryWrapper.orderByDesc(OaDocumentReceive::getCreateTime);
        return receiveService.page(page, queryWrapper);
    }

    /**
     * 快速保存收文
     */
    public boolean saveReceive(OaDocumentReceive document) {
        document.setCreateTime(new Date());
        document.setStatus("待处理");
        return receiveService.save(document);
    }

    /**
     * 快速更新收文处理信息
     */
    public boolean updateReceiveHandle(Long docId, String handleStatus, String handleOpinion, Long handlerId, String handlerName) {
        LambdaUpdateWrapper<OaDocumentReceive> updateWrapper = receiveService.buildUpdate();
        updateWrapper.eq(OaDocumentReceive::getDocId, docId);
        updateWrapper.set(OaDocumentReceive::getHandleStatus, handleStatus);
        updateWrapper.set(OaDocumentReceive::getHandleOpinion, handleOpinion);
        updateWrapper.set(OaDocumentReceive::getHandlerId, handlerId);
        updateWrapper.set(OaDocumentReceive::getHandlerName, handlerName);
        updateWrapper.set(OaDocumentReceive::getUpdateTime, new Date());
        
        return receiveService.executeUpdate(updateWrapper);
    }

    /**
     * 快速删除收文
     */
    public boolean deleteReceive(Long docId) {
        return receiveService.removeById(docId);
    }

    /**
     * 快速查询待处理收文
     */
    public List<OaDocumentReceive> getPendingReceiveList() {
        return receiveService.list(OaDocumentReceive::getStatus, "待处理");
    }

    /**
     * 快速查询我的收文
     */
    public List<OaDocumentReceive> getMyReceiveList(Long registrarId) {
        return receiveService.list(OaDocumentReceive::getRegistrarId, registrarId);
    }

    // ==================== 统计功能快速实现 ====================

    /**
     * 快速统计发文数量
     */
    public long countSendByStatus(String status) {
        return sendService.count(OaDocumentSend::getStatus, status);
    }

    /**
     * 快速统计收文数量
     */
    public long countReceiveByStatus(String status) {
        return receiveService.count(OaDocumentReceive::getStatus, status);
    }

    /**
     * 快速检查文档是否存在
     */
    public boolean existsSend(Long docId) {
        return sendService.exists(OaDocumentSend::getDocId, docId);
    }

    public boolean existsReceive(Long docId) {
        return receiveService.exists(OaDocumentReceive::getDocId, docId);
    }

    // ==================== 批量操作快速实现 ====================

    /**
     * 快速批量更新发文状态
     */
    public boolean batchUpdateSendStatus(List<Long> docIds, String status, String updateBy) {
        boolean success = true;
        for (Long docId : docIds) {
            if (!updateSendStatus(docId, status, updateBy)) {
                success = false;
            }
        }
        return success;
    }

    /**
     * 快速批量删除发文
     */
    public boolean batchDeleteSend(List<Long> docIds) {
        return sendService.removeByIds(docIds);
    }

    /**
     * 快速批量删除收文
     */
    public boolean batchDeleteReceive(List<Long> docIds) {
        return receiveService.removeByIds(docIds);
    }

    // ==================== 高级查询快速实现 ====================

    /**
     * 快速查询指定时间范围的文档
     */
    public List<OaDocumentSend> getSendByDateRange(Date startDate, Date endDate) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = sendService.buildQuery();
        queryWrapper.ge(OaDocumentSend::getCreateTime, startDate);
        queryWrapper.le(OaDocumentSend::getCreateTime, endDate);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return sendService.executeQuery(queryWrapper);
    }

    public List<OaDocumentReceive> getReceiveByDateRange(Date startDate, Date endDate) {
        LambdaQueryWrapper<OaDocumentReceive> queryWrapper = receiveService.buildQuery();
        queryWrapper.ge(OaDocumentReceive::getCreateTime, startDate);
        queryWrapper.le(OaDocumentReceive::getCreateTime, endDate);
        queryWrapper.orderByDesc(OaDocumentReceive::getCreateTime);
        return receiveService.executeQuery(queryWrapper);
    }

    /**
     * 快速查询紧急文档
     */
    public List<OaDocumentSend> getUrgentSendList() {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = sendService.buildQuery();
        queryWrapper.eq(OaDocumentSend::getUrgencyLevel, "紧急");
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return sendService.executeQuery(queryWrapper);
    }

    public List<OaDocumentReceive> getUrgentReceiveList() {
        LambdaQueryWrapper<OaDocumentReceive> queryWrapper = receiveService.buildQuery();
        queryWrapper.eq(OaDocumentReceive::getUrgencyLevel, "紧急");
        queryWrapper.orderByDesc(OaDocumentReceive::getCreateTime);
        return receiveService.executeQuery(queryWrapper);
    }
} 