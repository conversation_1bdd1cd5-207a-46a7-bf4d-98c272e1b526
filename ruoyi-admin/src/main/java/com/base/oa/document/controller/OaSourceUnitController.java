package com.base.oa.document.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.oa.document.domain.OaSourceUnit;
import com.base.oa.document.service.IOaSourceUnitService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;

/**
 * 来文单位Controller
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/oa/sourceUnit")
public class OaSourceUnitController extends BaseController
{
    @Autowired
    private IOaSourceUnitService oaSourceUnitService;

    /**
     * 查询来文单位列表
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaSourceUnit oaSourceUnit)
    {
        startPage();
        List<OaSourceUnit> list = oaSourceUnitService.selectOaSourceUnitList(oaSourceUnit);
        return getDataTable(list);
    }

    /**
     * 导出来文单位列表
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:export')")
    @Log(title = "来文单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaSourceUnit oaSourceUnit)
    {
        List<OaSourceUnit> list = oaSourceUnitService.selectOaSourceUnitList(oaSourceUnit);
        ExcelUtil<OaSourceUnit> util = new ExcelUtil<OaSourceUnit>(OaSourceUnit.class);
        util.exportExcel(response, list, "来文单位数据");
    }

    /**
     * 获取来文单位详细信息
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:query')")
    @GetMapping(value = "/{unitId}")
    public AjaxResult getInfo(@PathVariable("unitId") Long unitId)
    {
        return success(oaSourceUnitService.selectOaSourceUnitByUnitId(unitId));
    }

    /**
     * 新增来文单位
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:add')")
    @Log(title = "来文单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaSourceUnit oaSourceUnit)
    {
        return toAjax(oaSourceUnitService.insertOaSourceUnit(oaSourceUnit));
    }

    /**
     * 修改来文单位
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:edit')")
    @Log(title = "来文单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaSourceUnit oaSourceUnit)
    {
        return toAjax(oaSourceUnitService.updateOaSourceUnit(oaSourceUnit));
    }

    /**
     * 删除来文单位
     */
    @PreAuthorize("@ss.hasPermi('oa:sourceUnit:remove')")
    @Log(title = "来文单位", businessType = BusinessType.DELETE)
    @DeleteMapping("/{unitIds}")
    public AjaxResult remove(@PathVariable Long[] unitIds)
    {
        return toAjax(oaSourceUnitService.deleteOaSourceUnitByUnitIds(unitIds));
    }

    /**
     * 获取来文单位建议（用于自动完成）
     */
    @GetMapping("/suggestions")
    public AjaxResult getSourceUnitSuggestions(@RequestParam(value = "query", required = false) String query)
    {
        List<OaSourceUnit> list = oaSourceUnitService.getSourceUnitSuggestions(query);
        return success(list);
    }
}
