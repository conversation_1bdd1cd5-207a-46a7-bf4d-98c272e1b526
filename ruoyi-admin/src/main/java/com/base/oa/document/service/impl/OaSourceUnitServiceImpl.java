package com.base.oa.document.service.impl;

import java.util.List;
import com.base.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.oa.document.mapper.OaSourceUnitMapper;
import com.base.oa.document.domain.OaSourceUnit;
import com.base.oa.document.service.IOaSourceUnitService;

/**
 * 来文单位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class OaSourceUnitServiceImpl implements IOaSourceUnitService 
{
    @Autowired
    private OaSourceUnitMapper oaSourceUnitMapper;

    /**
     * 查询来文单位
     * 
     * @param unitId 来文单位主键
     * @return 来文单位
     */
    @Override
    public OaSourceUnit selectOaSourceUnitByUnitId(Long unitId)
    {
        return oaSourceUnitMapper.selectOaSourceUnitByUnitId(unitId);
    }

    /**
     * 查询来文单位列表
     * 
     * @param oaSourceUnit 来文单位
     * @return 来文单位
     */
    @Override
    public List<OaSourceUnit> selectOaSourceUnitList(OaSourceUnit oaSourceUnit)
    {
        return oaSourceUnitMapper.selectOaSourceUnitList(oaSourceUnit);
    }

    /**
     * 新增来文单位
     * 
     * @param oaSourceUnit 来文单位
     * @return 结果
     */
    @Override
    public int insertOaSourceUnit(OaSourceUnit oaSourceUnit)
    {
        oaSourceUnit.setCreateTime(DateUtils.getNowDate());
        return oaSourceUnitMapper.insertOaSourceUnit(oaSourceUnit);
    }

    /**
     * 修改来文单位
     * 
     * @param oaSourceUnit 来文单位
     * @return 结果
     */
    @Override
    public int updateOaSourceUnit(OaSourceUnit oaSourceUnit)
    {
        oaSourceUnit.setUpdateTime(DateUtils.getNowDate());
        return oaSourceUnitMapper.updateOaSourceUnit(oaSourceUnit);
    }

    /**
     * 批量删除来文单位
     * 
     * @param unitIds 需要删除的来文单位主键
     * @return 结果
     */
    @Override
    public int deleteOaSourceUnitByUnitIds(Long[] unitIds)
    {
        return oaSourceUnitMapper.deleteOaSourceUnitByUnitIds(unitIds);
    }

    /**
     * 删除来文单位信息
     * 
     * @param unitId 来文单位主键
     * @return 结果
     */
    @Override
    public int deleteOaSourceUnitByUnitId(Long unitId)
    {
        return oaSourceUnitMapper.deleteOaSourceUnitByUnitId(unitId);
    }

    /**
     * 根据查询条件获取来文单位建议列表
     * 
     * @param query 查询关键字
     * @return 来文单位建议列表
     */
    @Override
    public List<OaSourceUnit> getSourceUnitSuggestions(String query)
    {
        return oaSourceUnitMapper.selectSourceUnitSuggestions(query);
    }
}
