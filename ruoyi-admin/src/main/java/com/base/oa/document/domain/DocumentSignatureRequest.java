package com.base.oa.document.domain;

import java.util.Date;

/**
 * 文档签章请求
 *
 * <AUTHOR> Team
 * @date 2025-01-09
 */
public class DocumentSignatureRequest {
    
    /** 文档路径 */
    private String documentPath;
    
    /** 签章图片路径 */
    private String signatureImagePath;
    
    /** 证书路径 */
    private String certificatePath;
    
    /** 证书密码 */
    private String certificatePassword;
    
    /** 签章人 */
    private String signer;
    
    /** 签章原因 */
    private String reason;
    
    /** 签章地点 */
    private String location;
    
    /** 签章时间 */
    private Date signTime;
    
    /** 页码 */
    private int pageNumber;
    
    /** X坐标 */
    private float positionX;
    
    /** Y坐标 */
    private float positionY;
    
    /** 宽度 */
    private float width;
    
    /** 高度 */
    private float height;
    
    // Getters and Setters
    public String getDocumentPath() {
        return documentPath;
    }
    
    public void setDocumentPath(String documentPath) {
        this.documentPath = documentPath;
    }
    
    public String getSignatureImagePath() {
        return signatureImagePath;
    }
    
    public void setSignatureImagePath(String signatureImagePath) {
        this.signatureImagePath = signatureImagePath;
    }
    
    public String getCertificatePath() {
        return certificatePath;
    }
    
    public void setCertificatePath(String certificatePath) {
        this.certificatePath = certificatePath;
    }
    
    public String getCertificatePassword() {
        return certificatePassword;
    }
    
    public void setCertificatePassword(String certificatePassword) {
        this.certificatePassword = certificatePassword;
    }
    
    public String getSigner() {
        return signer;
    }
    
    public void setSigner(String signer) {
        this.signer = signer;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public Date getSignTime() {
        return signTime;
    }
    
    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }
    
    public int getPageNumber() {
        return pageNumber;
    }
    
    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    public float getPositionX() {
        return positionX;
    }
    
    public void setPositionX(float positionX) {
        this.positionX = positionX;
    }
    
    public float getPositionY() {
        return positionY;
    }
    
    public void setPositionY(float positionY) {
        this.positionY = positionY;
    }
    
    public float getWidth() {
        return width;
    }
    
    public void setWidth(float width) {
        this.width = width;
    }
    
    public float getHeight() {
        return height;
    }
    
    public void setHeight(float height) {
        this.height = height;
    }
}
