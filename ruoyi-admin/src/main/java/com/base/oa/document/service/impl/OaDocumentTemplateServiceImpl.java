package com.base.oa.document.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.oa.document.mapper.OaDocumentTemplateMapper;
import com.base.oa.document.domain.OaDocumentTemplate;
import com.base.oa.document.service.IOaDocumentTemplateService;
import com.base.common.core.text.Convert;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 公文模板（红头）Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Service
public class OaDocumentTemplateServiceImpl implements IOaDocumentTemplateService 
{
    @Autowired
    private OaDocumentTemplateMapper oaDocumentTemplateMapper;

    /**
     * 查询公文模板（红头）
     *
     * @param templateId 公文模板（红头）主键
     * @return 公文模板（红头）
     */
    @Override
    public OaDocumentTemplate selectOaDocumentTemplateByTemplateId(Long templateId)
    {
        return oaDocumentTemplateMapper.selectById(templateId);
    }

    /**
     * 查询公文模板（红头）列表
     *
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 公文模板（红头）
     */
    @Override
    public List<OaDocumentTemplate> selectOaDocumentTemplateList(OaDocumentTemplate oaDocumentTemplate)
    {
        LambdaQueryWrapper<OaDocumentTemplate> queryWrapper = Wrappers.lambdaQuery();

        if (StringUtils.isNotEmpty(oaDocumentTemplate.getTemplateName())) {
            queryWrapper.like(OaDocumentTemplate::getTemplateName, oaDocumentTemplate.getTemplateName());
        }
        if (StringUtils.isNotEmpty(oaDocumentTemplate.getTemplateType())) {
            queryWrapper.eq(OaDocumentTemplate::getTemplateType, oaDocumentTemplate.getTemplateType());
        }
        if (StringUtils.isNotEmpty(oaDocumentTemplate.getTemplateCode())) {
            queryWrapper.eq(OaDocumentTemplate::getTemplateCode, oaDocumentTemplate.getTemplateCode());
        }
        if (StringUtils.isNotEmpty(oaDocumentTemplate.getIsDefault())) {
            queryWrapper.eq(OaDocumentTemplate::getIsDefault, oaDocumentTemplate.getIsDefault());
        }
        if (StringUtils.isNotEmpty(oaDocumentTemplate.getStatus())) {
            queryWrapper.eq(OaDocumentTemplate::getStatus, oaDocumentTemplate.getStatus());
        }

        queryWrapper.orderByDesc(OaDocumentTemplate::getCreateTime);

        return oaDocumentTemplateMapper.selectList(queryWrapper);
    }

    /**
     * 新增公文模板（红头）
     *
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 结果
     */
    @Override
    public int insertOaDocumentTemplate(OaDocumentTemplate oaDocumentTemplate)
    {
        oaDocumentTemplate.setCreateTime(DateUtils.getNowDate());
        oaDocumentTemplate.setCreateBy(SecurityUtils.getUsername());
        return oaDocumentTemplateMapper.insert(oaDocumentTemplate);
    }

    /**
     * 修改公文模板（红头）
     *
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 结果
     */
    @Override
    public int updateOaDocumentTemplate(OaDocumentTemplate oaDocumentTemplate)
    {
        oaDocumentTemplate.setUpdateTime(DateUtils.getNowDate());
        oaDocumentTemplate.setUpdateBy(SecurityUtils.getUsername());
        return oaDocumentTemplateMapper.updateById(oaDocumentTemplate);
    }

    /**
     * 批量删除公文模板（红头）
     *
     * @param templateIds 需要删除的公文模板（红头）主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentTemplateByTemplateIds(Long[] templateIds)
    {
        return oaDocumentTemplateMapper.deleteBatchIds(Arrays.asList(templateIds));
    }

    /**
     * 删除公文模板（红头）信息
     *
     * @param templateId 公文模板（红头）主键
     * @return 结果
     */
    @Override
    public int deleteOaDocumentTemplateByTemplateId(Long templateId)
    {
        return oaDocumentTemplateMapper.deleteById(templateId);
    }

    /**
     * 获取可用的红头模板列表
     *
     * @param templateType 模板类型
     * @return 模板集合
     */
    @Override
    public List<OaDocumentTemplate> getAvailableTemplates(String templateType)
    {
        LambdaQueryWrapper<OaDocumentTemplate> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OaDocumentTemplate::getTemplateType, templateType)
                   .eq(OaDocumentTemplate::getStatus, "0")
                   .orderByDesc(OaDocumentTemplate::getIsDefault)
                   .orderByDesc(OaDocumentTemplate::getCreateTime);
        return oaDocumentTemplateMapper.selectList(queryWrapper);
    }

    /**
     * 获取默认红头模板
     *
     * @param templateType 模板类型
     * @param issuingOrgan 发文机关
     * @return 默认模板
     */
    @Override
    public OaDocumentTemplate getDefaultTemplate(String templateType, String issuingOrgan)
    {
        LambdaQueryWrapper<OaDocumentTemplate> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OaDocumentTemplate::getTemplateType, templateType)
                   .eq(OaDocumentTemplate::getIsDefault, "1")
                   .eq(OaDocumentTemplate::getStatus, "0");
        if (StringUtils.isNotEmpty(issuingOrgan)) {
            queryWrapper.eq(OaDocumentTemplate::getIssuingOrgan, issuingOrgan);
        }
        queryWrapper.last("LIMIT 1");
        return oaDocumentTemplateMapper.selectOne(queryWrapper);
    }

    /**
     * 根据机关和文种获取适用模板
     *
     * @param issuingOrgan 发文机关
     * @param docGenre 文种
     * @return 适用模板集合
     */
    @Override
    public List<OaDocumentTemplate> getTemplatesByOrganAndGenre(String issuingOrgan, String docGenre)
    {
        LambdaQueryWrapper<OaDocumentTemplate> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OaDocumentTemplate::getStatus, "0");
        if (StringUtils.isNotEmpty(issuingOrgan)) {
            queryWrapper.eq(OaDocumentTemplate::getIssuingOrgan, issuingOrgan);
        }
        if (StringUtils.isNotEmpty(docGenre)) {
            queryWrapper.like(OaDocumentTemplate::getApplicableGenres, docGenre);
        }
        queryWrapper.orderByDesc(OaDocumentTemplate::getIsDefault)
                   .orderByDesc(OaDocumentTemplate::getCreateTime);
        return oaDocumentTemplateMapper.selectList(queryWrapper);
    }

    /**
     * 设置默认模板
     *
     * @param templateId 模板ID
     * @param templateType 模板类型
     * @param issuingOrgan 发文机关
     * @return 结果
     */
    @Override
    public int setDefaultTemplate(Long templateId, String templateType, String issuingOrgan)
    {
        // 先将同类型同机关的模板都设为非默认
        LambdaQueryWrapper<OaDocumentTemplate> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(OaDocumentTemplate::getTemplateType, templateType);
        if (StringUtils.isNotEmpty(issuingOrgan)) {
            queryWrapper.eq(OaDocumentTemplate::getIssuingOrgan, issuingOrgan);
        }

        OaDocumentTemplate updateTemplate = new OaDocumentTemplate();
        updateTemplate.setIsDefault("0");
        oaDocumentTemplateMapper.update(updateTemplate, queryWrapper);

        // 设置指定模板为默认
        OaDocumentTemplate defaultTemplate = new OaDocumentTemplate();
        defaultTemplate.setTemplateId(templateId);
        defaultTemplate.setIsDefault("1");
        defaultTemplate.setUpdateTime(DateUtils.getNowDate());
        defaultTemplate.setUpdateBy(SecurityUtils.getUsername());
        return oaDocumentTemplateMapper.updateById(defaultTemplate);
    }

    /**
     * 生成公文HTML内容
     * 
     * @param templateId 模板ID
     * @param docTitle 文档标题
     * @param docCode 发文字号
     * @param content 正文内容
     * @param signatory 签发人
     * @param issuingOrgan 发文机关
     * @return HTML内容
     */
    @Override
    public String generateDocumentHtml(Long templateId, String docTitle, String docCode, 
                                     String content, String signatory, String issuingOrgan)
    {
        OaDocumentTemplate template = selectOaDocumentTemplateByTemplateId(templateId);
        if (template == null) {
            return "";
        }
        
        String templateContent = template.getTemplateContent();
        if (StringUtils.isEmpty(templateContent)) {
            return "";
        }
        
        // 替换模板变量
        Map<String, String> variables = new HashMap<>();
        variables.put("${docTitle}", StringUtils.isNotEmpty(docTitle) ? docTitle : "");
        variables.put("${docCode}", StringUtils.isNotEmpty(docCode) ? docCode : "");
        variables.put("${content}", StringUtils.isNotEmpty(content) ? content : "");
        variables.put("${signatory}", StringUtils.isNotEmpty(signatory) ? signatory : "");
        variables.put("${issuingOrgan}", StringUtils.isNotEmpty(issuingOrgan) ? issuingOrgan : "");
        variables.put("${currentDate}", DateUtils.dateTimeNow("yyyy年MM月dd日"));
        variables.put("${currentYear}", DateUtils.dateTimeNow("yyyy"));
        variables.put("${currentMonth}", DateUtils.dateTimeNow("MM"));
        variables.put("${currentDay}", DateUtils.dateTimeNow("dd"));
        
        String result = templateContent;
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            result = result.replace(entry.getKey(), entry.getValue());
        }
        
        return result;
    }
}