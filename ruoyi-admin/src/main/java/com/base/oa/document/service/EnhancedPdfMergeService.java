package com.base.oa.document.service;

import com.base.common.exception.ServiceException;
import com.base.common.utils.DateUtils;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.domain.OaFileAttachment;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.awt.Color;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 增强的PDF合并服务
 * 支持真实PDF附件内容的合并
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
@Service
public class EnhancedPdfMergeService {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedPdfMergeService.class);
    
    // 页面设置
    private static final float MARGIN = 50;
    private static final float FONT_SIZE = 12;
    private static final float TITLE_FONT_SIZE = 18;
    private static final float HEADER_FONT_SIZE = 24;
    private static final float LINE_HEIGHT = 20;

    /**
     * 文本样式类
     */
    private static class TextStyle {
        String text;
        Color color;
        boolean bold;
        boolean italic;

        public TextStyle(String text, Color color, boolean bold, boolean italic) {
            this.text = text;
            this.color = color != null ? color : Color.BLACK;
            this.bold = bold;
            this.italic = italic;
        }
    }

    /**
     * 生成完整的合并PDF，包含真实附件内容
     */
    public byte[] generateCompleteDocumentPdf(OaDocumentSend sendDoc, List<OaFileAttachment> attachments) {
        try {
            PDDocument finalDocument = new PDDocument();
            
            // 1. 添加审批单页面
            addApprovalPage(finalDocument, sendDoc);
            
            // 2. 添加红头文件页面（占位）
            addRedHeaderPage(finalDocument);
            
            // 3. 添加底稿页面
            addDraftPage(finalDocument, sendDoc);
            
            // 4. 合并PDF附件
            if (attachments != null && !attachments.isEmpty()) {
                mergePdfAttachments(finalDocument, attachments);
            }
            
            // 5. 添加非PDF附件列表页面
            List<OaFileAttachment> nonPdfAttachments = getNonPdfAttachments(attachments);
            if (!nonPdfAttachments.isEmpty()) {
                addNonPdfAttachmentPage(finalDocument, nonPdfAttachments);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            finalDocument.save(outputStream);
            finalDocument.close();
            
            return outputStream.toByteArray();
            
        } catch (Exception e) {
            logger.error("生成完整合并PDF失败", e);
            throw new ServiceException("生成完整合并PDF失败: " + e.getMessage());
        }
    }
    
    /**
     * 合并PDF附件到主文档
     */
    private void mergePdfAttachments(PDDocument mainDocument, List<OaFileAttachment> attachments) {
        for (OaFileAttachment attachment : attachments) {
            if (isPdfFile(attachment)) {
                try {
                    // 添加分隔页
                    addAttachmentSeparatorPage(mainDocument, attachment);

                    // 暂时只添加附件信息，不合并实际PDF内容
                    // TODO: 后续可以使用PDFMergerUtility实现真正的PDF合并
                    logger.info("已添加PDF附件信息: " + attachment.getFileName());
                } catch (Exception e) {
                    logger.error("处理PDF附件失败: " + attachment.getFileName(), e);
                }
            }
        }
    }
    
    /**
     * 添加附件分隔页
     */
    private void addAttachmentSeparatorPage(PDDocument document, OaFileAttachment attachment) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 标题
            yPosition = addCenteredText(contentStream, "附件", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 附件信息
            yPosition = addLeftAlignedText(contentStream, "文件名: " + attachment.getFileName(), 
                font, FONT_SIZE, yPosition, MARGIN);
            yPosition -= 20;
            
            if (attachment.getFileSize() != null) {
                yPosition = addLeftAlignedText(contentStream, "文件大小: " + attachment.getFileSizeDisplay(), 
                    font, FONT_SIZE, yPosition, MARGIN);
                yPosition -= 20;
            }
            
            if (attachment.getUploadTime() != null) {
                String uploadTime = DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", attachment.getUploadTime());
                yPosition = addLeftAlignedText(contentStream, "上传时间: " + uploadTime, 
                    font, FONT_SIZE, yPosition, MARGIN);
                yPosition -= 20;
            }
            
            if (StringUtils.isNotEmpty(attachment.getUploadUserName())) {
                addLeftAlignedText(contentStream, "上传人: " + attachment.getUploadUserName(), 
                    font, FONT_SIZE, yPosition, MARGIN);
            }
        }
    }
    
    /**
     * 添加非PDF附件列表页面
     */
    private void addNonPdfAttachmentPage(PDDocument document, List<OaFileAttachment> nonPdfAttachments) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 标题
            yPosition = addCenteredText(contentStream, "其他附件", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 附件列表
            for (int i = 0; i < nonPdfAttachments.size(); i++) {
                OaFileAttachment attachment = nonPdfAttachments.get(i);
                String info = String.format("%d. %s (%s)", 
                    i + 1, 
                    attachment.getFileName(),
                    attachment.getFileSizeDisplay());
                
                yPosition = addLeftAlignedText(contentStream, info, font, FONT_SIZE, yPosition, MARGIN);
                yPosition -= 20;
                
                // 检查是否需要换页
                if (yPosition < MARGIN + 50) {
                    break; // 简化处理，不自动换页
                }
            }
        }
    }
    
    /**
     * 添加审批单页面
     */
    private void addApprovalPage(PDDocument document, OaDocumentSend sendDoc) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 标题
            yPosition = addCenteredText(contentStream, "发文审批单", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 审批信息
            yPosition = addTableRow(contentStream, "文件标题:", sendDoc.getDocTitle(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "发文字号:", sendDoc.getDocNumber(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "收文单位:", sendDoc.getReceiverUnit(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "创建时间:", 
                sendDoc.getCreateTime() != null ? DateUtils.parseDateToStr("yyyy-MM-dd HH:mm:ss", sendDoc.getCreateTime()) : "", 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            yPosition = addTableRow(contentStream, "创建人:", sendDoc.getCreateBy(), 
                font, boldFont, FONT_SIZE, yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
        }
    }
    
    /**
     * 添加红头文件页面
     */
    private void addRedHeaderPage(PDDocument document) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont boldFont = getChineseBoldFont(document);
            
            yPosition = addCenteredText(contentStream, "红头文件", boldFont, TITLE_FONT_SIZE, 
                yPosition, page.getMediaBox().getWidth());
            yPosition -= 100;
            
            addCenteredText(contentStream, "（此处应为上传的红头文件内容）",
                getChineseFont(document), FONT_SIZE, yPosition, page.getMediaBox().getWidth());
        }
    }
    
    /**
     * 添加底稿页面
     */
    private void addDraftPage(PDDocument document, OaDocumentSend sendDoc) throws IOException {
        PDPage page = new PDPage(PDRectangle.A4);
        document.addPage(page);
        
        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            float yPosition = page.getMediaBox().getHeight() - MARGIN;
            PDFont font = getChineseFont(document);
            PDFont boldFont = getChineseBoldFont(document);
            
            // 红头标题
            yPosition = addCenteredText(contentStream, "红河哈尼族彝族自治州住房和城乡建设局", 
                boldFont, HEADER_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
            yPosition -= 40;
            
            // 文档标题
            if (StringUtils.isNotEmpty(sendDoc.getDocTitle())) {
                yPosition = addCenteredText(contentStream, sendDoc.getDocTitle(), 
                    boldFont, TITLE_FONT_SIZE, yPosition, page.getMediaBox().getWidth());
                yPosition -= 30;
            }
            
            // 文档内容
            if (StringUtils.isNotEmpty(sendDoc.getDocContent())) {
                List<TextStyle> styledTexts = parseHtmlContent(sendDoc.getDocContent());
                yPosition = addStyledText(contentStream, styledTexts, font, FONT_SIZE,
                    yPosition, MARGIN, page.getMediaBox().getWidth() - 2 * MARGIN);
            }
        }
    }
    
    // 辅助方法
    private boolean isPdfFile(OaFileAttachment attachment) {
        if (attachment.getFileExt() != null) {
            return "pdf".equalsIgnoreCase(attachment.getFileExt());
        }
        if (attachment.getFileName() != null) {
            return attachment.getFileName().toLowerCase().endsWith(".pdf");
        }
        return false;
    }
    
    private List<OaFileAttachment> getNonPdfAttachments(List<OaFileAttachment> attachments) {
        if (attachments == null) return new java.util.ArrayList<>();
        
        return attachments.stream()
            .filter(attachment -> !isPdfFile(attachment))
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 解析HTML内容并提取样式信息
     */
    private List<TextStyle> parseHtmlContent(String content) {
        List<TextStyle> styledTexts = new ArrayList<>();
        if (content == null || content.trim().isEmpty()) {
            return styledTexts;
        }

        // 解码HTML实体
        content = content.replaceAll("&nbsp;", " ")
                        .replaceAll("&quot;", "\"")
                        .replaceAll("&amp;", "&")
                        .replaceAll("&lt;", "<")
                        .replaceAll("&gt;", ">");

        // 正则表达式匹配带样式的文本
        Pattern pattern = Pattern.compile("(<[^>]*>)|([^<]+)");
        Matcher matcher = pattern.matcher(content);

        Color currentColor = Color.BLACK;
        boolean currentBold = false;
        boolean currentItalic = false;

        while (matcher.find()) {
            String match = matcher.group();

            if (match.startsWith("<")) {
                // 处理HTML标签
                if (match.contains("color")) {
                    currentColor = parseColor(match);
                } else if (match.toLowerCase().contains("<b>") || match.toLowerCase().contains("<strong>")) {
                    currentBold = true;
                } else if (match.toLowerCase().contains("</b>") || match.toLowerCase().contains("</strong>")) {
                    currentBold = false;
                } else if (match.toLowerCase().contains("<i>") || match.toLowerCase().contains("<em>")) {
                    currentItalic = true;
                } else if (match.toLowerCase().contains("</i>") || match.toLowerCase().contains("</em>")) {
                    currentItalic = false;
                } else if (match.toLowerCase().contains("</span>") || match.toLowerCase().contains("</font>")) {
                    // 重置样式
                    currentColor = Color.BLACK;
                }
            } else {
                // 处理文本内容
                if (!match.trim().isEmpty()) {
                    styledTexts.add(new TextStyle(match, currentColor, currentBold, currentItalic));
                }
            }
        }

        return styledTexts;
    }

    /**
     * 从HTML标签中解析颜色
     */
    private Color parseColor(String tag) {
        // 匹配 style="color: #rrggbb" 或 style="color: rgb(r,g,b)"
        Pattern colorPattern = Pattern.compile("color\\s*:\\s*(#[0-9a-fA-F]{6}|rgb\\s*\\(\\s*\\d+\\s*,\\s*\\d+\\s*,\\s*\\d+\\s*\\))", Pattern.CASE_INSENSITIVE);
        Matcher colorMatcher = colorPattern.matcher(tag);

        if (colorMatcher.find()) {
            String colorValue = colorMatcher.group(1);
            try {
                if (colorValue.startsWith("#")) {
                    // 十六进制颜色
                    return Color.decode(colorValue);
                } else if (colorValue.startsWith("rgb")) {
                    // RGB颜色
                    Pattern rgbPattern = Pattern.compile("rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)");
                    Matcher rgbMatcher = rgbPattern.matcher(colorValue);
                    if (rgbMatcher.find()) {
                        int r = Integer.parseInt(rgbMatcher.group(1));
                        int g = Integer.parseInt(rgbMatcher.group(2));
                        int b = Integer.parseInt(rgbMatcher.group(3));
                        return new Color(r, g, b);
                    }
                }
            } catch (Exception e) {
                logger.warn("解析颜色失败: {}", colorValue, e);
            }
        }

        return Color.BLACK;
    }

    private String cleanContent(String content) {
        if (content == null) return "";
        return content.replaceAll("<[^>]+>", "")
                     .replaceAll("&nbsp;", " ")
                     .replaceAll("&quot;", "\"")
                     .replaceAll("&amp;", "&")
                     .replaceAll("&lt;", "<")
                     .replaceAll("&gt;", ">")
                     .trim();
    }
    
    /**
     * 获取中文字体
     */
    private PDFont getChineseFont(PDDocument document) throws IOException {
        try {
            // 尝试加载项目内置字体
            String[] embeddedFonts = {
                "/fonts/楷体_GB2312.ttf",
                "/fonts/仿宋_GB2312.ttf",
                "/fonts/方正小标宋简体.ttf"
            };
            
            for (String fontPath : embeddedFonts) {
                try (InputStream fontStream = getClass().getResourceAsStream(fontPath)) {
                    if (fontStream != null) {
                        logger.info("成功加载内置字体: " + fontPath);
                        return PDType0Font.load(document, fontStream);
                    }
                }
            }
            
            throw new IOException("无法加载任何内置中文字体");
            
        } catch (Exception e) {
            logger.error("加载中文字体失败: " + e.getMessage());
            throw new IOException("无法加载中文字体");
        }
    }
    
    /**
     * 获取中文粗体字体
     */
    private PDFont getChineseBoldFont(PDDocument document) throws IOException {
        try {
            // 使用方正小标宋作为粗体字体
            try (InputStream fontStream = getClass().getResourceAsStream("/fonts/方正小标宋简体.ttf")) {
                if (fontStream != null) {
                    return PDType0Font.load(document, fontStream);
                }
            }
            
            // 备选方案：使用普通字体
            return getChineseFont(document);
        } catch (Exception e) {
            return getChineseFont(document);
        }
    }
    
    // 文本添加辅助方法
    private float addCenteredText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float pageWidth) throws IOException {
        if (text == null) text = "";
        
        float textWidth = font.getStringWidth(text) / 1000 * fontSize;
        float xPosition = (pageWidth - textWidth) / 2;
        
        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(text);
        contentStream.endText();
        
        return yPosition - LINE_HEIGHT;
    }
    
    private float addLeftAlignedText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float xPosition) throws IOException {
        if (text == null) text = "";
        
        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(text);
        contentStream.endText();
        
        return yPosition - LINE_HEIGHT;
    }
    
    private float addTableRow(PDPageContentStream contentStream, String label, String value,
            PDFont font, PDFont boldFont, float fontSize, float yPosition, float xPosition, float width) throws IOException {
        if (value == null) value = "";
        
        // 标签
        contentStream.beginText();
        contentStream.setFont(boldFont, fontSize);
        contentStream.newLineAtOffset(xPosition, yPosition);
        contentStream.showText(label);
        contentStream.endText();
        
        // 值
        float labelWidth = boldFont.getStringWidth(label) / 1000 * fontSize;
        contentStream.beginText();
        contentStream.setFont(font, fontSize);
        contentStream.newLineAtOffset(xPosition + labelWidth + 10, yPosition);
        contentStream.showText(value);
        contentStream.endText();
        
        return yPosition - LINE_HEIGHT;
    }
    
    private float addWrappedText(PDPageContentStream contentStream, String text, PDFont font,
            float fontSize, float yPosition, float xPosition, float width) throws IOException {
        if (text == null || text.trim().isEmpty()) return yPosition;

        // 先处理换行符，将文本按行分割
        String[] lines = text.split("\\r?\\n");
        float currentY = yPosition;

        for (String lineText : lines) {
            if (lineText.trim().isEmpty()) {
                // 空行，只移动Y坐标
                currentY -= LINE_HEIGHT;
                continue;
            }

            // 对于每一行，按字符分割进行自动换行
            String[] chars = lineText.split("");
            StringBuilder line = new StringBuilder();

            for (String ch : chars) {
                // 跳过控制字符（如换行符、回车符等）
                if (ch.matches("\\p{Cntrl}")) {
                    continue;
                }

                String testLine = line.toString() + ch;
                try {
                    float textWidth = font.getStringWidth(testLine) / 1000 * fontSize;

                    if (textWidth > width && line.length() > 0) {
                        // 输出当前行
                        contentStream.beginText();
                        contentStream.setFont(font, fontSize);
                        contentStream.newLineAtOffset(xPosition, currentY);
                        contentStream.showText(line.toString());
                        contentStream.endText();

                        currentY -= LINE_HEIGHT;
                        line = new StringBuilder(ch);
                    } else {
                        line.append(ch);
                    }
                } catch (IllegalArgumentException e) {
                    // 如果字符不被字体支持，跳过该字符
                    logger.warn("字符 '{}' 不被字体支持，已跳过", ch);
                    continue;
                }
            }

            // 输出最后一行
            if (line.length() > 0) {
                try {
                    contentStream.beginText();
                    contentStream.setFont(font, fontSize);
                    contentStream.newLineAtOffset(xPosition, currentY);
                    contentStream.showText(line.toString());
                    contentStream.endText();
                    currentY -= LINE_HEIGHT;
                } catch (IllegalArgumentException e) {
                    logger.warn("输出文本时出错: {}", e.getMessage());
                }
            } else {
                // 即使没有文本也要移动Y坐标
                currentY -= LINE_HEIGHT;
            }
        }

        return currentY;
    }

    /**
     * 添加带样式的文本
     */
    private float addStyledText(PDPageContentStream contentStream, List<TextStyle> styledTexts,
            PDFont font, float fontSize, float yPosition, float xPosition, float width) throws IOException {
        if (styledTexts == null || styledTexts.isEmpty()) return yPosition;

        float currentY = yPosition;
        float currentX = xPosition;
        StringBuilder currentLine = new StringBuilder();
        List<TextStyle> currentLineStyles = new ArrayList<>();

        for (TextStyle style : styledTexts) {
            String text = style.text;
            if (text == null) continue;

            // 处理换行符
            String[] lines = text.split("\\r?\\n");

            for (int i = 0; i < lines.length; i++) {
                String lineText = lines[i];

                if (i > 0) {
                    // 输出当前行
                    currentY = outputStyledLine(contentStream, currentLineStyles, font, fontSize, currentY, xPosition);
                    currentLine = new StringBuilder();
                    currentLineStyles = new ArrayList<>();
                    currentX = xPosition;
                }

                if (lineText.isEmpty()) continue;

                // 按字符处理自动换行
                String[] chars = lineText.split("");
                for (String ch : chars) {
                    // 跳过控制字符
                    if (ch.matches("\\p{Cntrl}")) {
                        continue;
                    }

                    String testLine = currentLine.toString() + ch;
                    try {
                        float textWidth = font.getStringWidth(testLine) / 1000 * fontSize;

                        if (textWidth > width && currentLine.length() > 0) {
                            // 输出当前行
                            currentY = outputStyledLine(contentStream, currentLineStyles, font, fontSize, currentY, xPosition);
                            currentLine = new StringBuilder(ch);
                            currentLineStyles = new ArrayList<>();
                            currentLineStyles.add(new TextStyle(ch, style.color, style.bold, style.italic));
                            currentX = xPosition;
                        } else {
                            currentLine.append(ch);
                            if (currentLineStyles.isEmpty() || !isSameStyle(currentLineStyles.get(currentLineStyles.size() - 1), style)) {
                                currentLineStyles.add(new TextStyle(ch, style.color, style.bold, style.italic));
                            } else {
                                // 合并相同样式的文本
                                TextStyle lastStyle = currentLineStyles.get(currentLineStyles.size() - 1);
                                lastStyle.text += ch;
                            }
                        }
                    } catch (IllegalArgumentException e) {
                        logger.warn("字符 '{}' 不被字体支持，已跳过", ch);
                        continue;
                    }
                }
            }
        }

        // 输出最后一行
        if (currentLine.length() > 0) {
            currentY = outputStyledLine(contentStream, currentLineStyles, font, fontSize, currentY, xPosition);
        }

        return currentY;
    }

    /**
     * 输出一行带样式的文本
     */
    private float outputStyledLine(PDPageContentStream contentStream, List<TextStyle> lineStyles,
            PDFont font, float fontSize, float currentY, float xPosition) throws IOException {
        if (lineStyles.isEmpty()) {
            return currentY - LINE_HEIGHT;
        }

        float currentX = xPosition;

        for (TextStyle style : lineStyles) {
            if (style.text == null || style.text.isEmpty()) continue;

            try {
                contentStream.beginText();
                contentStream.setFont(font, fontSize);
                contentStream.setNonStrokingColor(style.color);
                contentStream.newLineAtOffset(currentX, currentY);
                contentStream.showText(style.text);
                contentStream.endText();

                // 计算下一个文本的X位置
                float textWidth = font.getStringWidth(style.text) / 1000 * fontSize;
                currentX += textWidth;
            } catch (Exception e) {
                logger.warn("输出样式文本时出错: {}", e.getMessage());
            }
        }

        return currentY - LINE_HEIGHT;
    }

    /**
     * 检查两个样式是否相同
     */
    private boolean isSameStyle(TextStyle style1, TextStyle style2) {
        return style1.color.equals(style2.color) &&
               style1.bold == style2.bold &&
               style1.italic == style2.italic;
    }
}
