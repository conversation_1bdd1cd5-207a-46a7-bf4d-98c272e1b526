package com.base.oa.document.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.base.common.core.domain.entity.SysRole;
import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.mapper.OaDocumentSendMapper;
import com.base.oa.document.service.IOaDocumentSendService;
import com.base.oa.document.service.IOaDocumentTemplateService;
import com.base.oa.document.domain.OaDocumentTemplate;
import com.base.oa.seal.service.IOaSealService;
import com.base.oa.seal.domain.OaSeal;
import com.base.oa.workflow.service.IOaWorkflowService;
import com.base.oa.workflow.service.WorkflowStartService;
import com.base.system.service.ISysRoleService;
import com.base.system.service.ISysUserService;
import com.base.system.service.ISysDeptService;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.core.domain.entity.SysDept;
import com.base.common.utils.SecurityUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发文管理 Service 实现类（MyBatis Plus 优化版）
 * 继承 ServiceImpl，在 Service 层使用快捷方法实现所有持久层操作
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class OaDocumentSendServiceImpl extends ServiceImpl<OaDocumentSendMapper, OaDocumentSend>
    implements IOaDocumentSendService {

    @Autowired
    private IOaWorkflowService workflowService;

    @Autowired
    private WorkflowStartService workflowStartService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IOaDocumentTemplateService documentTemplateService;

    @Autowired
    private IOaSealService sealService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private OaDocumentSendMapper oaDocumentSendMapper;

    // ==================== 基础 CRUD 操作（继承自 ServiceImpl）====================
    
    // 以下方法自动继承，无需实现：
    // - save(T entity) - 保存
    // - saveBatch(Collection<T> entityList) - 批量保存
    // - updateById(T entity) - 根据ID更新
    // - updateBatchById(Collection<T> entityList) - 批量更新
    // - removeById(Serializable id) - 根据ID删除
    // - removeBatchByIds(Collection<? extends Serializable> idList) - 批量删除
    // - getById(Serializable id) - 根据ID查询
    // - list() - 查询所有
    // - list(Wrapper<T> queryWrapper) - 条件查询
    // - page(IPage<T> page) - 分页查询
    // - page(IPage<T> page, Wrapper<T> queryWrapper) - 条件分页查询
    // - count() - 统计总数
    // - count(Wrapper<T> queryWrapper) - 条件统计

    // ==================== 业务查询方法（使用 Lambda 表达式）====================

    /**
     * 根据条件查询发文列表
     */
    @Override
    public List<OaDocumentSend> getListByCondition(OaDocumentSend condition) {
        // 使用XML中定义的查询方法，避免MyBatis Plus自动生成SQL的问题
        return oaDocumentSendMapper.selectOaDocumentSendList(condition);
    }

    /**
     * 根据关键词和状态查询发文列表
     */
    @Override
    public List<OaDocumentSend> getListByKeywordAndStatus(String keyword, String status) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        
        // 关键词模糊查询（标题或编号）
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(OaDocumentSend::getDocTitle, keyword)
                       .or()
                       .like(OaDocumentSend::getDocNumber, keyword);
        }
        
        // 状态精确查询
        if (StringUtils.hasText(status)) {
            queryWrapper.eq(OaDocumentSend::getStatus, status);
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        
        return this.list(queryWrapper);
    }

    /**
     * 分页查询发文
     */
    @Override
    public IPage<OaDocumentSend> getPage(int pageNum, int pageSize, String keyword) {
        Page<OaDocumentSend> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.like(OaDocumentSend::getDocTitle, keyword);
        }
        
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.page(page, queryWrapper);
    }

    /**
     * 查询待审批的发文
     */
    @Override
    public List<OaDocumentSend> getPendingList() {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getStatus, "待审批");
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 查询我的发文
     */
    @Override
    public List<OaDocumentSend> getMyList(String createBy) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getCreateBy, createBy);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 查询紧急发文
     */
    @Override
    public List<OaDocumentSend> getUrgentList() {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getUrgencyLevel, "紧急");
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据时间范围查询
     */
    @Override
    public List<OaDocumentSend> getListByDateRange(Date startDate, Date endDate) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(OaDocumentSend::getCreateTime, startDate);
        queryWrapper.le(OaDocumentSend::getCreateTime, endDate);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    // ==================== 业务更新方法（使用 Lambda 表达式）====================

    /**
     * 更新发文状态
     */
    @Override
    public boolean updateStatus(Long docId, String status, String updateBy) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, status);
        updateWrapper.set(OaDocumentSend::getUpdateBy, updateBy);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        
        return this.update(updateWrapper);
    }

    /**
     * 审批发文
     */
    @Override
    public boolean approve(Long docId, String approvalOpinion, Long approverId, String approverName) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, "2"); // 2=已审核
        updateWrapper.set(OaDocumentSend::getUpdateBy, approverName);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

        return this.update(updateWrapper);
    }

    /**
     * 发布发文
     */
    @Override
    public boolean publish(Long docId, Long publisherId, String publisherName) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, "已发布");
        updateWrapper.set(OaDocumentSend::getSenderId, publisherId);
        updateWrapper.set(OaDocumentSend::getSenderName, publisherName);
        updateWrapper.set(OaDocumentSend::getSendTime, new Date());
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        
        return this.update(updateWrapper);
    }

    /**
     * 归档发文
     */
    @Override
    public boolean archive(Long docId, Long archiverId, String archiverName) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, "已归档");
        updateWrapper.set(OaDocumentSend::getUpdateBy, archiverName);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        
        return this.update(updateWrapper);
    }

    /**
     * 批量更新状态
     */
    @Override
    public boolean batchUpdateStatus(List<Long> docIds, String status, String updateBy) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(OaDocumentSend::getDocId, docIds);
        updateWrapper.set(OaDocumentSend::getStatus, status);
        updateWrapper.set(OaDocumentSend::getUpdateBy, updateBy);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        
        return this.update(updateWrapper);
    }

    // ==================== 统计方法（使用 Lambda 表达式）====================

    /**
     * 根据状态统计数量
     */
    @Override
    public long countByStatus(String status) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getStatus, status);
        return this.count(queryWrapper);
    }

    /**
     * 统计我的发文数量
     */
    @Override
    public long countMyDocuments(String createBy) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getCreateBy, createBy);
        return this.count(queryWrapper);
    }

    /**
     * 检查文档是否存在
     */
    @Override
    public boolean exists(Long docId) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getDocId, docId);
        return this.count(queryWrapper) > 0;
    }

    // ==================== 高级查询方法 ====================

    /**
     * 查询指定机关的收文
     */
    @Override
    public List<OaDocumentSend> getListBySendOrg(String sendOrg) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getReceiverUnit, sendOrg);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }



    /**
     * 查询指定密级的文档
     */
    @Override
    public List<OaDocumentSend> getListBySecurityLevel(String securityLevel) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getSecurityLevel, securityLevel);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    // ==================== 业务保存方法 ====================

    /**
     * 保存发文（设置默认值）
     */
    @Override
    public boolean saveDocument(OaDocumentSend document) {
        // 设置默认值
        if (document.getStatus() == null) {
            document.setStatus("0"); // 0=草稿
        }
        if (document.getUrgencyLevel() == null) {
            document.setUrgencyLevel("普通");
        }
        if (document.getSecurityLevel() == null) {
            document.setSecurityLevel("公开");
        }

        return this.save(document);
    }

    /**
     * 批量保存发文
     */
    @Override
    @Transactional
    public boolean saveBatchDocuments(List<OaDocumentSend> documents) {
        for (OaDocumentSend document : documents) {
            // 设置默认值
            if (document.getStatus() == null) {
                document.setStatus("0"); // 0=草稿
            }
            if (document.getUrgencyLevel() == null) {
                document.setUrgencyLevel("普通");
            }
            if (document.getSecurityLevel() == null) {
                document.setSecurityLevel("公开");
            }
        }

        return this.saveBatch(documents);
    }

    // ==================== 工作流相关方法 ====================

    /**
     * 提交发文审批
     */
    @Override
    @Transactional
    public boolean submitForApproval(Long docId, String workflowKey, String submitBy) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            // 检查文档状态
            if (!"0".equals(document.getStatus()) && !"退回".equals(document.getStatus())) {
                throw new RuntimeException("只有草稿或退回状态的文档才能提交审批");
            }

            // 准备流程变量
            Map<String, Object> variables = new HashMap<>();
            variables.put("docId", docId);
            variables.put("docTitle", document.getDocTitle());
            variables.put("urgencyLevel", document.getUrgencyLevel());
            variables.put("securityLevel", document.getSecurityLevel());
            variables.put("submitBy", submitBy);
            variables.put("workflowKey", workflowKey);

            // 设置业务主键用于监听器识别文档类型
            String businessKey = "send_doc_" + docId;
            variables.put("businessKey", businessKey);

            // 获取提交人信息
            SysUser submitUser = userService.selectUserByUserName(submitBy);
            if (submitUser == null) {
                throw new RuntimeException("提交人信息不存在");
            }

            System.out.println("=== 提交审批调试信息 ===");
            System.out.println("提交用户: " + submitBy);
            System.out.println("提交用户ID: " + submitUser.getUserId());
            System.out.println("提交用户昵称: " + submitUser.getNickName());
            System.out.println("提交用户部门ID: " + submitUser.getDeptId());

            // 获取科室负责人
            String deptManager = getDeptManager(submitUser.getDeptId());
            System.out.println("获取到的科室负责人: " + deptManager);

            // 获取办公室负责人（假设办公室部门名称为"办公室"）
            String officeManager = getOfficeManager();
            System.out.println("获取到的办公室负责人: " + officeManager);

            // 验证审批人是否存在
            if (deptManager != null && !deptManager.isEmpty()) {
                SysUser deptManagerUser = userService.selectUserByUserName(deptManager);
                if (deptManagerUser == null) {
                    System.err.println("警告：科室负责人用户不存在: " + deptManager);
                } else {
                    System.out.println("科室负责人用户验证成功: " + deptManagerUser.getUserName() + " (" + deptManagerUser.getNickName() + ")");
                }
            }

            if (officeManager != null && !officeManager.isEmpty()) {
                SysUser officeManagerUser = userService.selectUserByUserName(officeManager);
                if (officeManagerUser == null) {
                    System.err.println("警告：办公室负责人用户不存在: " + officeManager);
                } else {
                    System.out.println("办公室负责人用户验证成功: " + officeManagerUser.getUserName() + " (" + officeManagerUser.getNickName() + ")");
                }
            }

            // 设置审批人员
            variables.put("deptManager", deptManager); // 科室负责人
            variables.put("officeManager", officeManager); // 办公室负责人

            System.out.println("流程变量设置完成:");
            System.out.println("  deptManager = " + deptManager);
            System.out.println("  officeManager = " + officeManager);

            // 启动工作流 - 根据流程key选择启动方式
            String processInstanceId;
            if ("document_send_approval_v2".equals(workflowKey)) {
                // 使用新的V2流程启动服务
                processInstanceId = workflowStartService.startDocumentSendApprovalProcess(docId, variables);
            } else {
                // 使用旧的流程启动方式（向后兼容）
                processInstanceId = workflowService.startProcess(workflowKey, businessKey, variables);
            }

            if (processInstanceId != null) {
                // 更新文档状态
                LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(OaDocumentSend::getDocId, docId);
                updateWrapper.set(OaDocumentSend::getStatus, "1"); // 1=审核中
                updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, processInstanceId);
                updateWrapper.set(OaDocumentSend::getCurrentStep, "提交审批");
                updateWrapper.set(OaDocumentSend::getUpdateBy, submitBy);
                updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

                return this.update(updateWrapper);
            }

            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 完成发文审批
     */
    @Override
    @Transactional
    public boolean completeApproval(Long docId, String result, String opinion, String completeBy) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OaDocumentSend::getDocId, docId);

            if ("approve".equals(result)) {
                updateWrapper.set(OaDocumentSend::getStatus, "2"); // 2=已审核
                updateWrapper.set(OaDocumentSend::getCurrentStep, "审批完成");
            } else if ("reject".equals(result)) {
                updateWrapper.set(OaDocumentSend::getStatus, "0"); // 0=草稿（退回）
                updateWrapper.set(OaDocumentSend::getCurrentStep, "审批退回");
                updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, null); // 清除工作流实例ID
            }

            updateWrapper.set(OaDocumentSend::getUpdateBy, completeBy);
            updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

            return this.update(updateWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 撤回发文审批
     */
    @Override
    @Transactional
    public boolean withdrawApproval(Long docId, String reason, String withdrawBy) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            // 检查是否可以撤回
            if (!"审批中".equals(document.getStatus()) && !"特批申请中".equals(document.getStatus())) {
                throw new RuntimeException("只有审批中的文档才能撤回");
            }

            // 终止工作流（这里需要集成工作流引擎）
            if (document.getWorkflowInstanceId() != null) {
                terminateWorkflowProcess(document.getWorkflowInstanceId(), reason);
            }

            // 更新文档状态
            LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OaDocumentSend::getDocId, docId);
            updateWrapper.set(OaDocumentSend::getStatus, "0"); // 0=草稿
            updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, null);
            updateWrapper.set(OaDocumentSend::getCurrentStep, "撤回审批");
            updateWrapper.set(OaDocumentSend::getUpdateBy, withdrawBy);
            updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

            return this.update(updateWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查发文编辑权限 - 简化版，主要用于套红和印章权限判断
     */
    @Override
    public boolean checkEditPermission(Long docId, String username) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            // 简化逻辑：能访问编辑页面就允许编辑
            // 这里主要是为了保持接口兼容性
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 检查是否为最后审批人
     */
    @Override
    public boolean isLastApprover(Long docId, String username) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null || !"1".equals(document.getStatus())) {
                return false;
            }

            if (document.getWorkflowInstanceId() == null) {
                return false;
            }

            // 获取当前待办任务
            List<Task> currentTasks = taskService.createTaskQuery()
                .processInstanceId(document.getWorkflowInstanceId())
                .active()
                .list();

            // 检查当前用户是否为当前任务的处理人
            boolean isCurrentAssignee = false;
            for (Task task : currentTasks) {
                if (username.equals(task.getAssignee())) {
                    isCurrentAssignee = true;
                    break;
                }
            }

            if (!isCurrentAssignee) {
                return false;
            }

            // 检查是否为最后一个审批节点
            // 这里可以通过流程定义或者任务名称来判断
            // 假设最后审批节点的任务名称包含"书记"或"最终"
            for (Task task : currentTasks) {
                if (username.equals(task.getAssignee())) {
                    String taskName = task.getName();
                    if (taskName != null && (taskName.contains("书记") || taskName.contains("最终") || taskName.contains("签发"))) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 应用红头模板
     */
    @Override
    @Transactional
    public boolean applyRedHeaderTemplate(Long docId, Long templateId, String operator) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            // 获取模板内容
            OaDocumentTemplate template = documentTemplateService.selectOaDocumentTemplateByTemplateId(templateId);
            if (template == null) {
                return false;
            }

            // 生成套红后的HTML内容
            String redHeaderContent = documentTemplateService.generateDocumentHtml(
                templateId,
                document.getDocTitle(),
                document.getDocNumber(),
                document.getDocContent(),
                document.getSenderName(),
                template.getIssuingOrgan()
            );

            // 更新文档内容
            LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OaDocumentSend::getDocId, docId);
            updateWrapper.set(OaDocumentSend::getDocContent, redHeaderContent);
            updateWrapper.set(OaDocumentSend::getUpdateBy, operator);
            updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

            return this.update(updateWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 添加印章到文档
     */
    @Override
    @Transactional
    public boolean addSealToDocument(Long docId, Long sealId, String position, String operator) {
        try {
            OaDocumentSend document = this.getById(docId);
            if (document == null) {
                return false;
            }

            // 获取印章信息
            OaSeal seal = sealService.selectOaSealBySealId(sealId);
            if (seal == null || !"1".equals(seal.getStatus())) {
                return false;
            }

            // 在文档内容中添加印章
            String currentContent = document.getDocContent();
            String sealHtml = generateSealHtml(seal, position);
            String updatedContent = insertSealIntoContent(currentContent, sealHtml, position);

            // 更新文档内容
            LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OaDocumentSend::getDocId, docId);
            updateWrapper.set(OaDocumentSend::getDocContent, updatedContent);
            updateWrapper.set(OaDocumentSend::getUpdateBy, operator);
            updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());

            // 记录印章使用
            sealService.recordSealUsage(sealId, getCurrentUserId(), null, "发文用印");

            return this.update(updateWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 生成印章HTML
     */
    private String generateSealHtml(OaSeal seal, String position) {
        StringBuilder sealHtml = new StringBuilder();
        sealHtml.append("<div class=\"document-seal\" style=\"");
        sealHtml.append("position: absolute; ");
        sealHtml.append("z-index: 1000; ");

        // 解析位置信息
        if (position != null && !position.isEmpty()) {
            // position格式: "top:100px;left:200px"
            sealHtml.append(position);
        } else {
            // 默认位置
            sealHtml.append("top: 50%; left: 50%; transform: translate(-50%, -50%);");
        }

        sealHtml.append("\">");

        if (seal.getSealImage() != null && !seal.getSealImage().isEmpty()) {
            sealHtml.append("<img src=\"data:image/png;base64,");
            sealHtml.append(seal.getSealImage());
            sealHtml.append("\" alt=\"").append(seal.getSealName()).append("\" ");
            sealHtml.append("style=\"width: 120px; height: 120px; opacity: 0.8;\" />");
        }

        sealHtml.append("</div>");
        return sealHtml.toString();
    }

    /**
     * 将印章插入到文档内容中
     */
    private String insertSealIntoContent(String content, String sealHtml, String position) {
        if (content == null || content.isEmpty()) {
            return sealHtml;
        }

        // 在文档末尾添加印章
        if (content.contains("</body>")) {
            return content.replace("</body>", sealHtml + "</body>");
        } else {
            return content + sealHtml;
        }
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            return SecurityUtils.getUserId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 查询可提交审批的发文
     */
    @Override
    public List<OaDocumentSend> getSubmittableDocuments(String createBy) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getCreateBy, createBy);
        queryWrapper.in(OaDocumentSend::getStatus, "0", "退回"); // 0=草稿
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 查询审批中的发文
     */
    @Override
    public List<OaDocumentSend> getApprovingDocuments() {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getStatus, "1"); // 1=审核中
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return this.list(queryWrapper);
    }

    /**
     * 根据工作流实例ID查询发文
     */
    @Override
    public OaDocumentSend getByWorkflowInstanceId(String workflowInstanceId) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getWorkflowInstanceId, workflowInstanceId);
        return this.getOne(queryWrapper);
    }

    /**
     * 更新发文工作流状态
     */
    @Override
    public boolean updateWorkflowStatus(Long docId, String workflowInstanceId, String currentStep, String currentAssignee) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, workflowInstanceId);
        updateWrapper.set(OaDocumentSend::getCurrentStep, currentStep);
        updateWrapper.set(OaDocumentSend::getCurrentAssignee, currentAssignee);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        return this.update(updateWrapper);
    }

    /**
     * 清除工作流实例ID
     */
    @Override
    public boolean clearWorkflowInstance(Long docId) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, null);
        updateWrapper.set(OaDocumentSend::getCurrentStep, null);
        updateWrapper.set(OaDocumentSend::getCurrentAssignee, null);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
        return this.update(updateWrapper);
    }

    // ==================== 工作流辅助方法 ====================

    /**
     * 启动工作流流程
     */
    private String startWorkflowProcess(String workflowKey, Long docId, String submitBy) {
        // 这里需要集成工作流引擎，暂时返回模拟ID
        return "process_" + System.currentTimeMillis();
    }

    /**
     * 终止工作流流程
     */
    private void terminateWorkflowProcess(String processInstanceId, String reason) {
        // 这里需要集成工作流引擎
        // 暂时只记录日志
        System.out.println("终止工作流: " + processInstanceId + ", 原因: " + reason);
    }

    /**
     * 获取科室负责人
     * @param deptId 部门ID
     * @return 科室负责人用户名
     */
    private String getDeptManager(Long deptId) {
        try {
            System.out.println("=== 获取科室负责人调试信息 ===");
            System.out.println("部门ID: " + deptId);

            // 获取部门信息
            SysDept dept = deptService.selectDeptById(deptId);
            if (dept != null) {
                System.out.println("部门名称: " + dept.getDeptName());
                System.out.println("部门负责人字段: " + dept.getLeader());

                // 如果部门设置了负责人ID，根据ID查找用户
                if (dept.getLeader() != null && !dept.getLeader().isEmpty()) {
                    try {
                        // 尝试将leader字段解析为用户ID
                        Long leaderId = Long.parseLong(dept.getLeader());
                        SysUser leaderUser = userService.selectUserById(leaderId);
                        if (leaderUser != null) {
                            System.out.println("根据部门负责人ID找到用户: " + leaderUser.getUserName() + " (" + leaderUser.getNickName() + ")");
                            return leaderUser.getUserName();
                        } else {
                            System.out.println("部门负责人ID对应的用户不存在: " + leaderId);
                        }
                    } catch (NumberFormatException e) {
                        System.out.println("部门负责人字段不是有效的用户ID: " + dept.getLeader());
                    }
                }
            }

            // 查找该部门中具有科室负责人角色的用户
            SysUser user = new SysUser();
            user.setDeptId(deptId);
            List<SysUser> deptUsers = userService.selectUserList(user);
            System.out.println("部门用户数量: " + deptUsers.size());

            for (SysUser deptUser : deptUsers) {
                System.out.println("检查用户: " + deptUser.getUserName() + " (ID: " + deptUser.getUserId() + ")");
                // 检查用户是否具有科室负责人角色
                if (hasRole(deptUser.getUserId(), "ksfzr")) {
                    System.out.println("找到具有科室负责人角色的用户: " + deptUser.getUserName());
                    return deptUser.getUserName();
                }
            }

            // 如果找不到科室负责人，查找部门中的第一个用户作为默认负责人
            if (!deptUsers.isEmpty()) {
                String defaultManager = deptUsers.get(0).getUserName();
                System.out.println("使用默认负责人（部门第一个用户）: " + defaultManager);
                return defaultManager;
            }

            // 最后的兜底方案
            System.out.println("使用系统默认负责人: admin");
            return "admin";
        } catch (Exception e) {
            System.err.println("获取科室负责人失败: " + e.getMessage());
            e.printStackTrace();
            return "admin";
        }
    }

    /**
     * 获取办公室负责人
     * @return 办公室负责人用户名
     */
    private String getOfficeManager() {
        try {
            System.out.println("=== 获取办公室负责人调试信息 ===");

            // 查找办公室部门
            SysDept queryDept = new SysDept();
            queryDept.setDeptName("办公室");
            List<SysDept> depts = deptService.selectDeptList(queryDept);
            System.out.println("找到办公室部门数量: " + depts.size());

            if (!depts.isEmpty()) {
                SysDept officeDept = depts.get(0);
                System.out.println("办公室部门: " + officeDept.getDeptName() + " (ID: " + officeDept.getDeptId() + ")");
                System.out.println("办公室负责人字段: " + officeDept.getLeader());

                // 如果办公室设置了负责人ID，根据ID查找用户
                if (officeDept.getLeader() != null && !officeDept.getLeader().isEmpty()) {
                    try {
                        // 尝试将leader字段解析为用户ID
                        Long leaderId = Long.parseLong(officeDept.getLeader());
                        SysUser leaderUser = userService.selectUserById(leaderId);
                        if (leaderUser != null) {
                            System.out.println("根据办公室负责人ID找到用户: " + leaderUser.getUserName() + " (" + leaderUser.getNickName() + ")");
                            return leaderUser.getUserName();
                        } else {
                            System.out.println("办公室负责人ID对应的用户不存在: " + leaderId);
                        }
                    } catch (NumberFormatException e) {
                        System.out.println("办公室负责人字段不是有效的用户ID: " + officeDept.getLeader());
                    }
                }

                // 查找办公室中具有科室负责人角色的用户
                SysUser user = new SysUser();
                user.setDeptId(officeDept.getDeptId());
                List<SysUser> officeUsers = userService.selectUserList(user);
                System.out.println("办公室用户数量: " + officeUsers.size());

                for (SysUser officeUser : officeUsers) {
                    System.out.println("检查办公室用户: " + officeUser.getUserName() + " (ID: " + officeUser.getUserId() + ")");
                    if (hasRole(officeUser.getUserId(), "ksfzr")) {
                        System.out.println("找到具有科室负责人角色的办公室用户: " + officeUser.getUserName());
                        return officeUser.getUserName();
                    }
                }

                // 如果找不到具有科室负责人角色的用户，使用办公室第一个用户
                if (!officeUsers.isEmpty()) {
                    String defaultManager = officeUsers.get(0).getUserName();
                    System.out.println("使用默认办公室负责人（第一个用户）: " + defaultManager);
                    return defaultManager;
                }
            }

            // 如果找不到办公室负责人，返回默认值
            System.out.println("使用系统默认办公室负责人: admin");
            return "admin";
        } catch (Exception e) {
            System.err.println("获取办公室负责人失败: " + e.getMessage());
            e.printStackTrace();
            return "admin";
        }
    }

    /**
     * 检查用户是否具有指定角色
     * @param userId 用户ID
     * @param roleKey 角色标识
     * @return 是否具有角色
     */
    private boolean hasRole(Long userId, String roleKey) {
        try {
            // 使用roleService查询用户角色，确保获取完整的角色信息
            List<SysRole> roles = roleService.selectRolesByUserId(userId);
            System.out.println("用户 " + userId + " 的角色数量: " + roles.size());

            for (SysRole role : roles) {
                System.out.println("角色: " + role.getRoleName() + " (Key: " + role.getRoleKey() + ")");
                if (roleKey.equals(role.getRoleKey())) {
                    System.out.println("找到匹配角色: " + roleKey);
                    return true;
                }
            }

            System.out.println("未找到角色: " + roleKey);
            return false;
        } catch (Exception e) {
            System.err.println("检查用户角色失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 拒绝发文（恢复为草稿状态）
     */
    @Override
    @Transactional
    public boolean rejectDocument(Long docId, String reason) {
        try {
            LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(OaDocumentSend::getDocId, docId);
            updateWrapper.set(OaDocumentSend::getStatus, "0"); // 0=草稿
            updateWrapper.set(OaDocumentSend::getCurrentStep, "审批拒绝");
            updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, null); // 清除工作流实例ID
            updateWrapper.set(OaDocumentSend::getUpdateTime, new Date());
            updateWrapper.set(OaDocumentSend::getUpdateBy, "system");

            return this.update(updateWrapper);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}