package com.base.oa.document.mapper;

import java.util.List;
import com.base.oa.document.domain.OaDocumentSend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 发文管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Mapper
public interface OaDocumentSendMapper extends BaseMapper<OaDocumentSend>
{
    /**
     * 根据条件查询发文管理列表
     *
     * @param oaDocumentSend 查询条件
     * @return 发文管理集合
     */
    List<OaDocumentSend> selectOaDocumentSendList(OaDocumentSend oaDocumentSend);

    /**
     * 根据文档ID查询发文管理
     * 
     * @param docId 发文管理主键
     * @return 发文管理
     */
    default OaDocumentSend selectOaDocumentSendByDocId(Long docId) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getDocId, docId);
        return selectOne(queryWrapper);
    }

    /**
     * 新增发文管理
     * 
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    default int insertOaDocumentSend(OaDocumentSend oaDocumentSend) {
        return insert(oaDocumentSend);
    }

    /**
     * 修改发文管理
     *
     * @param oaDocumentSend 发文管理
     * @return 结果
     */
    default int updateOaDocumentSend(OaDocumentSend oaDocumentSend) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, oaDocumentSend.getDocId());
        
        // 动态设置更新字段
        if (oaDocumentSend.getDocNumber() != null) {
            updateWrapper.set(OaDocumentSend::getDocNumber, oaDocumentSend.getDocNumber());
        }
        if (oaDocumentSend.getDocTitle() != null) {
            updateWrapper.set(OaDocumentSend::getDocTitle, oaDocumentSend.getDocTitle());
        }
        if (oaDocumentSend.getReceiverUnit() != null) {
            updateWrapper.set(OaDocumentSend::getReceiverUnit, oaDocumentSend.getReceiverUnit());
        }
        if (oaDocumentSend.getReceiverContact() != null) {
            updateWrapper.set(OaDocumentSend::getReceiverContact, oaDocumentSend.getReceiverContact());
        }
        if (oaDocumentSend.getSendDate() != null) {
            updateWrapper.set(OaDocumentSend::getSendDate, oaDocumentSend.getSendDate());
        }
        if (oaDocumentSend.getUrgencyLevel() != null) {
            updateWrapper.set(OaDocumentSend::getUrgencyLevel, oaDocumentSend.getUrgencyLevel());
        }
        if (oaDocumentSend.getSecurityLevel() != null) {
            updateWrapper.set(OaDocumentSend::getSecurityLevel, oaDocumentSend.getSecurityLevel());
        }
        if (oaDocumentSend.getDocContent() != null) {
            updateWrapper.set(OaDocumentSend::getDocContent, oaDocumentSend.getDocContent());
        }
        if (oaDocumentSend.getAttachments() != null) {
            updateWrapper.set(OaDocumentSend::getAttachments, oaDocumentSend.getAttachments());
        }
        if (oaDocumentSend.getSenderId() != null) {
            updateWrapper.set(OaDocumentSend::getSenderId, oaDocumentSend.getSenderId());
        }
        if (oaDocumentSend.getSenderName() != null) {
            updateWrapper.set(OaDocumentSend::getSenderName, oaDocumentSend.getSenderName());
        }
        if (oaDocumentSend.getSendTime() != null) {
            updateWrapper.set(OaDocumentSend::getSendTime, oaDocumentSend.getSendTime());
        }
        if (oaDocumentSend.getStatus() != null) {
            updateWrapper.set(OaDocumentSend::getStatus, oaDocumentSend.getStatus());
        }
        if (oaDocumentSend.getWorkflowInstanceId() != null) {
            updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, oaDocumentSend.getWorkflowInstanceId());
        }
        if (oaDocumentSend.getCurrentStep() != null) {
            updateWrapper.set(OaDocumentSend::getCurrentStep, oaDocumentSend.getCurrentStep());
        }
        if (oaDocumentSend.getCurrentAssignee() != null) {
            updateWrapper.set(OaDocumentSend::getCurrentAssignee, oaDocumentSend.getCurrentAssignee());
        }
        if (oaDocumentSend.getUpdateBy() != null) {
            updateWrapper.set(OaDocumentSend::getUpdateBy, oaDocumentSend.getUpdateBy());
        }
        if (oaDocumentSend.getUpdateTime() != null) {
            updateWrapper.set(OaDocumentSend::getUpdateTime, oaDocumentSend.getUpdateTime());
        }
        if (oaDocumentSend.getRemark() != null) {
            updateWrapper.set(OaDocumentSend::getRemark, oaDocumentSend.getRemark());
        }
        
        // 确保更新时间和更新人被设置
        updateWrapper.set(OaDocumentSend::getUpdateTime, oaDocumentSend.getUpdateTime());
        
        return update(null, updateWrapper);
    }

    /**
     * 删除发文管理
     * 
     * @param docId 发文管理主键
     * @return 结果
     */
    default int deleteOaDocumentSendByDocId(Long docId) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getDocId, docId);
        return delete(queryWrapper);
    }

    /**
     * 批量删除发文管理
     * 
     * @param docIds 需要删除的数据主键集合
     * @return 结果
     */
    default int deleteOaDocumentSendByDocIds(Long[] docIds) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OaDocumentSend::getDocId, (Object[]) docIds);
        return delete(queryWrapper);
    }

    /**
     * 根据状态查询文档列表
     * 
     * @param status 文档状态
     * @return 文档列表
     */
    default List<OaDocumentSend> selectByStatus(String status) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getStatus, status);
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return selectList(queryWrapper);
    }

    /**
     * 根据处理人查询待处理文档
     * 
     * @param assignee 处理人
     * @return 文档列表
     */
    default List<OaDocumentSend> selectByAssignee(String assignee) {
        LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OaDocumentSend::getCurrentAssignee, assignee);
        queryWrapper.eq(OaDocumentSend::getStatus, "审批中");
        queryWrapper.orderByDesc(OaDocumentSend::getCreateTime);
        return selectList(queryWrapper);
    }

    /**
     * 更新文档状态
     * 
     * @param docId 文档ID
     * @param status 新状态
     * @param updateBy 更新人
     * @return 结果
     */
    default int updateStatus(@Param("docId") Long docId, @Param("status") String status, @Param("updateBy") String updateBy) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getStatus, status);
        updateWrapper.set(OaDocumentSend::getUpdateBy, updateBy);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new java.util.Date());
        return update(null, updateWrapper);
    }

    /**
     * 更新工作流相关信息
     * 
     * @param docId 文档ID
     * @param workflowInstanceId 工作流实例ID
     * @param currentStep 当前步骤
     * @param currentAssignee 当前处理人
     * @return 结果
     */
    default int updateWorkflowInfo(@Param("docId") Long docId, 
                                   @Param("workflowInstanceId") String workflowInstanceId,
                                   @Param("currentStep") String currentStep,
                                   @Param("currentAssignee") String currentAssignee) {
        LambdaUpdateWrapper<OaDocumentSend> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OaDocumentSend::getDocId, docId);
        updateWrapper.set(OaDocumentSend::getWorkflowInstanceId, workflowInstanceId);
        updateWrapper.set(OaDocumentSend::getCurrentStep, currentStep);
        updateWrapper.set(OaDocumentSend::getCurrentAssignee, currentAssignee);
        updateWrapper.set(OaDocumentSend::getUpdateTime, new java.util.Date());
        return update(null, updateWrapper);
    }
}
