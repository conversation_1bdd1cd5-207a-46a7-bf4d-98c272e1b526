package com.base.oa.document.service;

import com.base.oa.document.domain.DocumentSignatureRequest;
import com.base.oa.document.domain.DocumentSignatureResponse;

/**
 * 文档电子签章服务接口
 *
 * <AUTHOR> Team
 * @date 2025-01-09
 */
public interface IDocumentSignatureService {
    
    /**
     * 对文档进行电子签章
     *
     * @param request 签章请求
     * @return 签章响应
     */
    DocumentSignatureResponse signDocument(DocumentSignatureRequest request);
    
    /**
     * 验证文档电子签章
     *
     * @param documentPath 文档路径
     * @return 验证结果
     */
    boolean verifyDocumentSignature(String documentPath);
    
    /**
     * 获取文档签章信息
     *
     * @param documentPath 文档路径
     * @return 签章信息
     */
    String getDocumentSignatureInfo(String documentPath);
    
    /**
     * 移除文档签章
     *
     * @param documentPath 文档路径
     * @return 操作结果
     */
    boolean removeDocumentSignature(String documentPath);
}
