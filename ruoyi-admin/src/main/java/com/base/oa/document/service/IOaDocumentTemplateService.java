package com.base.oa.document.service;

import java.util.List;
import com.base.oa.document.domain.OaDocumentTemplate;

/**
 * 公文模板（红头）Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IOaDocumentTemplateService 
{
    /**
     * 查询公文模板（红头）
     * 
     * @param templateId 公文模板（红头）主键
     * @return 公文模板（红头）
     */
    public OaDocumentTemplate selectOaDocumentTemplateByTemplateId(Long templateId);

    /**
     * 查询公文模板（红头）列表
     * 
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 公文模板（红头）集合
     */
    public List<OaDocumentTemplate> selectOaDocumentTemplateList(OaDocumentTemplate oaDocumentTemplate);

    /**
     * 新增公文模板（红头）
     * 
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 结果
     */
    public int insertOaDocumentTemplate(OaDocumentTemplate oaDocumentTemplate);

    /**
     * 修改公文模板（红头）
     * 
     * @param oaDocumentTemplate 公文模板（红头）
     * @return 结果
     */
    public int updateOaDocumentTemplate(OaDocumentTemplate oaDocumentTemplate);

    /**
     * 批量删除公文模板（红头）
     * 
     * @param templateIds 需要删除的公文模板（红头）主键集合
     * @return 结果
     */
    public int deleteOaDocumentTemplateByTemplateIds(Long[] templateIds);

    /**
     * 删除公文模板（红头）信息
     * 
     * @param templateId 公文模板（红头）主键
     * @return 结果
     */
    public int deleteOaDocumentTemplateByTemplateId(Long templateId);

    /**
     * 获取可用的红头模板列表
     * 
     * @param templateType 模板类型
     * @return 模板集合
     */
    public List<OaDocumentTemplate> getAvailableTemplates(String templateType);

    /**
     * 获取默认红头模板
     * 
     * @param templateType 模板类型
     * @param issuingOrgan 发文机关
     * @return 默认模板
     */
    public OaDocumentTemplate getDefaultTemplate(String templateType, String issuingOrgan);

    /**
     * 根据机关和文种获取适用模板
     * 
     * @param issuingOrgan 发文机关
     * @param docGenre 文种
     * @return 适用模板集合
     */
    public List<OaDocumentTemplate> getTemplatesByOrganAndGenre(String issuingOrgan, String docGenre);

    /**
     * 设置默认模板
     * 
     * @param templateId 模板ID
     * @param templateType 模板类型
     * @param issuingOrgan 发文机关
     * @return 结果
     */
    public int setDefaultTemplate(Long templateId, String templateType, String issuingOrgan);

    /**
     * 生成公文HTML内容
     * 
     * @param templateId 模板ID
     * @param docTitle 文档标题
     * @param docCode 发文字号
     * @param content 正文内容
     * @param signatory 签发人
     * @param issuingOrgan 发文机关
     * @return HTML内容
     */
    public String generateDocumentHtml(Long templateId, String docTitle, String docCode, 
                                     String content, String signatory, String issuingOrgan);
}