package com.base.oa.document.service;

import com.base.oa.document.domain.OaFileAttachment;
import java.util.List;

/**
 * 文件附件Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-26
 */
public interface IOaFileAttachmentService {
    
    /**
     * 根据业务ID和业务类型查询附件列表
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 附件列表
     */
    List<OaFileAttachment> selectByBusinessIdAndType(Long businessId, String businessType);
    
    /**
     * 根据业务ID查询发文附件
     * 
     * @param documentId 发文ID
     * @return 附件列表
     */
    List<OaFileAttachment> selectByDocumentId(Long documentId);
    
    /**
     * 查询PDF类型的附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return PDF附件列表
     */
    List<OaFileAttachment> selectPdfAttachments(Long businessId, String businessType);
    
    /**
     * 查询非PDF类型的附件
     * 
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 非PDF附件列表
     */
    List<OaFileAttachment> selectNonPdfAttachments(Long businessId, String businessType);
}
