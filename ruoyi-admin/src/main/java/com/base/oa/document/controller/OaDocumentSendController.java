package com.base.oa.document.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.base.oa.common.service.PdfExportService;

import com.base.oa.document.domain.OaDocumentSend;
import com.base.oa.document.domain.OaFileAttachment;
import com.base.oa.workflow.domain.OaWorkflowInstance;

import com.base.oa.document.service.IOaDocumentSendService;
import com.base.oa.document.service.PdfBoxService;
import com.base.oa.document.service.EnhancedPdfMergeService;
import com.base.oa.document.service.IOaFileAttachmentService;

/**
 * 发文管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/oa/document/send")
public class OaDocumentSendController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(OaDocumentSendController.class);

    @Autowired
    private IOaDocumentSendService oaDocumentSendService;

    @Autowired
    private PdfExportService pdfExportService;

    @Autowired
    private PdfBoxService pdfBoxService;

    @Autowired
    private EnhancedPdfMergeService enhancedPdfMergeService;

    @Autowired
    private IOaFileAttachmentService fileAttachmentService;

    /**
     * 查询发文管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(OaDocumentSend oaDocumentSend)
    {
        startPage();
        // 如果不是管理员，则只查询与自己相关的数据
        if (!getLoginUser().getUser().isAdmin()) {
            oaDocumentSend.getParams().put("userId", getUsername());
        }
        List<OaDocumentSend> list = oaDocumentSendService.getListByCondition(oaDocumentSend);
        return getDataTable(list);
    }

    /**
     * 导出发文管理列表
     */
    @Log(title = "发文管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OaDocumentSend oaDocumentSend)
    {
        List<OaDocumentSend> list = oaDocumentSendService.getListByCondition(oaDocumentSend);
        ExcelUtil<OaDocumentSend> util = new ExcelUtil<OaDocumentSend>(OaDocumentSend.class);
        util.exportExcel(response, list, "发文管理数据");
    }

    /**
     * 获取发文管理详细信息
     */
    @GetMapping("/{docId}")
    public AjaxResult getInfo(@PathVariable("docId") Long docId)
    {
        return success(oaDocumentSendService.getById(docId));
    }

    /**
     * 新增发文管理
     */
    @Log(title = "发文管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaDocumentSend oaDocumentSend)
    {
        oaDocumentSend.setCreateBy(getUsername());
        oaDocumentSend.setCreateTime(new Date());

        // 设置默认状态为草稿
        if (oaDocumentSend.getStatus() == null || oaDocumentSend.getStatus().isEmpty()) {
            oaDocumentSend.setStatus("0"); // 0=草稿
        }

        boolean result = oaDocumentSendService.save(oaDocumentSend);
        if (result) {
            // 返回新创建的文档信息，包含docId
            return AjaxResult.success("新增成功", oaDocumentSend);
        } else {
            return AjaxResult.error("新增失败");
        }
    }

    /**
     * 修改发文管理
     */
    @Log(title = "发文管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaDocumentSend oaDocumentSend)
    {
        try {
            // 验证必要字段
            if (oaDocumentSend.getDocId() == null) {
                return AjaxResult.error("文档ID不能为空");
            }

            // 设置更新人信息
            oaDocumentSend.setUpdateBy(getUsername());
            oaDocumentSend.setUpdateTime(new Date());

            // 使用MyBatis-Plus的updateById方法
            boolean result = oaDocumentSendService.updateById(oaDocumentSend);

            if (result) {
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新发文时发生异常", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除发文管理
     */
    @Log(title = "发文管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docIds}")
    public AjaxResult remove(@PathVariable String docIds)
    {
        try {
            // 支持单个ID或多个ID（逗号分隔）
            String[] idArray = docIds.split(",");
            List<Long> idList = new ArrayList<>();

            for (String idStr : idArray) {
                try {
                    idList.add(Long.parseLong(idStr.trim()));
                } catch (NumberFormatException e) {
                    return AjaxResult.error("无效的文档ID: " + idStr);
                }
            }

            if (idList.isEmpty()) {
                return AjaxResult.error("请提供要删除的文档ID");
            }

            // 检查文档状态并终止相关工作流
            List<OaDocumentSend> documents = oaDocumentSendService.listByIds(idList);
            for (OaDocumentSend doc : documents) {
                // 如果文档有关联的工作流实例，先终止工作流
                if (doc.getWorkflowInstanceId() != null) {
                    try {
                        oaDocumentSendService.withdrawApproval(doc.getDocId(), "文档被删除", getUsername());
                    } catch (Exception e) {
                        logger.warn("终止工作流失败，文档ID: " + doc.getDocId(), e);
                    }
                }

                // 检查是否允许删除（草稿状态或已终止工作流的文档可以删除）
                if (!"0".equals(doc.getStatus()) && doc.getWorkflowInstanceId() != null) {
                    return AjaxResult.error("文档《" + doc.getDocTitle() + "》正在审批中，无法删除");
                }
            }

            LambdaQueryWrapper<OaDocumentSend> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(OaDocumentSend::getDocId, idList);
            boolean result = oaDocumentSendService.remove(queryWrapper);
            return result ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");

        } catch (Exception e) {
            logger.error("删除发文失败", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }



    /**
     * 发布发文
     */
    @Log(title = "发布发文", businessType = BusinessType.UPDATE)
    @PostMapping("/publish/{docId}")
    public AjaxResult publish(@PathVariable Long docId) {
        try {
            OaDocumentSend doc = oaDocumentSendService.getById(docId);
            if (doc == null) {
                return error("未找到发文记录");
            }
            
            // 检查文档状态，只有已审批的文档才能发布
            if (!"2".equals(doc.getStatus())) {
                return error("只有已审批的发文才能发布");
            }
            
            // 检查必要信息是否完整
            if (doc.getDocTitle() == null || doc.getDocTitle().trim().isEmpty()) {
                return error("发文标题不能为空");
            }
            
            if (doc.getDocContent() == null || doc.getDocContent().trim().isEmpty()) {
                return error("发文内容不能为空");
            }
            
            // 更新发文状态为已发布
            doc.setStatus("3"); // 3=已发布
            doc.setSendTime(new Date());
            doc.setUpdateBy(getUsername());
            doc.setUpdateTime(new Date());
            
            // 如果没有发文人，设置当前用户为发文人
            if (doc.getSenderName() == null || doc.getSenderName().trim().isEmpty()) {
                doc.setSenderName(getUsername());
            }
            
            boolean result = oaDocumentSendService.updateById(doc);
            
            if (result) {
                logger.info("发文发布成功，文档ID: {}, 标题: {}, 发布人: {}", 
                    docId, doc.getDocTitle(), getUsername());
                return success("发布成功");
            } else {
                return error("发布失败");
            }
            
        } catch (Exception e) {
            logger.error("发布发文失败，文档ID: " + docId, e);
            return error("发布失败: " + e.getMessage());
        }
    }

    /**
     * 查询可提交审批的发文
     */
    @GetMapping("/submittable")
    public TableDataInfo getSubmittableDocuments()
    {
        startPage();
        String createBy = getUsername();
        List<OaDocumentSend> list = oaDocumentSendService.getSubmittableDocuments(createBy);
        return getDataTable(list);
    }

    /**
     * 查询审批中的发文
     */
    @GetMapping("/approving")
    public TableDataInfo getApprovingDocuments()
    {
        startPage();
        List<OaDocumentSend> list = oaDocumentSendService.getApprovingDocuments();
        return getDataTable(list);
    }

    /**
     * 完成发文审批
     */
    @Log(title = "完成发文审批", businessType = BusinessType.UPDATE)
    @PostMapping("/complete-approval/{docId}")
    public AjaxResult completeApproval(@PathVariable Long docId, @RequestBody Map<String, Object> params)
    {
        String result = (String) params.get("result"); // approve 或 reject
        String opinion = (String) params.get("opinion");
        String completeBy = getUsername();

        boolean success = oaDocumentSendService.completeApproval(docId, result, opinion, completeBy);
        return success ? success("审批完成") : error("审批失败");
    }

    /**
     * 导出发文PDF
     */
    @PostMapping("/exportPdf/{docId}")
    public void exportPdf(@PathVariable Long docId, HttpServletResponse response) {
        try {
            // 查询发文信息
            OaDocumentSend sendDoc = oaDocumentSendService.getById(docId);
            if (sendDoc == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 生成PDF内容
            byte[] pdfBytes = pdfBoxService.generateSendDocumentPdf(sendDoc);

            // 设置响应头为PDF格式
            response.setContentType("application/pdf");
            response.setContentLength(pdfBytes.length);
            response.setHeader("Content-Disposition",
                "attachment; filename=" + java.net.URLEncoder.encode(
                    sendDoc.getDocTitle() + "_发文.pdf", "UTF-8"));

            // 输出HTML内容
            response.getOutputStream().write(pdfBytes);
            response.getOutputStream().flush();

        } catch (Exception e) {
            logger.error("导出发文PDF失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }



    /**
     * 获取字典标签
     */
    private String getDictLabel(String dictType, String dictValue) {
        if (dictValue == null) return "";
        
        switch (dictType) {
            case "oa_urgency_level":
                switch (dictValue) {
                    case "1": return "特急";
                    case "2": return "加急";
                    case "3": return "平急";
                    case "4": return "普通";
                    default: return dictValue;
                }
            case "oa_security_level":
                switch (dictValue) {
                    case "1": return "公开";
                    case "2": return "内部";
                    case "3": return "秘密";
                    case "4": return "机密";
                    default: return dictValue;
                }
            case "oa_retention_period":
                switch (dictValue) {
                    case "0": return "无";
                    case "1": return "10年";
                    case "2": return "30年";
                    case "3": return "永久";
                    default: return dictValue;
                }
            default:
                return dictValue;
        }
    }

    /**
     * 获取状态标签
     */
    private String getStatusLabel(String status) {
        if (status == null) return "未知";

        switch (status) {
            case "0": return "草稿";
            case "1": return "审核中";
            case "2": return "已审核";
            case "3": return "已归档";
            default: return status;
        }
    }

    /**
     * 提交发文审批
     */
    @Log(title = "发文提交审批", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{docId}")
    public AjaxResult submit(@PathVariable Long docId, @RequestBody(required = false) Map<String, Object> params) {
        try {
            // 默认使用发文审批流程V2（基于部门角色的新流程）
            String workflowKey = "document_send_approval_v2";

            // 如果传入了参数，可以覆盖默认流程
            if (params != null && params.containsKey("workflowKey")) {
                String paramWorkflowKey = (String) params.get("workflowKey");
                if (paramWorkflowKey != null && !paramWorkflowKey.trim().isEmpty()) {
                    workflowKey = paramWorkflowKey;
                }
            }

            System.out.println("发文提交审批 - 使用流程: " + workflowKey);

            boolean result = oaDocumentSendService.submitForApproval(docId, workflowKey, getUsername());
            if (result) {
                return success("提交审批成功");
            } else {
                return error("提交审批失败");
            }
        } catch (Exception e) {
            logger.error("提交发文审批失败", e);
            return error("提交审批失败: " + e.getMessage());
        }
    }

    /**
     * 检查发文编辑权限
     */
    @GetMapping("/check-edit-permission/{docId}")
    public AjaxResult checkEditPermission(@PathVariable Long docId) {
        try {
            String currentUser = getUsername();
            boolean canEdit = oaDocumentSendService.checkEditPermission(docId, currentUser);
            boolean isLastApprover = oaDocumentSendService.isLastApprover(docId, currentUser);

            Map<String, Object> result = new HashMap<>();
            result.put("canEdit", canEdit);
            result.put("isLastApprover", isLastApprover);
            result.put("canApplyTemplate", isLastApprover); // 只有最后审批人可以套红
            result.put("canAddSeal", isLastApprover); // 只有最后审批人可以加印章

            return success(result);
        } catch (Exception e) {
            logger.error("检查编辑权限失败", e);
            return error("检查权限失败: " + e.getMessage());
        }
    }

    /**
     * 应用红头模板
     */
    @Log(title = "应用红头模板", businessType = BusinessType.UPDATE)
    @PostMapping("/apply-template/{docId}")
    public AjaxResult applyTemplate(@PathVariable Long docId, @RequestBody Map<String, Object> params) {
        try {
            String currentUser = getUsername();

            // 检查是否为最后审批人
            if (!oaDocumentSendService.isLastApprover(docId, currentUser)) {
                return error("只有最后审批人可以应用红头模板");
            }

            Long templateId = Long.valueOf(params.get("templateId").toString());
            boolean result = oaDocumentSendService.applyRedHeaderTemplate(docId, templateId, currentUser);

            if (result) {
                return success("红头模板应用成功");
            } else {
                return error("红头模板应用失败");
            }
        } catch (Exception e) {
            logger.error("应用红头模板失败", e);
            return error("应用模板失败: " + e.getMessage());
        }
    }

    /**
     * 添加印章
     */
    @Log(title = "添加印章", businessType = BusinessType.UPDATE)
    @PostMapping("/add-seal/{docId}")
    public AjaxResult addSeal(@PathVariable Long docId, @RequestBody Map<String, Object> params) {
        try {
            String currentUser = getUsername();

            // 检查是否为最后审批人
            if (!oaDocumentSendService.isLastApprover(docId, currentUser)) {
                return error("只有最后审批人可以添加印章");
            }

            Long sealId = Long.valueOf(params.get("sealId").toString());
            String position = (String) params.get("position"); // 印章位置信息

            boolean result = oaDocumentSendService.addSealToDocument(docId, sealId, position, currentUser);

            if (result) {
                return success("印章添加成功");
            } else {
                return error("印章添加失败");
            }
        } catch (Exception e) {
            logger.error("添加印章失败", e);
            return error("添加印章失败: " + e.getMessage());
        }
    }

    /**
     * 审核发文
     */
    @Log(title = "审核发文", businessType = BusinessType.UPDATE)
    @PostMapping("/approve/{docId}")
    public AjaxResult approve(@PathVariable Long docId) {
        OaDocumentSend doc = oaDocumentSendService.getById(docId);
        if (doc == null) return error("未找到发文");
        doc.setStatus("2"); // 2=已审核
        doc.setUpdateBy(getUsername());
        doc.setUpdateTime(new Date());
        oaDocumentSendService.updateById(doc);
        return success("审核成功");
    }

    /**
     * 归档发文
     */
    @Log(title = "归档发文", businessType = BusinessType.UPDATE)
    @PostMapping("/archive/{docId}")
    public AjaxResult archive(@PathVariable Long docId) {
        OaDocumentSend doc = oaDocumentSendService.getById(docId);
        if (doc == null) return error("未找到发文");
        doc.setStatus("3"); // 3=已归档
        doc.setUpdateBy(getUsername());
        doc.setUpdateTime(new Date());
        oaDocumentSendService.updateById(doc);
        return success("归档成功");
    }

    /**
     * 合并导出发文PDF（审批单+红头文件+底稿+附件）
     */
    @PostMapping("/exportMergedPdf/{docId}")
    public void exportMergedPdf(@PathVariable Long docId, HttpServletResponse response) {
        try {
            // 查询发文信息
            OaDocumentSend sendDoc = oaDocumentSendService.getById(docId);
            if (sendDoc == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 检查发文状态，只有审批完成的发文才能合并导出
            if (!"2".equals(sendDoc.getStatus())) { // 2=已审批
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("只有审批完成的发文才能合并导出");
                return;
            }

            // 查询附件列表（简化实现）
            List<OaFileAttachment> attachments = new java.util.ArrayList<>();
            try {
                attachments = fileAttachmentService.selectByDocumentId(docId);
            } catch (Exception e) {
                logger.warn("查询附件失败，使用空列表: " + e.getMessage());
            }

            // 生成增强的合并PDF（包含真实附件内容）
            byte[] pdfBytes = enhancedPdfMergeService.generateCompleteDocumentPdf(sendDoc, attachments);

            // 设置响应头为PDF格式
            response.setContentType("application/pdf");
            response.setContentLength(pdfBytes.length);
            response.setHeader("Content-Disposition",
                "attachment; filename=" + java.net.URLEncoder.encode(
                    sendDoc.getDocTitle() + "_合并文件.pdf", "UTF-8"));

            // 输出HTML内容
            response.getOutputStream().write(pdfBytes);
            response.getOutputStream().flush();

        } catch (Exception e) {
            logger.error("合并导出PDF失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("合并导出失败: " + e.getMessage());
            } catch (IOException ioException) {
                logger.error("写入错误响应失败", ioException);
            }
        }
    }

    /**
     * 备用方案：生成简单HTML
     */
    private byte[] generateFallbackHtml(OaDocumentSend sendDoc) throws Exception {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head><meta charset='UTF-8'>");
        html.append("<title>").append(sendDoc.getDocTitle() != null ? sendDoc.getDocTitle() : "发文文档").append("</title>");
        html.append("<style>body { font-family: SimSun, serif; margin: 40px; }</style>");
        html.append("</head><body>");
        html.append("<h1 style='text-align: center; color: red;'>红河哈尼族彝族自治州住房和城乡建设局</h1>");
        if (sendDoc.getDocTitle() != null) {
            html.append("<h2 style='text-align: center;'>").append(sendDoc.getDocTitle()).append("</h2>");
        }
        if (sendDoc.getDocContent() != null) {
            html.append("<div>").append(sendDoc.getDocContent().replaceAll("<[^>]+>", "")).append("</div>");
        }
        html.append("</body></html>");
        return html.toString().getBytes("UTF-8");
    }

    /**
     * 备用方案：生成简单合并HTML
     */
    private byte[] generateFallbackMergedHtml(OaDocumentSend sendDoc, List<OaFileAttachment> attachments) throws Exception {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>");
        html.append("<html><head><meta charset='UTF-8'>");
        html.append("<title>合并文档</title>");
        html.append("<style>body { font-family: SimSun, serif; margin: 40px; }</style>");
        html.append("</head><body>");
        html.append("<h1>发文合并文档</h1>");
        html.append("<h2>1. 审批单</h2>");
        html.append("<p>文件标题：").append(sendDoc.getDocTitle() != null ? sendDoc.getDocTitle() : "").append("</p>");
        html.append("<h2>2. 红头文件</h2>");
        html.append("<p>（红头文件内容）</p>");
        html.append("<h2>3. 底稿</h2>");
        if (sendDoc.getDocContent() != null) {
            html.append("<div>").append(sendDoc.getDocContent().replaceAll("<[^>]+>", "")).append("</div>");
        }
        html.append("<h2>4. 附件</h2>");
        html.append("<p>附件数量：").append(attachments != null ? attachments.size() : 0).append("</p>");
        html.append("</body></html>");
        return html.toString().getBytes("UTF-8");
    }
}
