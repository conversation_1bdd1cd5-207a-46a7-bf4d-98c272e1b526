package com.base.web.controller.monitor;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.constant.CacheConstants;
import com.base.common.core.domain.AjaxResult;
import com.base.common.utils.StringUtils;
import com.base.system.domain.SysCache;
import com.base.common.core.cache.MemoryCache;

/**
 * 缓存监控 - 内存缓存
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/cache")
public class CacheController
{
    @Autowired
    private MemoryCache memoryCache;

    private final static List<SysCache> caches = new ArrayList<SysCache>();
    {
        caches.add(new SysCache(CacheConstants.LOGIN_TOKEN_KEY, "用户信息"));
        caches.add(new SysCache(CacheConstants.SYS_CONFIG_KEY, "配置信息"));
        caches.add(new SysCache(CacheConstants.SYS_DICT_KEY, "数据字典"));
        caches.add(new SysCache(CacheConstants.CAPTCHA_CODE_KEY, "验证码"));
        caches.add(new SysCache(CacheConstants.REPEAT_SUBMIT_KEY, "防重提交"));
        caches.add(new SysCache(CacheConstants.RATE_LIMIT_KEY, "限流处理"));
        caches.add(new SysCache(CacheConstants.PWD_ERR_CNT_KEY, "密码错误次数"));
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping()
    public AjaxResult getInfo() throws Exception
    {
        Map<String, Object> info = new HashMap<>();
        info.put("cache_type", "Memory Cache");
        info.put("cache_size", memoryCache.size());
        info.put("used_memory", Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory());
        info.put("max_memory", Runtime.getRuntime().maxMemory());
        info.put("free_memory", Runtime.getRuntime().freeMemory());

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", memoryCache.size());

        List<Map<String, String>> pieList = new ArrayList<>();
        Map<String, String> data = new HashMap<>(2);
        data.put("name", "memory_cache");
        data.put("value", String.valueOf(memoryCache.size()));
        pieList.add(data);
        result.put("commandStats", pieList);
        return AjaxResult.success(result);
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getNames")
    public AjaxResult cache()
    {
        return AjaxResult.success(caches);
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getKeys/{cacheName}")
    public AjaxResult getCacheKeys(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = memoryCache.keys(cacheName + "*");
        return AjaxResult.success(new TreeSet<>(cacheKeys));
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    public AjaxResult getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
        Object cacheValue = memoryCache.get(cacheKey);
        String valueStr = cacheValue != null ? cacheValue.toString() : null;
        SysCache sysCache = new SysCache(cacheName, cacheKey, valueStr);
        return AjaxResult.success(sysCache);
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheName/{cacheName}")
    public AjaxResult clearCacheName(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = memoryCache.keys(cacheName + "*");
        for (String key : cacheKeys) {
            memoryCache.delete(key);
        }
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheKey/{cacheKey}")
    public AjaxResult clearCacheKey(@PathVariable String cacheKey)
    {
        memoryCache.delete(cacheKey);
        return AjaxResult.success();
    }

    @PreAuthorize("@ss.hasPermi('monitor:cache:list')")
    @DeleteMapping("/clearCacheAll")
    public AjaxResult clearCacheAll()
    {
        memoryCache.clear();
        return AjaxResult.success();
    }
}
