<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.meeting.mapper.OaMeetingMapper">
    
    <resultMap type="OaMeetingRoom" id="OaMeetingRoomResult">
        <result property="roomId"    column="room_id"    />
        <result property="roomName"    column="room_name"    />
        <result property="capacity"    column="capacity"    />
        <result property="floor"    column="floor"    />
        <result property="location"    column="location"    />
        <result property="equipment"    column="equipment"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaMeetingRoomVo">
        select room_id, room_name, capacity, floor, location, equipment, description, status, create_by, create_time, update_by, update_time, remark from oa_meeting_room
    </sql>

    <select id="selectMeetingRoomList" parameterType="OaMeetingRoom" resultMap="OaMeetingRoomResult">
        <include refid="selectOaMeetingRoomVo"/>
        <where>  
            <if test="roomName != null  and roomName != ''"> and room_name like concat('%', #{roomName}, '%')</if>
            <if test="capacity != null "> and capacity = #{capacity}</if>
            <if test="floor != null  and floor != ''"> and floor = #{floor}</if>
            <if test="location != null  and location != ''"> and location like concat('%', #{location}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaMeetingRoomByRoomId" parameterType="Long" resultMap="OaMeetingRoomResult">
        <include refid="selectOaMeetingRoomVo"/>
        where room_id = #{roomId}
    </select>
        
    <insert id="insertOaMeetingRoom" parameterType="OaMeetingRoom" useGeneratedKeys="true" keyProperty="roomId">
        insert into oa_meeting_room
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name,</if>
            <if test="capacity != null">capacity,</if>
            <if test="floor != null">floor,</if>
            <if test="location != null">location,</if>
            <if test="equipment != null">equipment,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">#{roomName},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="floor != null">#{floor},</if>
            <if test="location != null">#{location},</if>
            <if test="equipment != null">#{equipment},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaMeetingRoom" parameterType="OaMeetingRoom">
        update oa_meeting_room
        <trim prefix="SET" suffixOverrides=",">
            <if test="roomName != null and roomName != ''">room_name = #{roomName},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="floor != null">floor = #{floor},</if>
            <if test="location != null">location = #{location},</if>
            <if test="equipment != null">equipment = #{equipment},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where room_id = #{roomId}
    </update>

    <delete id="deleteOaMeetingRoomByRoomId" parameterType="Long">
        delete from oa_meeting_room where room_id = #{roomId}
    </delete>

    <delete id="deleteOaMeetingRoomByRoomIds" parameterType="String">
        delete from oa_meeting_room where room_id in 
        <foreach item="roomId" collection="array" open="(" separator="," close=")">
            #{roomId}
        </foreach>
    </delete>
</mapper>
