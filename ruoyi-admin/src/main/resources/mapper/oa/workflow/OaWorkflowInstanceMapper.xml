<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.workflow.mapper.OaWorkflowInstanceMapper">
    
    <resultMap type="OaWorkflowInstance" id="OaWorkflowInstanceResult">
        <result property="instanceId"    column="instance_id"    />
        <result property="processInstanceId"    column="process_instance_id"    />
        <result property="definitionId"    column="definition_id"    />
        <result property="definitionKey"    column="definition_key"    />
        <result property="businessKey"    column="business_key"    />
        <result property="title"    column="title"    />
        <result property="status"    column="status"    />
        <result property="startUserId"    column="start_user_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="duration"    column="duration"    />
        <result property="currentTaskId"    column="current_task_id"    />
        <result property="currentTaskName"    column="current_task_name"    />
        <result property="currentAssignee"    column="current_assignee"    />
        <result property="formData"    column="form_data"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaWorkflowInstanceVo">
        select instance_id, process_instance_id, definition_id, definition_key, business_key, title, status, start_user_id, start_time, end_time, duration, current_task_id, current_task_name, current_assignee, form_data, create_by, create_time, update_by, update_time, remark from oa_workflow_instance
    </sql>

    <select id="selectOaWorkflowInstanceList" parameterType="OaWorkflowInstance" resultMap="OaWorkflowInstanceResult">
        <include refid="selectOaWorkflowInstanceVo"/>
        <where>
            <if test="processInstanceId != null  and processInstanceId != ''"> and process_instance_id like concat('%', #{processInstanceId}, '%')</if>
            <if test="definitionKey != null  and definitionKey != ''"> and definition_key = #{definitionKey}</if>
            <if test="businessKey != null  and businessKey != ''"> and business_key = #{businessKey}</if>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startUserId != null "> and start_user_id = #{startUserId}</if>
            <if test="currentAssignee != null  and currentAssignee != ''"> and current_assignee = #{currentAssignee}</if>
            <!-- 支持流程名称和发起人姓名的模糊查询，这些字段在enrichInstanceInfoBatch中设置 -->
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaWorkflowInstanceByInstanceId" parameterType="Long" resultMap="OaWorkflowInstanceResult">
        <include refid="selectOaWorkflowInstanceVo"/>
        where instance_id = #{instanceId}
    </select>
    
    <select id="selectOaWorkflowInstanceByBusinessKey" parameterType="String" resultMap="OaWorkflowInstanceResult">
        <include refid="selectOaWorkflowInstanceVo"/>
        where business_key = #{businessKey}
    </select>

    <select id="selectOaWorkflowInstanceByProcessInstanceId" parameterType="String" resultMap="OaWorkflowInstanceResult">
        <include refid="selectOaWorkflowInstanceVo"/>
        where process_instance_id = #{processInstanceId}
    </select>

    <select id="selectOaWorkflowInstanceByStartUserId" parameterType="Long" resultMap="OaWorkflowInstanceResult">
        <include refid="selectOaWorkflowInstanceVo"/>
        where start_user_id = #{startUserId}
        order by create_time desc
    </select>
        
    <insert id="insertOaWorkflowInstance" parameterType="OaWorkflowInstance" useGeneratedKeys="true" keyProperty="instanceId">
        insert into oa_workflow_instance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processInstanceId != null and processInstanceId != ''">process_instance_id,</if>
            <if test="definitionId != null">definition_id,</if>
            <if test="definitionKey != null and definitionKey != ''">definition_key,</if>
            <if test="businessKey != null">business_key,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="startUserId != null">start_user_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="duration != null">duration,</if>
            <if test="currentTaskId != null">current_task_id,</if>
            <if test="currentTaskName != null">current_task_name,</if>
            <if test="currentAssignee != null">current_assignee,</if>
            <if test="formData != null">form_data,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processInstanceId != null and processInstanceId != ''">#{processInstanceId},</if>
            <if test="definitionId != null">#{definitionId},</if>
            <if test="definitionKey != null and definitionKey != ''">#{definitionKey},</if>
            <if test="businessKey != null">#{businessKey},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="startUserId != null">#{startUserId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="currentTaskId != null">#{currentTaskId},</if>
            <if test="currentTaskName != null">#{currentTaskName},</if>
            <if test="currentAssignee != null">#{currentAssignee},</if>
            <if test="formData != null">#{formData},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaWorkflowInstance" parameterType="OaWorkflowInstance">
        update oa_workflow_instance
        <trim prefix="SET" suffixOverrides=",">
            <if test="processInstanceId != null and processInstanceId != ''">process_instance_id = #{processInstanceId},</if>
            <if test="definitionId != null">definition_id = #{definitionId},</if>
            <if test="definitionKey != null and definitionKey != ''">definition_key = #{definitionKey},</if>
            <if test="businessKey != null">business_key = #{businessKey},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="startUserId != null">start_user_id = #{startUserId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="currentTaskId != null">current_task_id = #{currentTaskId},</if>
            <if test="currentTaskName != null">current_task_name = #{currentTaskName},</if>
            <if test="currentAssignee != null">current_assignee = #{currentAssignee},</if>
            <if test="formData != null">form_data = #{formData},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where instance_id = #{instanceId}
    </update>

    <delete id="deleteOaWorkflowInstanceByInstanceId" parameterType="Long">
        delete from oa_workflow_instance where instance_id = #{instanceId}
    </delete>

    <delete id="deleteOaWorkflowInstanceByInstanceIds" parameterType="String">
        delete from oa_workflow_instance where instance_id in 
        <foreach item="instanceId" collection="array" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </delete>

</mapper>
