<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.workflow.mapper.OaWorkflowDefinitionMapper">
    
    <resultMap type="OaWorkflowDefinition" id="OaWorkflowDefinitionResult">
        <result property="workflowId"    column="workflow_id"    />
        <result property="workflowName"    column="workflow_name"    />
        <result property="workflowKey"    column="workflow_key"    />
        <result property="workflowVersion"    column="workflow_version"    />
        <result property="description"    column="description"    />
        <result property="bpmnXml"    column="bpmn_xml"    />
        <result property="deploymentId"    column="deployment_id"    />
        <result property="processDefinitionId"    column="process_definition_id"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaWorkflowDefinitionVo">
        select workflow_id, workflow_name, workflow_key, workflow_version, description, bpmn_xml, deployment_id, process_definition_id, status, create_by, create_time, update_by, update_time, remark from oa_workflow_definition
    </sql>

    <select id="selectOaWorkflowDefinitionList" parameterType="OaWorkflowDefinition" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        <where>
            <if test="workflowKey != null  and workflowKey != ''"> and workflow_key = #{workflowKey}</if>
            <if test="workflowName != null  and workflowName != ''"> and workflow_name like concat('%', #{workflowName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectOaWorkflowDefinitionByWorkflowId" parameterType="Long" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        where workflow_id = #{workflowId}
    </select>

    <select id="selectOaWorkflowDefinitionByKey" parameterType="String" resultMap="OaWorkflowDefinitionResult">
        <include refid="selectOaWorkflowDefinitionVo"/>
        where workflow_key = #{workflowKey}
    </select>

    <insert id="insertOaWorkflowDefinition" parameterType="OaWorkflowDefinition" useGeneratedKeys="true" keyProperty="workflowId">
        insert into oa_workflow_definition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <!-- 必填字段，始终包含 -->
            workflow_name,
            workflow_key,
            <!-- 可选字段，根据条件包含 -->
            <if test="workflowVersion != null">workflow_version,</if>
            <if test="description != null">description,</if>
            <if test="bpmnXml != null">bpmn_xml,</if>
            <if test="deploymentId != null and deploymentId != ''">deployment_id,</if>
            <if test="processDefinitionId != null and processDefinitionId != ''">process_definition_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <!-- 必填字段，始终包含，如果为空则使用默认值 -->
            #{workflowName},
            #{workflowKey},
            <!-- 可选字段，根据条件包含 -->
            <if test="workflowVersion != null">#{workflowVersion},</if>
            <if test="description != null">#{description},</if>
            <if test="bpmnXml != null">#{bpmnXml},</if>
            <if test="deploymentId != null and deploymentId != ''">#{deploymentId},</if>
            <if test="processDefinitionId != null and processDefinitionId != ''">#{processDefinitionId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaWorkflowDefinition" parameterType="OaWorkflowDefinition">
        update oa_workflow_definition
        <trim prefix="SET" suffixOverrides=",">
            <if test="workflowName != null and workflowName != ''">workflow_name = #{workflowName},</if>
            <if test="workflowKey != null and workflowKey != ''">workflow_key = #{workflowKey},</if>
            <if test="workflowVersion != null">workflow_version = #{workflowVersion},</if>
            <if test="description != null">description = #{description},</if>
            <if test="bpmnXml != null">bpmn_xml = #{bpmnXml},</if>
            <if test="deploymentId != null and deploymentId != ''">deployment_id = #{deploymentId},</if>
            <if test="processDefinitionId != null and processDefinitionId != ''">process_definition_id = #{processDefinitionId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where workflow_id = #{workflowId}
    </update>

    <delete id="deleteOaWorkflowDefinitionByWorkflowId" parameterType="Long">
        delete from oa_workflow_definition where workflow_id = #{workflowId}
    </delete>

    <delete id="deleteOaWorkflowDefinitionByWorkflowIds" parameterType="String">
        delete from oa_workflow_definition where workflow_id in
        <foreach item="workflowId" collection="array" open="(" separator="," close=")">
            #{workflowId}
        </foreach>
    </delete>

</mapper>
