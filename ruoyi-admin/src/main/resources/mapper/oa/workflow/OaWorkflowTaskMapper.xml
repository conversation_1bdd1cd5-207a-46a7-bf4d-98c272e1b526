<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.workflow.mapper.OaWorkflowTaskMapper">
    
    <resultMap type="OaWorkflowTask" id="OaWorkflowTaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="flowableTaskId"    column="flowable_task_id"    />
        <result property="instanceId"    column="instance_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="taskKey"    column="task_key"    />
        <result property="assigneeId"    column="assignee_id"    />
        <result property="assigneeName"    column="assignee_name"    />
        <result property="status"    column="status"    />
        <result property="priority"    column="priority"    />
        <result property="dueDate"    column="due_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="completeTime"    column="complete_time"    />
        <result property="comment"    column="comment"    />
    </resultMap>

    <sql id="selectOaWorkflowTaskVo">
        select task_id, flowable_task_id, instance_id, task_name, task_key, assignee_id, assignee_name, status, priority, due_date, create_time, complete_time, comment from oa_workflow_task
    </sql>

    <select id="selectOaWorkflowTaskList" parameterType="OaWorkflowTask" resultMap="OaWorkflowTaskResult">
        <include refid="selectOaWorkflowTaskVo"/>
        <where>  
            <if test="flowableTaskId != null  and flowableTaskId != ''"> and flowable_task_id = #{flowableTaskId}</if>
            <if test="instanceId != null "> and instance_id = #{instanceId}</if>
            <if test="processInstanceId != null  and processInstanceId != ''"> and process_instance_id = #{processInstanceId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="taskKey != null  and taskKey != ''"> and task_key = #{taskKey}</if>
            <if test="assignee != null  and assignee != ''"> and assignee = #{assignee}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="priority != null "> and priority = #{priority}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaWorkflowTaskByTaskId" parameterType="Long" resultMap="OaWorkflowTaskResult">
        <include refid="selectOaWorkflowTaskVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectOaWorkflowTaskByFlowableTaskId" parameterType="String" resultMap="OaWorkflowTaskResult">
        <include refid="selectOaWorkflowTaskVo"/>
        where flowable_task_id = #{flowableTaskId}
    </select>


    <insert id="insertOaWorkflowTask" parameterType="OaWorkflowTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into oa_workflow_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">flowable_task_id,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="taskKey != null and taskKey != ''">task_key,</if>
            <if test="assigneeId != null">assignee_id,</if>
            <if test="assigneeName != null and assigneeName != ''">assignee_name,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="priority != null">priority,</if>
            <if test="dueDate != null">due_date,</if>
            <if test="comment != null">comment,</if>
            <if test="createTime != null">create_time,</if>
            <if test="completeTime != null">complete_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">#{flowableTaskId},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="taskKey != null and taskKey != ''">#{taskKey},</if>
            <if test="assigneeId != null">#{assigneeId},</if>
            <if test="assigneeName != null and assigneeName != ''">#{assigneeName},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="priority != null">#{priority},</if>
            <if test="dueDate != null">#{dueDate},</if>
            <if test="comment != null">#{comment},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="completeTime != null">#{completeTime},</if>
         </trim>
    </insert>

    <update id="updateOaWorkflowTask" parameterType="OaWorkflowTask">
        update oa_workflow_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="flowableTaskId != null and flowableTaskId != ''">flowable_task_id = #{flowableTaskId},</if>
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="taskKey != null and taskKey != ''">task_key = #{taskKey},</if>
            <if test="assigneeId != null">assignee_id = #{assigneeId},</if>
            <if test="assigneeName != null and assigneeName != ''">assignee_name = #{assigneeName},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="dueDate != null">due_date = #{dueDate},</if>
            <if test="comment != null">comment = #{comment},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="completeTime != null">complete_time = #{completeTime},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteOaWorkflowTaskByTaskId" parameterType="Long">
        delete from oa_workflow_task where task_id = #{taskId}
    </delete>

    <delete id="deleteOaWorkflowTaskByTaskIds" parameterType="Long">
        delete from oa_workflow_task where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

</mapper>
