<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.oa.document.mapper.OaDocumentReceiveMapper">
    
    <resultMap type="OaDocumentReceive" id="OaDocumentReceiveResult">
        <result property="docId"    column="doc_id"    />
        <result property="docNumber"    column="doc_number"    />
        <result property="docTitle"    column="doc_title"    />

        <result property="sourceUnit"    column="source_unit"    />
        <result property="senderUnit"    column="sender_unit"    />
        <result property="senderContact"    column="sender_contact"    />
        <result property="receiveDate"    column="receive_date"    />
        <result property="urgencyLevel"    column="urgency_level"    />
        <result property="securityLevel"    column="security_level"    />
        <result property="secretLevel"    column="secret_level"    />
        <result property="docContent"    column="doc_content"    />
        <result property="attachments"    column="attachments"    />
        <result property="attachmentPath"    column="attachment_path"    />
        <result property="registrarId"    column="registrar_id"    />
        <result property="registrarName"    column="registrar_name"    />
        <result property="registerTime"    column="register_time"    />
        <result property="handlerId"    column="handler_id"    />
        <result property="handlerName"    column="handler_name"    />
        <result property="handleStatus"    column="handle_status"    />
        <result property="handleOpinion"    column="handle_opinion"    />
        <result property="status"    column="status"    />
        <result property="workflowInstanceId"    column="workflow_instance_id"    />
        <result property="currentStep"    column="current_step"    />
        <result property="currentAssignee"    column="current_assignee"    />
        <result property="completionDeadline"    column="completion_deadline"    />
        <result property="actualCompletionTime"    column="actual_completion_time"    />
        <result property="isOverdue"    column="is_overdue"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="retentionPeriod"    column="retention_period"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOaDocumentReceiveVo">
        select doc_id, doc_number, doc_title, source_unit, sender_unit, sender_contact, receive_date, urgency_level, security_level, secret_level, doc_content, attachments, attachment_path, registrar_id, registrar_name, register_time, handler_id, handler_name, handle_status, handle_opinion, status, workflow_instance_id, current_step, current_assignee, completion_deadline, actual_completion_time, is_overdue, overdue_days, retention_period, create_by, create_time, update_by, update_time, remark from oa_document_receive
    </sql>

    <select id="selectOaDocumentReceiveList" parameterType="OaDocumentReceive" resultMap="OaDocumentReceiveResult">
        <include refid="selectOaDocumentReceiveVo"/>
        <where>  
            <if test="docNumber != null  and docNumber != ''"> and doc_number like concat('%', #{docNumber}, '%')</if>
            <if test="docTitle != null  and docTitle != ''"> and doc_title like concat('%', #{docTitle}, '%')</if>

            <if test="senderUnit != null  and senderUnit != ''"> and sender_unit like concat('%', #{senderUnit}, '%')</if>
            <if test="receiveDate != null "> and receive_date = #{receiveDate}</if>
            <if test="urgencyLevel != null  and urgencyLevel != ''"> and urgency_level = #{urgencyLevel}</if>
            <if test="securityLevel != null  and securityLevel != ''"> and security_level = #{securityLevel}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="currentAssignee != null  and currentAssignee != ''"> and current_assignee = #{currentAssignee}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOaDocumentReceiveByDocId" parameterType="Long" resultMap="OaDocumentReceiveResult">
        <include refid="selectOaDocumentReceiveVo"/>
        where doc_id = #{docId}
    </select>
        
    <insert id="insertOaDocumentReceive" parameterType="OaDocumentReceive" useGeneratedKeys="true" keyProperty="docId">
        insert into oa_document_receive
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">doc_number,</if>
            <if test="docTitle != null and docTitle != ''">doc_title,</if>

            <if test="sourceUnit != null">source_unit,</if>
            <if test="senderUnit != null">sender_unit,</if>
            <if test="senderContact != null">sender_contact,</if>
            <if test="receiveDate != null">receive_date,</if>
            <if test="urgencyLevel != null">urgency_level,</if>
            <if test="securityLevel != null">security_level,</if>
            <if test="secretLevel != null">secret_level,</if>
            <if test="docContent != null">doc_content,</if>
            <if test="attachments != null">attachments,</if>
            <if test="attachmentPath != null">attachment_path,</if>
            <if test="registrarId != null">registrar_id,</if>
            <if test="registrarName != null">registrar_name,</if>
            <if test="registerTime != null">register_time,</if>
            <if test="handlerId != null">handler_id,</if>
            <if test="handlerName != null">handler_name,</if>
            <if test="handleStatus != null">handle_status,</if>
            <if test="handleOpinion != null">handle_opinion,</if>
            <if test="status != null">status,</if>
            <if test="workflowInstanceId != null">workflow_instance_id,</if>
            <if test="currentStep != null">current_step,</if>
            <if test="currentAssignee != null">current_assignee,</if>
            <if test="completionDeadline != null">completion_deadline,</if>
            <if test="actualCompletionTime != null">actual_completion_time,</if>
            <if test="isOverdue != null">is_overdue,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="retentionPeriod != null">retention_period,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">#{docNumber},</if>
            <if test="docTitle != null and docTitle != ''">#{docTitle},</if>
            <if test="docType != null">#{docType},</if>
            <if test="sourceUnit != null">#{sourceUnit},</if>
            <if test="senderUnit != null">#{senderUnit},</if>
            <if test="senderContact != null">#{senderContact},</if>
            <if test="receiveDate != null">#{receiveDate},</if>
            <if test="urgencyLevel != null">#{urgencyLevel},</if>
            <if test="securityLevel != null">#{securityLevel},</if>
            <if test="secretLevel != null">#{secretLevel},</if>
            <if test="docContent != null">#{docContent},</if>
            <if test="attachments != null">#{attachments},</if>
            <if test="attachmentPath != null">#{attachmentPath},</if>
            <if test="registrarId != null">#{registrarId},</if>
            <if test="registrarName != null">#{registrarName},</if>
            <if test="registerTime != null">#{registerTime},</if>
            <if test="handlerId != null">#{handlerId},</if>
            <if test="handlerName != null">#{handlerName},</if>
            <if test="handleStatus != null">#{handleStatus},</if>
            <if test="handleOpinion != null">#{handleOpinion},</if>
            <if test="status != null">#{status},</if>
            <if test="workflowInstanceId != null">#{workflowInstanceId},</if>
            <if test="currentStep != null">#{currentStep},</if>
            <if test="currentAssignee != null">#{currentAssignee},</if>
            <if test="completionDeadline != null">#{completionDeadline},</if>
            <if test="actualCompletionTime != null">#{actualCompletionTime},</if>
            <if test="isOverdue != null">#{isOverdue},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="retentionPeriod != null">#{retentionPeriod},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOaDocumentReceive" parameterType="OaDocumentReceive">
        update oa_document_receive
        <trim prefix="SET" suffixOverrides=",">
            <if test="docNumber != null and docNumber != ''">doc_number = #{docNumber},</if>
            <if test="docTitle != null and docTitle != ''">doc_title = #{docTitle},</if>

            <if test="sourceUnit != null">source_unit = #{sourceUnit},</if>
            <if test="senderUnit != null">sender_unit = #{senderUnit},</if>
            <if test="senderContact != null">sender_contact = #{senderContact},</if>
            <if test="receiveDate != null">receive_date = #{receiveDate},</if>
            <if test="urgencyLevel != null">urgency_level = #{urgencyLevel},</if>
            <if test="securityLevel != null">security_level = #{securityLevel},</if>
            <if test="secretLevel != null">secret_level = #{secretLevel},</if>
            <if test="docContent != null">doc_content = #{docContent},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="attachmentPath != null">attachment_path = #{attachmentPath},</if>
            <if test="registrarId != null">registrar_id = #{registrarId},</if>
            <if test="registrarName != null">registrar_name = #{registrarName},</if>
            <if test="registerTime != null">register_time = #{registerTime},</if>
            <if test="handlerId != null">handler_id = #{handlerId},</if>
            <if test="handlerName != null">handler_name = #{handlerName},</if>
            <if test="handleStatus != null">handle_status = #{handleStatus},</if>
            <if test="handleOpinion != null">handle_opinion = #{handleOpinion},</if>
            <if test="status != null">status = #{status},</if>
            <if test="workflowInstanceId != null">workflow_instance_id = #{workflowInstanceId},</if>
            <if test="currentStep != null">current_step = #{currentStep},</if>
            <if test="currentAssignee != null">current_assignee = #{currentAssignee},</if>
            <if test="completionDeadline != null">completion_deadline = #{completionDeadline},</if>
            <if test="actualCompletionTime != null">actual_completion_time = #{actualCompletionTime},</if>
            <if test="isOverdue != null">is_overdue = #{isOverdue},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="retentionPeriod != null">retention_period = #{retentionPeriod},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where doc_id = #{docId}
    </update>

    <delete id="deleteOaDocumentReceiveByDocId" parameterType="Long">
        delete from oa_document_receive where doc_id = #{docId}
    </delete>

    <delete id="deleteOaDocumentReceiveByDocIds" parameterType="String">
        delete from oa_document_receive where doc_id in 
        <foreach item="docId" collection="array" open="(" separator="," close=")">
            #{docId}
        </foreach>
    </delete>

    <select id="getListByCondition" resultType="com.base.oa.document.domain.OaDocumentReceive">
        select * from oa_document_receive
        <where>
            <if test="docId != null"> and doc_id = #{docId}</if>
            <if test="docTitle != null and docTitle != ''"> and doc_title like concat('%', #{docTitle}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
            <if test="params.userId != null and params.userId != ''">
                and (create_by = #{params.userId} or current_assignee = #{params.userId})
            </if>
        </where>
        order by create_time desc
    </select>

</mapper>
