<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:flowable="http://flowable.org/bpmn"
                  targetNamespace="http://www.flowable.org/processdef">

    <bpmn:process id="document_receive_approval_v2" name="收文审批流程V2" isExecutable="true">
        <bpmn:documentation>收文审批流程 - 支持并行分支和动态人员选择</bpmn:documentation>

        <!-- 开始事件 -->
        <bpmn:startEvent id="startEvent" name="开始">
            <bpmn:outgoing>flow1</bpmn:outgoing>
        </bpmn:startEvent>

        <!-- 办公室收文登记 -->
        <bpmn:userTask id="officeReceive" name="办公室收文登记" flowable:candidateGroups="clerk">
            <bpmn:documentation>办公室人员进行收文登记</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${officeReceiveTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow1</bpmn:incoming>
            <bpmn:outgoing>flow2</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 办公室主任审批 -->
        <bpmn:userTask id="officeManagerApproval" name="办公室主任审批" flowable:candidateGroups="role_ksfzr">
            <bpmn:documentation>办公室主任审批收文</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${autoAssignTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow2</bpmn:incoming>
            <bpmn:outgoing>flow3</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 书记审批（选择分管领导） -->
        <bpmn:userTask id="secretaryApproval" name="书记审批" flowable:candidateGroups="role_sj">
            <bpmn:documentation>书记审批收文并选择分管领导</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${autoAssignTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow3</bpmn:incoming>
            <bpmn:outgoing>flow4</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 分管领导审批（多实例并行任务） -->
        <bpmn:userTask id="leaderApproval" name="分管领导审批" flowable:candidateGroups="dynamic_leaders">
            <bpmn:documentation>分管领导审批收文并选择科室负责人</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${dynamicLeaderTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow4</bpmn:incoming>
            <bpmn:outgoing>flow5</bpmn:outgoing>
            <!-- 多实例配置：为每个选中的分管领导创建一个任务实例 -->
            <bpmn:multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${selectedLeadersList}" flowable:elementVariable="currentLeader">
                <bpmn:completionCondition>${nrOfCompletedInstances == nrOfInstances}</bpmn:completionCondition>
            </bpmn:multiInstanceLoopCharacteristics>
        </bpmn:userTask>

        <!-- 科室负责人审批（选择经办人） -->
        <bpmn:userTask id="deptManagerApproval" name="科室负责人审批" flowable:candidateGroups="dynamic_managers">
            <bpmn:documentation>科室负责人审批收文并选择经办人</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${dynamicManagerTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow5</bpmn:incoming>
            <bpmn:outgoing>flow6</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 经办人查阅 -->
        <bpmn:userTask id="handlerReview" name="经办人查阅" flowable:candidateGroups="dynamic_handlers">
            <bpmn:documentation>经办人查阅收文信息</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${dynamicHandlerTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow6</bpmn:incoming>
            <bpmn:outgoing>flow7</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 经办人确认 -->
        <bpmn:userTask id="handlerConfirm" name="经办人确认" flowable:candidateGroups="dynamic_handlers">
            <bpmn:documentation>经办人确认收文处理完成</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${dynamicHandlerTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow7</bpmn:incoming>
            <bpmn:outgoing>flow8</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 办公室汇总 -->
        <bpmn:userTask id="officeSummary" name="办公室汇总" flowable:candidateGroups="clerk">
            <bpmn:documentation>办公室人员汇总收文处理结果</bpmn:documentation>
            <bpmn:extensionElements>
                <flowable:taskListener event="create" delegateExpression="${officeSummaryTaskListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow8</bpmn:incoming>
            <bpmn:outgoing>flow9</bpmn:outgoing>
        </bpmn:userTask>

        <!-- 结束事件 -->
        <bpmn:endEvent id="endEvent" name="结束">
            <bpmn:extensionElements>
                <flowable:executionListener event="end" delegateExpression="${processEndListener}"/>
            </bpmn:extensionElements>
            <bpmn:incoming>flow9</bpmn:incoming>
        </bpmn:endEvent>

        <!-- 连接线 -->
        <bpmn:sequenceFlow id="flow1" sourceRef="startEvent" targetRef="officeReceive"/>
        <bpmn:sequenceFlow id="flow2" sourceRef="officeReceive" targetRef="officeManagerApproval"/>
        <bpmn:sequenceFlow id="flow3" sourceRef="officeManagerApproval" targetRef="secretaryApproval"/>
        <bpmn:sequenceFlow id="flow4" sourceRef="secretaryApproval" targetRef="leaderApproval"/>
        <bpmn:sequenceFlow id="flow5" sourceRef="leaderApproval" targetRef="deptManagerApproval"/>
        <bpmn:sequenceFlow id="flow6" sourceRef="deptManagerApproval" targetRef="handlerReview"/>
        <bpmn:sequenceFlow id="flow7" sourceRef="handlerReview" targetRef="handlerConfirm"/>
        <bpmn:sequenceFlow id="flow8" sourceRef="handlerConfirm" targetRef="officeSummary"/>
        <bpmn:sequenceFlow id="flow9" sourceRef="officeSummary" targetRef="endEvent"/>
        
    </bpmn:process>

    <!-- 图形信息 -->
    <bpmndi:BPMNDiagram id="BPMNDiagram_document_receive_approval_v2">
        <bpmndi:BPMNPlane bpmnElement="document_receive_approval_v2" id="BPMNPlane_document_receive_approval_v2">

            <!-- 开始事件 -->
            <bpmndi:BPMNShape bpmnElement="startEvent" id="BPMNShape_startEvent">
                <omgdc:Bounds height="35.0" width="35.0" x="50.0" y="200.0"/>
            </bpmndi:BPMNShape>

            <!-- 办公室收文登记 -->
            <bpmndi:BPMNShape bpmnElement="officeReceive" id="BPMNShape_officeReceive">
                <omgdc:Bounds height="80.0" width="100.0" x="120.0" y="177.5"/>
            </bpmndi:BPMNShape>

            <!-- 办公室主任审批 -->
            <bpmndi:BPMNShape bpmnElement="officeManagerApproval" id="BPMNShape_officeManagerApproval">
                <omgdc:Bounds height="80.0" width="100.0" x="270.0" y="177.5"/>
            </bpmndi:BPMNShape>

            <!-- 书记审批 -->
            <bpmndi:BPMNShape bpmnElement="secretaryApproval" id="BPMNShape_secretaryApproval">
                <omgdc:Bounds height="80.0" width="100.0" x="420.0" y="177.5"/>
            </bpmndi:BPMNShape>

            <!-- 并行网关 -->
            <bpmndi:BPMNShape bpmnElement="parallelGateway" id="BPMNShape_parallelGateway">
                <omgdc:Bounds height="40.0" width="40.0" x="570.0" y="197.5"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导审批 -->
            <bpmndi:BPMNShape bpmnElement="leaderApproval" id="BPMNShape_leaderApproval">
                <omgdc:Bounds height="80.0" width="100.0" x="660.0" y="77.5"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人审批 -->
            <bpmndi:BPMNShape bpmnElement="deptManagerApproval" id="BPMNShape_deptManagerApproval">
                <omgdc:Bounds height="80.0" width="100.0" x="810.0" y="77.5"/>
            </bpmndi:BPMNShape>

            <!-- 经办人查阅 -->
            <bpmndi:BPMNShape bpmnElement="handlerReview" id="BPMNShape_handlerReview">
                <omgdc:Bounds height="80.0" width="100.0" x="960.0" y="77.5"/>
            </bpmndi:BPMNShape>

            <!-- 经办人确认 -->
            <bpmndi:BPMNShape bpmnElement="handlerConfirm" id="BPMNShape_handlerConfirm">
                <omgdc:Bounds height="80.0" width="100.0" x="1110.0" y="77.5"/>
            </bpmndi:BPMNShape>

            <!-- 汇聚网关 -->
            <bpmndi:BPMNShape bpmnElement="joinGateway" id="BPMNShape_joinGateway">
                <omgdc:Bounds height="40.0" width="40.0" x="1260.0" y="197.5"/>
            </bpmndi:BPMNShape>

            <!-- 办公室汇总 -->
            <bpmndi:BPMNShape bpmnElement="officeSummary" id="BPMNShape_officeSummary">
                <omgdc:Bounds height="80.0" width="100.0" x="1350.0" y="177.5"/>
            </bpmndi:BPMNShape>

            <!-- 结束事件 -->
            <bpmndi:BPMNShape bpmnElement="endEvent" id="BPMNShape_endEvent">
                <omgdc:Bounds height="35.0" width="35.0" x="1500.0" y="200.0"/>
            </bpmndi:BPMNShape>

            <!-- 连接线 -->
            <bpmndi:BPMNEdge bpmnElement="flow1" id="BPMNEdge_flow1">
                <omgdi:waypoint x="85.0" y="217.5"/>
                <omgdi:waypoint x="120.0" y="217.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow2" id="BPMNEdge_flow2">
                <omgdi:waypoint x="220.0" y="217.5"/>
                <omgdi:waypoint x="270.0" y="217.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow3" id="BPMNEdge_flow3">
                <omgdi:waypoint x="370.0" y="217.5"/>
                <omgdi:waypoint x="420.0" y="217.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow4" id="BPMNEdge_flow4">
                <omgdi:waypoint x="520.0" y="217.5"/>
                <omgdi:waypoint x="570.0" y="217.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow5" id="BPMNEdge_flow5">
                <omgdi:waypoint x="590.0" y="197.5"/>
                <omgdi:waypoint x="590.0" y="117.5"/>
                <omgdi:waypoint x="660.0" y="117.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow6" id="BPMNEdge_flow6">
                <omgdi:waypoint x="760.0" y="117.5"/>
                <omgdi:waypoint x="810.0" y="117.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow7" id="BPMNEdge_flow7">
                <omgdi:waypoint x="910.0" y="117.5"/>
                <omgdi:waypoint x="960.0" y="117.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow8" id="BPMNEdge_flow8">
                <omgdi:waypoint x="1060.0" y="117.5"/>
                <omgdi:waypoint x="1110.0" y="117.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow9" id="BPMNEdge_flow9">
                <omgdi:waypoint x="1210.0" y="117.5"/>
                <omgdi:waypoint x="1280.0" y="117.5"/>
                <omgdi:waypoint x="1280.0" y="197.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow10" id="BPMNEdge_flow10">
                <omgdi:waypoint x="1300.0" y="217.5"/>
                <omgdi:waypoint x="1350.0" y="217.5"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="flow11" id="BPMNEdge_flow11">
                <omgdi:waypoint x="1450.0" y="217.5"/>
                <omgdi:waypoint x="1500.0" y="217.5"/>
            </bpmndi:BPMNEdge>

        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>

</bpmn:definitions>
