const axios = require('axios');

async function testHandlersComplete() {
    try {
        console.log('🚀 开始完整测试经办人功能...\n');

        // 1. 登录
        console.log('🔐 正在登录admin用户...');
        const loginResponse = await axios.post('http://localhost:8080/login', {
            username: 'admin',
            password: 'admin123',
            code: '',
            uuid: ''
        });

        if (loginResponse.data.code !== 200) {
            throw new Error('登录失败');
        }

        const token = loginResponse.data.token;
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        console.log('✅ admin登录成功\n');

        // 2. 获取部门和用户数据
        console.log('📋 获取部门和用户数据...');
        const [deptResponse, userResponse] = await Promise.all([
            axios.get('http://localhost:8080/system/dept/list'),
            axios.get('http://localhost:8080/system/user/list')
        ]);

        const depts = deptResponse.data.data;
        const users = userResponse.data.rows;
        console.log(`✅ 获取到 ${depts.length} 个部门，${users.length} 个用户\n`);

        // 3. 分析部门负责人数据
        console.log('🔍 分析部门负责人数据...');
        const deptsWithLeaders = depts.filter(dept => dept.leader);
        console.log(`📊 有负责人的部门: ${deptsWithLeaders.length} 个`);

        for (const dept of deptsWithLeaders) {
            const leader = users.find(user => user.userId == dept.leader);
            if (leader) {
                console.log(`   ✅ ${dept.deptName} → ${leader.nickName} (${leader.userName})`);
            } else {
                console.log(`   ❌ ${dept.deptName} → 负责人ID ${dept.leader} 不存在`);
            }
        }
        console.log();

        // 4. 找一个有效的科长进行测试
        console.log('🎯 寻找有效的科长进行测试...');
        let testManager = null;
        let testDept = null;

        for (const dept of deptsWithLeaders) {
            const leader = users.find(user => user.userId == dept.leader);
            if (leader) {
                // 查看该部门下是否有其他人员
                const deptUsers = users.filter(user => user.deptId == dept.deptId && user.userId != leader.userId);
                if (deptUsers.length > 0) {
                    testManager = leader;
                    testDept = dept;
                    console.log(`✅ 找到测试科长: ${leader.nickName} (${leader.userName})`);
                    console.log(`   部门: ${dept.deptName}`);
                    console.log(`   部门下其他人员: ${deptUsers.length} 个`);
                    deptUsers.forEach(user => {
                        console.log(`     - ${user.nickName} (${user.userName})`);
                    });
                    break;
                }
            }
        }

        if (!testManager) {
            console.log('❌ 未找到合适的测试科长（需要有负责的部门且部门下有其他人员）');
            
            // 创建测试数据
            console.log('\n🔧 创建测试数据...');
            
            // 找到人事科
            const hrDept = depts.find(dept => dept.deptName.includes('人事'));
            if (hrDept) {
                // 创建人事科长用户
                console.log('创建人事科长用户...');
                const createUserResponse = await axios.post('http://localhost:8080/system/user', {
                    userName: 'manager_hr_test',
                    nickName: '人事科长测试',
                    email: '<EMAIL>',
                    phonenumber: '13800000001',
                    sex: '1',
                    status: '0',
                    deptId: hrDept.deptId,
                    postIds: [],
                    roleIds: [2] // 普通角色
                });

                if (createUserResponse.data.code === 200) {
                    const newUserId = createUserResponse.data.data;
                    console.log(`✅ 创建用户成功，ID: ${newUserId}`);

                    // 更新部门负责人
                    const updateDeptResponse = await axios.put('http://localhost:8080/system/dept', {
                        ...hrDept,
                        leader: newUserId.toString()
                    });

                    if (updateDeptResponse.data.code === 200) {
                        console.log('✅ 更新部门负责人成功');
                        testManager = {
                            userId: newUserId,
                            userName: 'manager_hr_test',
                            nickName: '人事科长测试'
                        };
                        testDept = hrDept;
                    }
                }
            }
        }

        if (!testManager) {
            console.log('❌ 无法创建测试数据，测试终止');
            return;
        }

        console.log();

        // 5. 测试经办人查询API
        console.log('🧪 测试经办人查询API...');
        console.log(`测试科长: ${testManager.nickName} (${testManager.userName})`);

        const handlersResponse = await axios.get(`http://localhost:8080/oa/workflow/personnel/handlers/${testManager.userName}`);
        
        if (handlersResponse.data.code === 200) {
            const handlers = handlersResponse.data.data;
            console.log(`✅ API调用成功，返回 ${handlers.length} 个经办人:`);
            
            if (handlers.length > 0) {
                handlers.forEach(handler => {
                    console.log(`   - ${handler.nickName} (${handler.userName}) - ${handler.deptName}`);
                    console.log(`     联系方式: ${handler.phonenumber || '无'}, 邮箱: ${handler.email || '无'}`);
                });
            } else {
                console.log('   (该科长负责的部门下暂无其他人员)');
            }
        } else {
            console.log(`❌ API调用失败: ${handlersResponse.data.msg}`);
        }

        console.log('\n🎉 测试完成！');
        console.log('\n📋 功能说明:');
        console.log('1. 经办人查询API: GET /oa/workflow/personnel/handlers/{managerId}');
        console.log('2. 根据科长用户名查询其负责部门下的所有人员（排除科长自己）');
        console.log('3. 返回经办人的详细信息，包括姓名、用户名、部门、联系方式等');
        console.log('4. 前端可以在科长审批时调用此API获取经办人列表供选择');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.response ? error.response.data : error.message);
    }
}

testHandlersComplete();
