const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:8080';

// 测试书记审批环节分管领导指派修复效果
async function testSecretaryApprovalFix() {
    console.log('🚀 开始测试书记审批环节分管领导指派修复效果...\n');

    try {
        // 1. 测试原有的 /system/user/listAll 接口（应该返回所有用户）
        console.log('🔍 测试原有的 /system/user/listAll 接口');
        const allUsersResponse = await axios.get(`${BASE_URL}/system/user/listAll`);
        
        if (allUsersResponse.data.code === 200) {
            const allUsers = allUsersResponse.data.data;
            console.log(`✅ /system/user/listAll 接口正常，返回 ${allUsers.length} 个用户`);
            console.log(`   前3个用户: ${allUsers.slice(0, 3).map(u => u.nickName).join(', ')}`);
        } else {
            console.log('❌ /system/user/listAll 接口异常:', allUsersResponse.data.msg);
        }

        console.log('');

        // 2. 测试新的工作流分管领导接口
        console.log('🔍 测试新的工作流分管领导接口');
        const leadersResponse = await axios.get(`${BASE_URL}/oa/workflow/personnel/leaders`);
        
        if (leadersResponse.data.code === 200) {
            const leaders = leadersResponse.data.data;
            console.log(`✅ 工作流分管领导接口正常，返回 ${leaders.length} 个分管领导`);
            console.log('   分管领导列表:');
            leaders.forEach(leader => {
                console.log(`     - ${leader.nickName} (${leader.userName}) - ${leader.deptName || '未分配部门'}`);
            });
            
            // 验证是否只包含分管领导
            const expectedLeaders = ['leader_admin', 'leader_finance', 'leader_operation'];
            const actualLeaders = leaders.map(l => l.userName);
            const isCorrect = expectedLeaders.every(expected => actualLeaders.includes(expected)) &&
                             actualLeaders.every(actual => expectedLeaders.includes(actual));
            
            if (isCorrect) {
                console.log('✅ 分管领导列表正确，只包含具有fgld角色的用户');
            } else {
                console.log('❌ 分管领导列表不正确');
                console.log(`   期望: ${expectedLeaders.join(', ')}`);
                console.log(`   实际: ${actualLeaders.join(', ')}`);
            }
        } else {
            console.log('❌ 工作流分管领导接口异常:', leadersResponse.data.msg);
        }

        console.log('');

        // 3. 对比两个接口的返回结果
        console.log('📊 对比分析:');
        if (allUsersResponse.data.code === 200 && leadersResponse.data.code === 200) {
            const allUsersCount = allUsersResponse.data.data.length;
            const leadersCount = leadersResponse.data.data.length;
            
            console.log(`   - 所有用户数量: ${allUsersCount}`);
            console.log(`   - 分管领导数量: ${leadersCount}`);
            console.log(`   - 过滤效果: 从 ${allUsersCount} 个用户筛选出 ${leadersCount} 个分管领导`);
            console.log(`   - 过滤比例: ${((leadersCount / allUsersCount) * 100).toFixed(1)}%`);
            
            if (leadersCount < allUsersCount && leadersCount === 3) {
                console.log('✅ 过滤效果正确，成功限制了分管领导候选人范围');
            } else {
                console.log('❌ 过滤效果异常');
            }
        }

        console.log('');

        // 4. 测试科室负责人关联查询
        console.log('🔍 测试科室负责人关联查询');
        if (leadersResponse.data.code === 200) {
            const leaders = leadersResponse.data.data;
            
            for (const leader of leaders) {
                console.log(`\n   测试分管领导: ${leader.nickName} (${leader.userName})`);
                
                try {
                    const managersResponse = await axios.get(`${BASE_URL}/oa/workflow/personnel/managers/${leader.userId}`);
                    
                    if (managersResponse.data.code === 200) {
                        const managers = managersResponse.data.data;
                        console.log(`   ✅ 获取到 ${managers.length} 个科室负责人:`);
                        managers.forEach(manager => {
                            console.log(`       - ${manager.nickName} (${manager.userName}) - ${manager.deptName}`);
                        });
                    } else {
                        console.log(`   ❌ 获取科室负责人失败: ${managersResponse.data.msg}`);
                    }
                } catch (error) {
                    console.log(`   ❌ 请求科室负责人接口失败: ${error.message}`);
                }
            }
        }

        console.log('\n============================================================');
        console.log('🎉 测试完成！');
        
        console.log('\n📋 修复效果总结:');
        console.log('1. ✅ 保持了原有 /system/user/listAll 接口的功能（返回所有用户）');
        console.log('2. ✅ 新增了专门的工作流分管领导接口（只返回分管领导）');
        console.log('3. ✅ 前端审批页面现在调用新接口，实现了分管领导候选人限制');
        console.log('4. ✅ 科室负责人关联查询功能正常工作');
        
        console.log('\n🔧 技术实现:');
        console.log('- 修改了 approval.vue 中的 loadDepartmentLeaders() 方法');
        console.log('- 从调用 listAllActiveUsers() 改为调用 getAvailableLeaders()');
        console.log('- 添加了 getAvailableLeaders 的导入');
        console.log('- 保持了向后兼容性，不影响其他功能');

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testSecretaryApprovalFix();
