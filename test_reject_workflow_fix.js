/**
 * 测试拒绝流程修复效果
 * 验证当刘晨审批中选择拒绝时，流程状态应该被正确关闭
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:8080';
const LOGIN_URL = `${BASE_URL}/login`;
const WORKFLOW_API = `${BASE_URL}/oa/workflow`;

// 用户信息
const USERS = {
    admin: { username: 'admin', password: 'admin123' }, // 管理员
    liulei: { username: 'liulei', password: 'admin123' }, // 刘磊 - 办公室主任
    wangwu: { username: 'wangwu', password: 'admin123' }, // 王武 - 办公室工作人员
    lisi: { username: 'lisi', password: 'admin123' } // 李四 - 人事科工作人员
};

// 全局变量
let tokens = {};
let testDocumentId = null;
let testProcessInstanceId = null;

/**
 * 登录获取Cookie
 */
async function login(username, password) {
    try {
        const response = await axios.post(LOGIN_URL, {
            username: username,
            password: password,
            code: '1234',
            uuid: 'test-uuid'
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            withCredentials: true
        });

        if (response.data.code === 200) {
            // 提取Token
            const token = response.data.token;
            if (token) {
                tokens[username] = token;
            }
            console.log(`✅ ${username} 登录成功`);
            return true;
        } else {
            console.log(`❌ ${username} 登录失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.log(`❌ ${username} 登录异常:`, error.message);
        return false;
    }
}

/**
 * 创建发文并提交审批
 */
async function createAndSubmitDocument() {
    try {
        console.log('\n📝 步骤1: 管理员创建发文并提交审批...');

        // 创建发文
        const createResponse = await axios.post(`${BASE_URL}/oa/document/send`, {
            docNumber: 'TEST-' + Date.now(),
            docTitle: '测试拒绝流程修复-发文',
            docContent: '<p>这是一个测试拒绝流程修复的发文内容</p>',
            urgencyLevel: '1',
            securityLevel: '1',
            receiverUnit: '测试单位',
            status: '0' // 草稿状态
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + tokens['admin']
            }
        });

        if (createResponse.data.code === 200) {
            testDocumentId = createResponse.data.data.docId;
            console.log(`✅ 发文创建成功，ID: ${testDocumentId}`);
        } else {
            throw new Error(`创建发文失败: ${createResponse.data.msg}`);
        }

        // 提交审批
        const submitResponse = await axios.post(`${BASE_URL}/oa/document/send/submit/${testDocumentId}`, {
            workflowKey: 'document_send_approval_v2'
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + tokens['admin']
            }
        });

        if (submitResponse.data.code === 200) {
            console.log(`✅ 审批流程启动成功`);
            return true;
        } else {
            throw new Error(`启动审批流程失败: ${submitResponse.data.msg}`);
        }
    } catch (error) {
        console.log(`❌ 创建和提交文档失败:`, error.message);
        return false;
    }
}

/**
 * 查询待办任务
 */
async function getMyTasks(username) {
    try {
        const response = await axios.get(`${WORKFLOW_API}/task/todo`, {
            headers: {
                'Authorization': 'Bearer ' + tokens[username]
            }
        });

        if (response.data.code === 200) {
            return response.data.rows || [];
        } else {
            console.log(`❌ 查询${username}的待办任务失败:`, response.data.msg);
            return [];
        }
    } catch (error) {
        console.log(`❌ 查询${username}的待办任务异常:`, error.message);
        return [];
    }
}

/**
 * 完成任务（拒绝）
 */
async function rejectTask(taskId, comment) {
    try {
        const response = await axios.post(`${WORKFLOW_API}/task/complete/${taskId}`, {
            variables: {
                approvalResult: 'reject'
            },
            comment: comment
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + tokens['liulei']
            }
        });

        if (response.data.code === 200) {
            console.log(`✅ 任务拒绝成功: ${comment}`);
            return true;
        } else {
            console.log(`❌ 任务拒绝失败:`, response.data.msg);
            return false;
        }
    } catch (error) {
        console.log(`❌ 任务拒绝异常:`, error.message);
        return false;
    }
}

/**
 * 查询流程监控状态
 */
async function checkProcessStatus() {
    try {
        const response = await axios.get(`${WORKFLOW_API}/instance/list`, {
            params: {
                processInstanceId: testProcessInstanceId
            },
            headers: {
                'Authorization': 'Bearer ' + tokens['liulei']
            }
        });

        if (response.data.code === 200) {
            const instances = response.data.rows || [];
            const targetInstance = instances.find(inst => inst.processInstanceId === testProcessInstanceId);
            
            if (targetInstance) {
                console.log(`📊 流程状态: ${targetInstance.status} (${getStatusText(targetInstance.status)})`);
                console.log(`📊 流程结束时间: ${targetInstance.endTime || '未结束'}`);
                return targetInstance.status;
            } else {
                console.log(`❌ 未找到流程实例: ${testProcessInstanceId}`);
                return null;
            }
        } else {
            console.log(`❌ 查询流程状态失败:`, response.data.msg);
            return null;
        }
    } catch (error) {
        console.log(`❌ 查询流程状态异常:`, error.message);
        return null;
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        '1': '进行中',
        '2': '已完成',
        '3': '已终止'
    };
    return statusMap[status] || '未知状态';
}

/**
 * 主测试流程
 */
async function runTest() {
    console.log('🚀 开始测试拒绝流程修复效果...\n');

    try {
        // 1. 登录所有用户
        console.log('👤 登录用户...');
        const loginResults = await Promise.all([
            login('admin', 'admin123'),
            login('liulei', 'admin123'),
            login('wangwu', 'admin123')
        ]);

        if (!loginResults.every(result => result)) {
            throw new Error('用户登录失败');
        }

        // 2. 创建发文并提交审批
        if (!await createAndSubmitDocument()) {
            throw new Error('创建和提交文档失败');
        }

        // 3. 等待一下让流程启动完成
        console.log('\n⏳ 等待流程启动完成...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 4. 查询刘磊的待办任务
        console.log('\n📋 步骤2: 查询刘磊的待办任务...');
        const liuleiTasks = await getMyTasks('liulei');
        console.log(`📋 刘磊的待办任务数量: ${liuleiTasks.length}`);

        if (liuleiTasks.length === 0) {
            throw new Error('刘磊没有待办任务，可能流程分配有问题');
        }

        // 打印所有任务信息用于调试
        console.log('所有任务详情:', liuleiTasks.map(t => ({
            id: t.id,
            flowableTaskId: t.flowableTaskId,
            taskId: t.taskId,
            name: t.name || t.taskName,
            businessKey: t.businessKey,
            processInstanceId: t.processInstanceId
        })));

        // 找到相关的任务（通过文档ID匹配）
        const targetTask = liuleiTasks.find(task =>
            task.businessKey && task.businessKey.includes(`doc_send_${testDocumentId}`)
        );

        if (!targetTask) {
            throw new Error('未找到相关的审批任务');
        }

        console.log(`📋 找到目标任务: ${targetTask.name || targetTask.taskName} (ID: ${targetTask.flowableTaskId || targetTask.taskId || targetTask.id})`);
        testProcessInstanceId = targetTask.processInstanceId;

        // 5. 刘磊拒绝任务
        console.log('\n❌ 步骤3: 刘磊拒绝审批...');
        const taskId = targetTask.flowableTaskId || targetTask.taskId || targetTask.id;
        if (!await rejectTask(taskId, '文档内容不符合要求，需要重新修改')) {
            throw new Error('拒绝任务失败');
        }

        // 6. 等待一下让拒绝处理完成
        console.log('\n⏳ 等待拒绝处理完成...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 7. 检查流程状态
        console.log('\n🔍 步骤4: 检查流程状态...');
        const finalStatus = await checkProcessStatus();

        // 8. 验证结果
        console.log('\n📊 测试结果验证:');
        if (finalStatus === '3') {
            console.log('✅ 测试通过: 流程状态已正确更新为"已终止"');
            console.log('✅ 修复成功: 拒绝操作正确关闭了流程');
        } else if (finalStatus === '1') {
            console.log('❌ 测试失败: 流程状态仍然是"进行中"，修复未生效');
            console.log('❌ 问题依然存在: 拒绝操作没有正确关闭流程');
        } else {
            console.log(`❓ 测试结果不确定: 流程状态为 ${getStatusText(finalStatus)}`);
        }

        // 9. 验证文档状态
        console.log('\n📄 验证文档状态...');
        const docResponse = await axios.get(`${BASE_URL}/oa/document/send/${testDocumentId}`, {
            headers: {
                'Authorization': 'Bearer ' + tokens['admin']
            }
        });

        if (docResponse.data.code === 200) {
            const docStatus = docResponse.data.data.status;
            console.log(`📄 文档状态: ${docStatus} (${docStatus === '0' ? '草稿' : '其他'})`);
            if (docStatus === '0') {
                console.log('✅ 文档状态已正确恢复为草稿');
            } else {
                console.log('❌ 文档状态未正确恢复为草稿');
            }
        }

    } catch (error) {
        console.log(`\n❌ 测试执行失败: ${error.message}`);
    }

    console.log('\n🏁 测试完成');
}

// 运行测试
runTest();
