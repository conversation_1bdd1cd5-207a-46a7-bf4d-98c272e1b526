-- 直接执行版本：为科员109分配发文管理权限

-- 1. 更新发文列表菜单，添加权限标识
UPDATE sys_menu SET perms = 'oa:document:send:list' WHERE menu_name = '发文列表';

-- 2. 添加发文管理的功能权限按钮（假设发文列表菜单ID为20076）
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, remark) VALUES
('发文查询', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 1, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:query', '#', 'admin', NOW(), ''),
('发文新增', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 2, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:add', '#', 'admin', NOW(), ''),
('发文修改', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 3, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:edit', '#', 'admin', NOW(), ''),
('发文删除', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 4, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:remove', '#', 'admin', NOW(), ''),
('发文导出', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 5, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:export', '#', 'admin', NOW(), ''),
('发文提交审批', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 6, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:submit', '#', 'admin', NOW(), ''),
('发文发布', (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1), 7, '#', '', '', 1, 0, 'F', '0', '0', 'oa:document:send:publish', '#', 'admin', NOW(), '');

-- 3. 为科员角色分配发文列表菜单权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'clerk' AND m.menu_name = '发文列表';

-- 4. 为科员角色分配发文管理的所有功能权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id) 
SELECT r.role_id, m.menu_id 
FROM sys_role r, sys_menu m 
WHERE r.role_key = 'clerk' 
AND m.parent_id = (SELECT menu_id FROM sys_menu WHERE menu_name = '发文列表' LIMIT 1)
AND m.menu_type = 'F';

-- 5. 验证结果
SELECT 
    '权限分配结果' as '说明',
    r.role_name as '角色名称',
    m.menu_name as '菜单名称',
    m.perms as '权限标识',
    CASE m.menu_type 
        WHEN 'C' THEN '菜单' 
        WHEN 'F' THEN '按钮' 
        ELSE m.menu_type 
    END as '类型'
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key = 'clerk' 
AND (m.menu_name LIKE '%发文%' OR m.perms LIKE '%send%')
ORDER BY m.menu_type, m.order_num;
