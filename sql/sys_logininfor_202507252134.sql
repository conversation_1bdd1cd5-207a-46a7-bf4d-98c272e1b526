INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-05 22:43:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-05 23:45:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-06 00:17:12'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-07 09:45:01'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-12 17:21:35'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-13 09:58:03'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-13 11:27:49'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-13 13:50:30'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-13 15:25:44'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-13 22:07:45');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','**************','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-13 22:22:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-13 23:08:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','1','验证码错误','2024-06-15 14:11:45'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','1','验证码错误','2024-06-15 14:11:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-15 14:12:00'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-15 14:13:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-15 14:18:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-15 15:21:46'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-15 16:19:50'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','1','验证码错误','2024-06-17 20:45:04');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-17 20:45:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-17 21:39:44'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-17 21:45:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-17 22:15:26'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-17 23:44:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-18 00:02:29'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-18 20:51:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-19 15:35:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码错误','2024-06-20 09:35:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-20 09:35:23');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-20 11:53:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-20 14:23:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-20 14:40:55'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-20 14:49:54'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','1','验证码已失效','2024-06-20 16:57:42'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-20 16:57:47'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','1','验证码错误','2024-06-20 20:50:20'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-20 20:50:22'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-06-20 22:11:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-20 22:51:48');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-21 14:35:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-22 17:31:37'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 13:52:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 15:30:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 21:00:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-23 21:12:06'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 21:12:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-23 21:22:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 21:42:37'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-23 21:50:16');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 21:50:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-23 21:54:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 21:58:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-23 22:23:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-23 22:23:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-24 10:36:11'),
	 ('admin','************','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-24 11:47:30'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 12:01:09'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','1','验证码错误','2024-06-24 12:35:30'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 12:35:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 13:40:46'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 14:38:10'),
	 ('admin','************','内网IP','Chrome 8','Windows 7','0','登录成功','2024-06-24 14:44:28'),
	 ('admin','************','内网IP','Chrome 8','Windows 7','0','登录成功','2024-06-24 14:44:46'),
	 ('admin','************','内网IP','Chrome 8','Windows 7','0','退出成功','2024-06-24 14:50:51'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','退出成功','2024-06-24 14:55:38'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 15:06:20'),
	 ('admin','************','内网IP','Chrome 12','Windows 10','0','登录成功','2024-06-24 15:07:27'),
	 ('admin','************','内网IP','Chrome 8','Windows 7','0','登录成功','2024-06-24 15:08:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 17:13:23');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 17:15:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 18:59:15'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 20:55:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-29 21:10:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 21:11:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-29 21:26:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 21:26:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-06-29 22:00:29'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 22:00:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-29 22:36:16');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-06-30 14:37:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-07-01 16:09:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-01 17:18:50'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-13 15:48:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-14 11:08:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-14 11:42:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-14 20:26:20'),
	 ('admin','*************','XX XX','Chrome 12','Mac OS X','1','验证码已失效','2024-07-14 21:35:53'),
	 ('admin','*************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-14 21:36:00'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-15 08:55:38');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-15 08:59:59'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-15 09:03:35'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-07-15 09:06:44'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-15 09:06:50'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-15 09:19:28'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 08:53:51'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 08:53:51'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码错误','2024-07-16 08:54:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-16 08:54:05'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 09:01:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 10:06:53'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 14:39:11'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:07:53'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:09:07'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:09:39'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:10:05'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:10:06'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-16 15:11:46'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-07-16 15:13:48'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:13:48');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:13:53'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:14:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码已失效','2024-07-16 15:14:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:14:30'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:35:44'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:36:48'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:37:29'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:39:53'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:41:09'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:44:16');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-16 15:51:57'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-16 15:51:59'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:52:05'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:56:42'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-07-16 15:57:00'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 15:59:04'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 16:03:56'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 16:05:27'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 16:24:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码错误','2024-07-16 16:31:54');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-16 16:31:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码已失效','2024-07-16 23:30:38'),
	 ('admin','127.0.0.1','内网IP','Chrome Mobile','Android 6.x','1','验证码错误','2024-07-16 23:30:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-16 23:30:38'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','1','验证码错误','2024-07-16 23:30:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-16 23:30:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-07-16 23:30:38'),
	 ('admin','***************','XX XX','Chrome Mobile','Android 6.x','1','验证码错误','2024-07-16 23:30:38'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-16 23:30:39');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','127.0.0.1','内网IP','Chrome Mobile','Android 6.x','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-16 23:30:39');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:30:39'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:33:09'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-07-16 23:49:51'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','1','验证码已失效','2024-07-16 23:58:00'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-16 23:58:06'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 08:50:05'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 08:50:13'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 08:51:41'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 08:55:04');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 09:15:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 09:24:02'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-17 09:39:01'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 09:41:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 09:49:31'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-17 11:18:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 12:28:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-07-17 12:34:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 12:34:53'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-07-17 12:37:54');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 12:37:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-07-17 12:42:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 12:42:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 13:33:26'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 14:35:47'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 14:53:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 15:48:09'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 16:01:32'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-17 17:05:21'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 17:05:42');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-17 17:10:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-17 17:53:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-18 00:22:53'),
	 ('admin','**************','XX XX','Chrome 12','Mac OS X','1','验证码错误','2024-07-18 01:02:54'),
	 ('admin','**************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-18 01:02:59'),
	 ('admin','**************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-18 01:54:36'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 08:43:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 08:43:48'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 08:52:52'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 08:53:28');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','**************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-07-18 09:05:24'),
	 ('admin','**************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 09:24:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-07-18 09:26:26'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-07-18 10:40:02'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 10:40:05'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 12:25:22'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 13:03:00'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 13:59:32'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-18 14:12:50'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-07-22 16:27:55');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 11','Windows 10','0','登录成功','2024-08-02 16:12:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-02 16:21:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-10 15:38:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-10 16:52:37'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-10 22:32:09'),
	 ('admin','************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-16 11:25:01'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-16 16:49:13'),
	 ('admin','**************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-17 18:29:03'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-28 09:50:55'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 09:50:58');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 09:52:27'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-28 09:53:35'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 09:53:42'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-28 09:53:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 09:53:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-28 10:00:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-08-28 10:11:27'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 10:11:31'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 10:28:45'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-28 10:37:45');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 10:37:49'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 10:46:12'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 10:56:25'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 14:45:47'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 14:45:48'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 14:49:56'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 15:48:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-28 16:13:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-28 16:18:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-28 17:16:53');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 17:31:25'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 17:32:35'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 17:33:32'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-28 17:33:39'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 17:34:09'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-28 17:45:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-28 19:12:05'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:02:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-29 08:38:18'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:38:23');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:41:28'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:41:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:42:00'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-29 08:42:22'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-29 08:42:52'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-29 08:42:53'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 08:43:00'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 09:26:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-29 09:51:42'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-08-29 09:57:47');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 09:57:52'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 09:58:07'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-29 09:58:26'),
	 ('admin','************','内网IP','Chrome 12','Mac OS X','1','验证码已失效','2024-08-29 13:39:29'),
	 ('admin','************','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 13:39:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','1','验证码已失效','2024-08-29 16:59:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-29 16:59:18'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 17:04:16'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-29 17:04:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 17:31:29');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 17:50:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 19:57:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 20:49:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-29 21:56:41'),
	 ('admin','***************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-08-30 00:13:41'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 08:34:58'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 08:56:56'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 09:31:57'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-08-30 09:58:24'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 09:59:30');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-30 10:18:35'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 10:26:29'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 10:26:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 10:27:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','1','验证码已失效','2024-08-30 10:29:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-30 10:29:14'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 10:29:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 10:33:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-30 10:49:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-08-30 10:53:14');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-30 10:53:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-30 11:07:32'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 11:25:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 14:33:34'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 14:35:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 11','Windows 10','0','登录成功','2024-08-30 14:35:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-08-30 14:52:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 14:52:55'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 15:14:36'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-30 15:17:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 15:17:40'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 15:26:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-30 15:34:17'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-08-30 15:35:18'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 15:35:21'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-08-30 15:45:01'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 16:23:07'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-08-30 16:23:47'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 16:27:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-08-30 17:05:48');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-08-30 17:11:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-08-31 18:20:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-08-31 18:24:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-31 18:30:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-08-31 18:45:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-08-31 19:09:10'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-01 08:42:06'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-01 12:22:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-09-01 12:57:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-01 13:31:49');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-01 14:06:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-01 14:08:26'),
	 ('admin','**************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-01 14:11:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-09-01 14:13:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-01 20:57:54'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码错误','2024-09-01 22:00:10'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-09-01 22:00:15'),
	 ('admin','************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-01 23:20:25'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 08:58:33'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 09:24:43');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-02 10:19:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-09-02 10:32:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-02 10:32:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-09-02 10:41:45'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-02 11:26:06'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 11:39:06'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 14:37:15'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 14:41:01'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-02 14:42:44'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 14:42:49');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-02 15:06:17'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-02 15:06:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-02 22:37:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-02 22:51:26'),
	 ('admin','***************','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-02 23:58:33'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 09:06:52'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 09:08:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-09-03 11:04:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 14:25:18'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 16:43:50');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','1','验证码错误','2024-09-03 17:44:37'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:37'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:37'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:37'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-03 17:44:38');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:44:38'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-03 17:47:33'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-03 17:49:36'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-03 18:22:03'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-04 09:28:15'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-04 09:28:16'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-04 09:28:18'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-04 09:28:24'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-04 09:28:28');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-04 09:28:32'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-04 09:53:13'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-04 09:53:14'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-04 10:22:11'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-04 14:56:05'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-04 16:08:05'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-05 08:27:41'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-05 09:00:34'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-05 10:30:40'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-05 10:30:43');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:11:20'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-06 09:14:52'),
	 ('zjjadmin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:15:04'),
	 ('zjjadmin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-06 09:15:14'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:15:18'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-06 09:26:41'),
	 ('zjjadmin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:26:54'),
	 ('zjjadmin','*************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-06 09:28:52'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:29:02'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-06 09:36:07');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 09:36:12'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-06 10:15:48'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-06 10:39:24'),
	 ('admin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-06 10:39:29'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:41:46'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 13:43:03'),
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:43:15'),
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 13:49:37'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:49:40'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 13:50:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:50:46'),
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 13:50:57'),
	 ('ry','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:51:03'),
	 ('ry','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 13:51:38'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 13:51:42'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 14:13:43'),
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 14:13:52'),
	 ('zjjadmin','***********','XX XX','Chrome 12','Mac OS X','0','退出成功','2024-09-06 14:15:40'),
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-06 14:15:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-06 14:19:14');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-09-06 14:19:22'),
	 ('zjjadmin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-06 14:19:31'),
	 ('zjjadmin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','退出成功','2024-09-06 14:19:41'),
	 ('jzjadmin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-09 10:13:26'),
	 ('jzadmin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-09 10:13:33'),
	 ('jzadmin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-09 10:13:37'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 10:13:57'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 10:14:11'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 10:14:23'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 11:42:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***********','XX XX','Chrome 12','Mac OS X','0','登录成功','2024-09-09 11:43:34'),
	 ('zjjadmin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-09-09 14:18:42'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 14:36:39'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 15:01:37'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-09 15:01:43'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 15:01:46'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 16:54:04'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 16:54:34'),
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 16:54:47'),
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 16:59:41');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 16:59:51'),
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 16:59:59'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 17:00:07'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 17:10:27'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 17:10:44'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 17:10:48'),
	 ('zjjadmin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-09 17:11:16'),
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-09 17:11:21'),
	 ('fgjadmin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-09 17:11:36'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-10 16:55:57');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-11 08:34:28'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-11 08:58:33'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-11 09:27:14'),
	 ('admin','***********','XX XX','Apple WebKit','Mac OS X (iPhone)','0','登录成功','2024-09-13 10:50:33'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-13 10:53:32'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-13 10:54:01'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-13 10:54:20'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-13 10:54:38'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-13 10:54:39'),
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-13 10:54:40');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','*************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-13 10:55:00'),
	 ('zjjadmin','*************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-13 10:55:32'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-18 10:08:55'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-18 10:08:59'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-19 15:17:16'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-19 15:17:19'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-27 10:54:02'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-27 10:54:18'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 08:35:20'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-29 08:41:03');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 08:41:08'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 08:46:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-29 08:47:13'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-29 08:47:23'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-29 08:48:22'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-29 08:48:26'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-29 08:48:42'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','用户不存在/密码错误','2024-09-29 08:49:03'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-29 08:49:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-09-29 08:49:05');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 08:51:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-09-29 08:52:41'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 08:52:46'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-09-29 08:53:11'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 09:27:08'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-09-29 11:08:14'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-11 16:25:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-13 23:50:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-14 09:58:46'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-14 09:59:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-14 10:31:21'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-14 14:39:17'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-14 14:40:19'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-15 17:35:52'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-15 17:36:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-15 21:53:03'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-16 14:15:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-17 08:59:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-17 13:48:03'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-17 15:56:46');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-17 16:45:31'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-17 16:51:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-20 12:04:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-20 12:07:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-20 12:15:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2024-10-20 13:31:06'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-20 23:08:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 09:32:38'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-10-21 09:38:42'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 09:38:46');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 09:48:54'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 14:36:30'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 15:03:45'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 15:27:47'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-21 15:32:31'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 15:32:35'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-21 15:32:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 15:33:01'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 16:08:35'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 16:10:35');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-21 16:12:15'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-10-21 16:14:33'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 16:14:39'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-21 16:21:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-21 16:21:12'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-22 09:00:44'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2024-10-22 10:07:29'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-22 11:36:15'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-10-22 12:16:30'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码已失效','2024-10-22 15:01:39');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-22 15:01:43'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-22 16:31:00'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-10-22 16:41:56'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-23 08:33:04'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-10-23 09:57:49'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-23 09:57:51'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','退出成功','2024-10-23 11:12:30'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-23 11:13:13'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-23 14:30:23'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-10-23 17:10:48');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-10-24 14:35:26'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-10-25 10:09:33'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-10-28 10:19:35'),
	 ('admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-10-29 09:12:28'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-01 14:56:14'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-04 15:10:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Mac OS X','0','登录成功','2024-11-04 21:38:08'),
	 ('admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-04 22:19:30'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-04 23:20:44'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-05 09:07:56');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-05 14:48:19'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-05 16:17:49'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-06 14:57:44'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-06 15:16:42'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-06 16:07:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-08 11:49:11'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-08 12:41:26'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-09 09:33:45'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-09 09:34:53'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-09 09:56:48');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-12 11:19:14'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-12 14:48:27'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-12 15:01:15'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-12 15:02:43'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-12 16:12:14'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-14 16:54:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2024-11-14 20:12:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2024-11-14 20:12:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-14 20:12:44'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-15 08:42:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-15 09:17:18'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-15 15:55:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-15 16:10:10'),
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-15 16:38:56'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-15 16:45:16'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-15 16:45:19'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-16 15:17:26'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-17 10:59:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-17 21:17:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2024-11-17 23:01:20');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-18 00:42:59'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 08:30:53'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 08:31:27'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 08:45:25'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 09:03:33'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 09:23:08'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:02:04'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:04:09'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-18 10:06:51'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:06:54');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:36:28'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-18 10:36:40'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:36:46'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','1','验证码错误','2024-11-18 10:37:07'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 10:37:10'),
	 ('admin','**************','XX XX','Chrome Mobile','Android 1.x','0','登录成功','2024-11-18 11:17:33'),
	 ('zjjadmin','**************','XX XX','Chrome 12','Windows 10','1','验证码错误','2024-11-18 11:19:59'),
	 ('zjjadmin','**************','XX XX','Chrome 12','Windows 10','0','登录成功','2024-11-18 11:20:13'),
	 ('fgjadmin','***************','XX XX','Chrome 10','Windows','1','用户不存在/密码错误','2024-11-18 11:20:32'),
	 ('fgjadmin','***************','XX XX','Chrome 10','Windows','1','验证码错误','2024-11-18 11:20:49');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','***************','XX XX','Chrome 10','Windows','0','登录成功','2024-11-18 11:20:58'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 11:51:22'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-18 14:46:58'),
	 ('zjjadmin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:33:59'),
	 ('zjjadmin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:34:21'),
	 ('fgjadmin','***************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2024-11-19 09:34:33'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:34:46'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:36:02'),
	 ('fgjadmin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:36:10'),
	 ('fgjadmin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:36:33');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:36:38'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:37:24'),
	 ('fgjadmin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:37:30'),
	 ('fgjadmin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:41:48'),
	 ('zjjadmin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-19 09:41:53'),
	 ('zjjadmin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2024-11-19 09:42:21'),
	 ('admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-11-24 10:30:47'),
	 ('admin','*************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-12-12 10:13:39'),
	 ('zjjadmin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2024-12-28 13:11:25'),
	 ('admin','**************','XX XX','Apple WebKit','Mac OS X (iPhone)','0','登录成功','2024-12-28 13:11:39');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Safari','Mac OS X','1','验证码错误','2025-01-02 12:54:40'),
	 ('zjjadmin','***************','XX XX','Safari','Mac OS X','0','登录成功','2025-01-02 12:54:59'),
	 ('zjjadmin','***************','XX XX','Safari','Mac OS X','0','登录成功','2025-01-02 13:48:17'),
	 ('zjjadmin','***************','XX XX','Safari','Mac OS X','0','登录成功','2025-01-02 18:56:03'),
	 ('zjjadmin','************','XX XX','Safari','Mac OS X','0','登录成功','2025-01-03 22:37:25'),
	 ('zjjadmin','************','XX XX','Safari','Mac OS X','0','退出成功','2025-01-03 22:43:44'),
	 ('fgjadmin','************','XX XX','Safari','Mac OS X','0','登录成功','2025-01-03 22:43:51'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-02-25 16:54:48'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-02-25 16:58:35'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-02-25 17:02:45');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-02-25 18:01:07'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-02-26 10:45:21'),
	 ('admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-03-03 17:25:05'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-03-26 10:33:19'),
	 ('admin','***************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-03-26 10:33:29'),
	 ('admin','**********','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-07 21:42:07'),
	 ('admin','**********','XX XX','Chrome 13','Windows 10','0','退出成功','2025-04-07 21:47:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','1','验证码错误','2025-04-12 13:57:10'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2025-04-12 13:57:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 13:57:52');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-12 14:27:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-04-12 14:27:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 14:27:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-12 14:28:03'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 14:28:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-04-12 14:28:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 14:29:05'),
	 ('admin','*************','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 21:19:08'),
	 ('admin','*************','内网IP','Chrome 13','Windows 10','0','登录成功','2025-04-12 22:08:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2025-04-13 21:20:21');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 10','Windows 10','1','验证码错误','2025-04-14 17:20:23'),
	 ('admin','************','XX XX','Chrome 10','Windows 10','0','登录成功','2025-04-14 17:20:30'),
	 ('admin','**************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-04-14 17:30:33'),
	 ('admin','************','XX XX','Chrome 10','Windows 10','0','退出成功','2025-04-14 17:34:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 12','Windows 10','0','登录成功','2025-04-14 19:46:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-29 20:22:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-30 13:56:40'),
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-01 22:39:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-01 22:39:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-01 22:39:13');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-03 21:56:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-03 22:36:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-03 22:37:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-04 01:39:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-04 01:39:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-04 02:06:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-04 02:06:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 13:33:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 14:36:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 14:55:50');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-05 15:28:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-05 15:28:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 15:28:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 15:38:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 18:18:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 18:26:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 18:33:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 18:40:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 18:51:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 20:10:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 20:28:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 20:46:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 20:50:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:03:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:16:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:26:51'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:27:37'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:29:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:47:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 21:52:25');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:15:31'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:38:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-05 22:42:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:42:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:45:50'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:45:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:54:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-05 22:55:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 22:55:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 23:05:28');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 23:22:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-05 23:33:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-06 00:09:50'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-06 00:14:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-06 01:14:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 21:30:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 21:41:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 21:55:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:18:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:19:00');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:24:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-07 22:31:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:31:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:49:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 22:59:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 23:12:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 23:22:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 23:25:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-07 23:58:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-07 23:58:58');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-07 23:59:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-08 00:00:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:00:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-08 00:05:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:05:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-08 00:10:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:10:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:27:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:45:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 00:47:50');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 01:05:47'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 19:04:03'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-08 19:05:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-08 19:34:47'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-08 19:34:57'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-08 19:38:03'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-09 19:25:24'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-09 19:47:55'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-09 19:53:58'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-09 20:00:24');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-09 21:45:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-09 21:45:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-09 22:14:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-09 23:59:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 00:13:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 01:23:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-10 01:30:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 01:30:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-10 01:36:35'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-10 01:36:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 01:36:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 01:49:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 08:55:46'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-10 10:34:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 12:36:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 13:43:03'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-10 13:54:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 13:54:44'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-10 14:07:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 22:03:59');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-10 22:04:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 22:04:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-10 22:47:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 00:30:31'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 00:49:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 01:11:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 22:20:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 22:31:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 22:41:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-11 23:49:14');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-12 00:16:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 00:16:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 00:31:06'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 00:52:49'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 09:37:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-12 10:28:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 10:29:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 11:09:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 11:45:22'),
	 ('admin','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-07-12 11:48:38');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-12 11:54:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 11:54:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 12:51:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-12 12:55:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 12:55:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 13:16:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 13:28:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-12 13:36:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 13:36:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 13:44:26');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-12 14:15:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-12 14:16:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 14:16:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 15:07:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 15:14:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-12 15:25:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-12 16:30:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 21:18:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 21:24:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 21:45:36');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 22:01:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 22:28:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:00:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:10:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:18:54'),
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-12 23:22:21'),
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-12 23:22:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:22:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:38:47'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-12 23:55:36');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-13 00:00:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 00:00:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 00:11:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 00:29:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 00:29:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 00:35:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 11:57:06'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 12:06:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-13 12:15:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 12:16:00');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 12:17:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 12:27:03'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 13:05:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-13 13:09:51'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:08:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:16:19'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:28:15'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:43:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-14 01:44:04'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:44:09');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-14 01:44:17'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:44:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-14 01:46:17'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:46:22'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:52:55'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 01:54:56'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 02:01:06'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 02:08:50'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-14 02:12:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-14 14:45:24');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-14 15:37:19'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-14 17:00:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-15 09:24:02'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-15 09:28:00'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-15 10:39:54'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-15 10:44:40'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-15 10:47:02'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-15 22:37:43'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-15 23:13:19'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:09:31');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:10:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:10:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:11:19'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:11:24'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:12:16'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:13:02'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:13:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:14:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:14:14'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:15:13');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:16:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 00:16:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:16:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:17:20'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:17:31'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:17:56'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 00:18:02'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:18:07'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 00:18:18'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-16 00:20:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 00:20:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:21:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:23:13'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:23:19'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:23:35'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:24:31'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:24:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:24:45'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:27:08'),
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:27:12');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('fgjadmin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-16 00:40:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:40:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:41:24'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:41:30'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:41:51'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:41:56'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:42:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:42:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:42:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:44:04');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 00:44:10'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:44:13'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:44:25'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:44:41'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 00:45:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 00:45:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:07:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:13:06'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:13:11'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:13:37');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:13:40'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:14:28'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:14:33'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:14:37'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:14:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:16:41'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:16:46'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:17:00'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 02:17:06'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:17:11');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:19:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:19:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 02:26:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-16 02:26:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:26:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:37:37'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:37:43'),
	 ('ry','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:37:55'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:38:02'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-16 02:38:41');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-16 02:38:47'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 10:39:22'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 10:57:01'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','1','用户已封禁，请联系管理员','2025-07-16 10:57:08'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 10:57:17'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 10:57:37'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 10:57:44'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 10:58:49'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 10:58:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-16 11:23:13');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-16 11:23:19'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 11:29:00'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','1','用户已封禁，请联系管理员','2025-07-16 11:29:12'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 11:29:18'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 11:29:37'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 11:29:44'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:32:04'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:32:30'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:32:35'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:33:00');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:33:05'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:46:37'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','1','用户已封禁，请联系管理员','2025-07-16 14:46:52'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:46:58'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:47:09'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 14:47:16'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:47:26'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:47:42'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-16 14:47:51'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:47:55');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('ry','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:51:15'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:51:30'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:51:59'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-16 14:52:07'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 14:52:12'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-16 14:52:15'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 14:52:18'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 14:52:27'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:52:33'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:52:58');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:53:08'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 14:54:35'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 14:54:43'),
	 ('ry','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 15:00:15'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 15:00:19'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 15:32:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-16 15:41:23'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:51:17'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:54:38'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:54:46');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:55:12'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','1','验证码已失效','2025-07-16 16:57:20'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:57:24'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:57:46'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:57:53'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:58:20'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-16 16:58:28'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:58:31'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:58:46'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:58:54');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 16:59:15'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 16:59:25'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:09:55'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:09:59'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:10:16'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:10:31'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:10:46'),
	 ('zhansgan','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 17:10:56'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:11:05'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:11:22');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-16 17:11:31'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:11:37'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:11:56'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:12:02'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:12:08'),
	 ('wutao','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:12:16'),
	 ('wutao','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:12:35'),
	 ('qianqi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:12:44'),
	 ('qianqi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:13:06'),
	 ('wangyu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:13:19');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangyu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:13:36'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:13:44'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:13:57'),
	 ('liuer','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:14:04'),
	 ('liuer','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:14:35'),
	 ('wangliang','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-16 17:14:45'),
	 ('wangliang','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:14:48'),
	 ('wangliang','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:15:01'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:15:03'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:15:17');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('yangyang','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:15:24'),
	 ('yangyang','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:15:44'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:15:48'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:16:03'),
	 ('sunzhida','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:16:10'),
	 ('sunzhida','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:16:26'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:16:31'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:16:41'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:16:46'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:17:06');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:17:09'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-16 17:19:21'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-16 17:19:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 00:07:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-17 00:20:52'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-17 00:20:57'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 00:21:35'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-17 00:27:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 00:27:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-17 00:38:49');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 00:51:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-17 08:41:00'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 09:33:36'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 10:11:25'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 10:11:32'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 10:13:03'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 10:13:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-17 10:28:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 10:28:55'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:29:01');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:31:26'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:31:34'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:32:50'),
	 ('wamngwu','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-17 18:32:56'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:33:02'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:34:00'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:34:06'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:35:15'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:35:18'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:39:43');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:39:53'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:40:05'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-17 18:40:10'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-17 18:40:17'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:40:23'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:40:33'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','1','验证码错误','2025-07-17 18:40:38'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-17 18:40:42'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:40:48'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:41:05');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:41:10'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:41:34'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:41:42'),
	 ('wangwu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:42:00'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:42:10'),
	 ('zhangsan','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:42:29'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:42:40'),
	 ('liulei','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:42:53'),
	 ('wutao','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:43:02'),
	 ('wutao','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:43:16');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('qianqi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:43:28'),
	 ('qianqi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:43:43'),
	 ('wangyu','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:43:53'),
	 ('wangyu','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:44:10'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:44:20'),
	 ('lisi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:44:32'),
	 ('liuer','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:44:40'),
	 ('liuer','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:44:59'),
	 ('wangliang','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:45:07'),
	 ('wangliang','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:45:21');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:45:27'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:45:47'),
	 ('yangyang','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:45:56'),
	 ('yangyang','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:46:10'),
	 ('sunzhida','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:46:19'),
	 ('sunzhida','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:46:32'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:46:37'),
	 ('zhaoyi','************','XX XX','Chrome 13','Windows 10','0','退出成功','2025-07-17 18:46:54'),
	 ('admin','************','XX XX','Chrome 13','Windows 10','0','登录成功','2025-07-17 18:47:00'),
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-17 23:38:11');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 23:38:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 23:48:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-17 23:57:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 00:26:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 00:48:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 10:31:31'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:00:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:00:39'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:00:50'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:03:34');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:03:46'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:04:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:04:15'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:06:59'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:07:05'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:07:25'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:07:30'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:09:05'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:09:16'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:09:35');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('张三','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-18 15:09:41'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:09:50'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:10:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:10:28'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:10:54'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:11:01'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:12:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:12:04'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:12:14'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:12:22');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:12:41'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:12:51'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:13:10'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:13:17'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:13:22'),
	 ('wangyu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:13:32'),
	 ('wangyu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:13:48'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:13:52'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:14:05'),
	 ('liuer','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:14:15');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liuer','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:14:34'),
	 ('yangyang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:14:42'),
	 ('yangyang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:14:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:15:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:15:10'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:15:18'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:15:36'),
	 ('sunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:15:44'),
	 ('sunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:15:58'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:16:07');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-18 15:16:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 15:16:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-18 17:22:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 21:40:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 22:08:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-18 22:15:00'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-18 22:15:03'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 22:15:05'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 22:29:54'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 23:35:12');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 23:38:33'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-18 23:39:42'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 23:39:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 23:46:19'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-18 23:48:21'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-18 23:48:27'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-18 23:51:54'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-18 23:51:59'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 00:11:28'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-19 08:57:51');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 08:57:55'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 09:09:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:09:29'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:15:20'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:21:23'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:29:34'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:34:49'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 09:35:04'),
	 ('lis','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-19 09:36:15'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-19 09:36:20');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:36:26'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 09:37:31'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:38:43'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:51:39'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:52:59'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 09:53:53'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:53:57'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 09:56:28'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:56:32'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 09:59:30');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:09:21'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:17:40'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:32:49'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 10:33:04'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:33:11'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 10:34:50'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-19 10:34:58'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-19 10:36:16'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-19 10:36:22'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:36:51');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 10:40:52'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:40:57'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 10:41:18'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:41:22'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:50:06'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-19 10:50:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-19 10:50:18'),
	 ('18890062616','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-20 00:30:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-20 00:30:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 09:03:31');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-21 09:39:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 09:39:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 10:03:09'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 10:35:59'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 11:13:34'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 11:32:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 11:43:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 11:50:46'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 15:37:10'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 15:45:01');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 15:45:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 15:47:37'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 15:47:44'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 15:47:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 15:47:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 15:48:15'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户已封禁，请联系管理员','2025-07-21 15:48:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 15:48:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 15:48:37'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 15:48:45');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-21 15:48:50'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 15:48:54'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 15:49:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-21 15:49:40'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 15:50:08'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 15:51:00'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 15:51:07'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','密码输入错误5次，帐户锁定10分钟','2025-07-21 15:51:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 15:51:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 16:08:19');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-21 16:08:25'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 16:08:29'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 16:10:12'),
	 ('zunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 16:10:18'),
	 ('sunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 16:10:28'),
	 ('sunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 16:10:37'),
	 ('sunzhida','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 16:11:52'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 16:11:56'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 16:58:25'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:00:36');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 17:00:52'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:01:04'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:02:09'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:02:17'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:03:41'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:03:49'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:04:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:04:27'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:04:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码已失效','2025-07-21 17:10:23');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:10:26'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:10:51'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 17:11:07'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','1','验证码错误','2025-07-21 17:11:18'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:11:20'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:11:47'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:11:54'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:12:28'),
	 ('liuer','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 17:12:35'),
	 ('liuer','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:12:41');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liuer','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:13:04'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 17:13:32'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:13:38'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:14:04'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-21 17:14:09'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:14:15'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-21 17:14:21'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-21 17:14:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-21 23:51:29'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:37:02');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:38:50'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-23 14:38:57'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:39:04'),
	 ('lisi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:39:38'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:39:52'),
	 ('wangwu','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:45:41'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:45:48'),
	 ('zhangsan','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:49:07'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-23 14:49:15'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:49:21');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:50:28'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-23 14:50:34'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:50:38'),
	 ('wutao','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:51:03'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:51:12'),
	 ('qianqi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:51:33'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-23 14:51:38'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:51:43'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 14:51:55'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 14:51:58');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 15:29:48'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','退出成功','2025-07-23 15:37:57'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','1','用户不存在/密码错误','2025-07-23 15:38:06'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 15:38:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-23 16:47:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-24 14:46:57'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 20:56:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 21:03:55'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-24 21:04:01'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:04:30');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:19:54'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:23:23'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:33:35'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 21:48:37'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:48:46'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 21:49:45'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:49:51'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:55:11'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:58:50'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 21:59:09');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:59:14'),
	 ('zhaoyi','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 21:59:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 21:59:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 22:00:49'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:00:53'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 22:01:02'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:01:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:17:20'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 22:17:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:17:33');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 22:28:12'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-24 22:28:23'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:28:26'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 22:28:40'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:30:22'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 22:51:39'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 23:00:13'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-07-24 23:05:18'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:05:23'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 23:10:58');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:11:01'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:23:16'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 23:23:59'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:24:04'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 23:25:33'),
	 ('wangyu','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-24 23:25:43'),
	 ('wangyu','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:26:10'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:41:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-24 23:43:32'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-24 23:43:42');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:13:35'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 00:13:41'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:13:45'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 00:14:32'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:14:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:19:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:34:12'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 00:34:40'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:34:44'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:56:39');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 00:57:02'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:57:06'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 00:57:51'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 00:57:55'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:05:50'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 01:06:38'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:06:43'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 01:07:01'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:07:04'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 01:07:31');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liufeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-25 01:07:36'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:08:30'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 01:09:02'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:09:05'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 01:09:35'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 01:09:49'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-25 10:32:07'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 14:30:24'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 14:31:07'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 14:31:13');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 15:09:28'),
	 ('liulei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 15:09:50'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 15:09:53'),
	 ('zhangfeifei','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-07-25 15:10:25'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-25 15:10:29'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-07-25 15:10:51'),
	 ('wangliang','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 15:11:32'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-25 15:38:14'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 15:42:03'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Windows 10','0','登录成功','2025-07-25 16:25:07');
INSERT INTO `base-oa`.sys_logininfor (user_name,ipaddr,login_location,browser,os,status,msg,login_time) VALUES
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-07-25 16:37:15'),
	 ('admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-07-25 16:37:17');
