INSERT INTO `base-oa`.oa_workflow_definition (workflow_name,workflow_key,workflow_version,description,bpmn_xml,deployment_id,process_definition_id,status,create_by,create_time,update_by,update_time,remark) VALUES
	 ('收文审批流程','document_receive_approval',1,'收文审批流程','<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef">
    <process id="document_receive_approval" name="收文审批流程" isExecutable="true">
        <!-- 添加流程结束监听器 -->
        <extensionElements>
            <flowable:executionListener event="end" class="com.base.oa.workflow.listener.ProcessEndListener"/>
        </extensionElements>

        <startEvent id="StartEvent_1" name="开始">
            <outgoing>Flow_start_to_register</outgoing>
        </startEvent>

        <userTask id="Task_office_register" name="办公室主任登记" flowable:assignee="lisi" flowable:candidateUsers="" flowable:candidateGroups="">
            <!-- 添加任务完成监听器 -->
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_start_to_register</incoming>
            <outgoing>Flow_register_to_secretary</outgoing>
        </userTask>

        <userTask id="Task_secretary_approval" name="书记审批" flowable:assignee="wangwu" flowable:candidateUsers="" flowable:candidateGroups="">
            <!-- 添加任务完成监听器 -->
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_register_to_secretary</incoming>
            <outgoing>Flow_secretary_to_parallel</outgoing>
        </userTask>

        <inclusiveGateway id="Gateway_parallel_start" name="包含分发">
            <incoming>Flow_secretary_to_parallel</incoming>
            <outgoing>Flow_to_dept1</outgoing>
            <outgoing>Flow_to_dept2</outgoing>
            <outgoing>Flow_to_dept3</outgoing>
            <outgoing>Flow_to_dept4</outgoing>
            <outgoing>Flow_to_dept5</outgoing>
        </inclusiveGateway>

        <userTask id="Task_dept1_leader" name="分管领导1审阅" flowable:candidateGroups="dept1_leaders" flowable:assignee="zhangsan" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_to_dept1</incoming>
            <outgoing>Flow_dept1_to_staff1</outgoing>
        </userTask>

        <userTask id="Task_dept1_staff" name="科室负责人A1确认" flowable:candidateGroups="dept1_staff" flowable:assignee="liulei" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_dept1_to_staff1</incoming>
            <outgoing>Flow_dept1_to_join</outgoing>
        </userTask>

        <userTask id="Task_dept2_leader" name="分管领导2审阅" flowable:candidateGroups="dept2_leaders" flowable:assignee="liuer" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_to_dept2</incoming>
            <outgoing>Flow_dept2_to_staff2</outgoing>
        </userTask>

        <userTask id="Task_dept2_staff" name="科室负责人A2确认" flowable:candidateGroups="dept2_staff" flowable:assignee="wangliang" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_dept2_to_staff2</incoming>
            <outgoing>Flow_dept2_to_join</outgoing>
        </userTask>

        <userTask id="Task_dept3_leader" name="分管领导3审阅" flowable:candidateGroups="dept3_leaders" flowable:assignee="wutao" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_to_dept3</incoming>
            <outgoing>Flow_dept3_to_staff3</outgoing>
        </userTask>

        <userTask id="Task_dept3_staff" name="科室负责人A3确认" flowable:candidateGroups="dept3_staff" flowable:assignee="qianqi" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_dept3_to_staff3</incoming>
            <outgoing>Flow_dept3_to_join</outgoing>
        </userTask>

        <userTask id="Task_dept4_leader" name="分管领导4审阅" flowable:candidateGroups="dept4_leaders" flowable:assignee="wangyu" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_to_dept4</incoming>
            <outgoing>Flow_dept4_to_staff4</outgoing>
        </userTask>

        <userTask id="Task_dept4_staff" name="科室负责人A4确认" flowable:candidateGroups="dept4_staff" flowable:assignee="lisi">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_dept4_to_staff4</incoming>
            <outgoing>Flow_dept4_to_join</outgoing>
        </userTask>

        <userTask id="Task_dept5_leader" name="分管领导5审阅" flowable:candidateGroups="dept5_leaders" flowable:assignee="yangyang" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_to_dept5</incoming>
            <outgoing>Flow_dept5_to_staff5</outgoing>
        </userTask>

        <userTask id="Task_dept5_staff" name="科室负责人A5确认" flowable:candidateGroups="dept5_staff" flowable:assignee="sunzhida" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_dept5_to_staff5</incoming>
            <outgoing>Flow_dept5_to_join</outgoing>
        </userTask>

        <inclusiveGateway id="Gateway_parallel_join" name="包含汇聚">
            <incoming>Flow_dept1_to_join</incoming>
            <incoming>Flow_dept2_to_join</incoming>
            <incoming>Flow_dept3_to_join</incoming>
            <incoming>Flow_dept4_to_join</incoming>
            <incoming>Flow_dept5_to_join</incoming>
            <outgoing>Flow_join_to_review</outgoing>
        </inclusiveGateway>

        <userTask id="Task_office_review" name="办公室工作人员复核" flowable:candidateGroups="office_staff" flowable:assignee="zhaoyi" flowable:candidateUsers="">
            <extensionElements>
                <flowable:taskListener event="complete" class="com.base.oa.workflow.listener.TaskCompleteListener"/>
            </extensionElements>
            <incoming>Flow_join_to_review</incoming>
            <outgoing>Flow_review_to_end</outgoing>
        </userTask>

        <endEvent id="EndEvent_1" name="流程结束">
            <incoming>Flow_review_to_end</incoming>
        </endEvent>

        <!-- 所有的sequenceFlow -->
        <sequenceFlow id="Flow_start_to_register" sourceRef="StartEvent_1" targetRef="Task_office_register" />
        <sequenceFlow id="Flow_register_to_secretary" sourceRef="Task_office_register" targetRef="Task_secretary_approval" />
        <sequenceFlow id="Flow_secretary_to_parallel" sourceRef="Task_secretary_approval" targetRef="Gateway_parallel_start" />

        <!-- 带条件的包含分支 -->
        <sequenceFlow id="Flow_to_dept1" sourceRef="Gateway_parallel_start" targetRef="Task_dept1_leader">
            <conditionExpression xsi:type="tFormalExpression">
                ${selectedLeaders != null && selectedLeaders.indexOf(''zhangsan'') != -1}
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_to_dept2" sourceRef="Gateway_parallel_start" targetRef="Task_dept2_leader">
            <conditionExpression xsi:type="tFormalExpression">
                ${selectedLeaders != null && selectedLeaders.indexOf(''liuer'') != -1}
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_to_dept3" sourceRef="Gateway_parallel_start" targetRef="Task_dept3_leader">
            <conditionExpression xsi:type="tFormalExpression">
                ${selectedLeaders != null && selectedLeaders.indexOf(''wutao'') != -1}
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_to_dept4" sourceRef="Gateway_parallel_start" targetRef="Task_dept4_leader">
            <conditionExpression xsi:type="tFormalExpression">
                ${selectedLeaders != null && selectedLeaders.indexOf(''wangyu'') != -1}
            </conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="Flow_to_dept5" sourceRef="Gateway_parallel_start" targetRef="Task_dept5_leader">
            <conditionExpression xsi:type="tFormalExpression">
                ${selectedLeaders != null && selectedLeaders.indexOf(''yangyang'') != -1}
            </conditionExpression>
        </sequenceFlow>

        <sequenceFlow id="Flow_dept1_to_staff1" sourceRef="Task_dept1_leader" targetRef="Task_dept1_staff" />
        <sequenceFlow id="Flow_dept2_to_staff2" sourceRef="Task_dept2_leader" targetRef="Task_dept2_staff" />
        <sequenceFlow id="Flow_dept3_to_staff3" sourceRef="Task_dept3_leader" targetRef="Task_dept3_staff" />
        <sequenceFlow id="Flow_dept4_to_staff4" sourceRef="Task_dept4_leader" targetRef="Task_dept4_staff" />
        <sequenceFlow id="Flow_dept5_to_staff5" sourceRef="Task_dept5_leader" targetRef="Task_dept5_staff" />
        <sequenceFlow id="Flow_dept1_to_join" sourceRef="Task_dept1_staff" targetRef="Gateway_parallel_join" />
        <sequenceFlow id="Flow_dept2_to_join" sourceRef="Task_dept2_staff" targetRef="Gateway_parallel_join" />
        <sequenceFlow id="Flow_dept3_to_join" sourceRef="Task_dept3_staff" targetRef="Gateway_parallel_join" />
        <sequenceFlow id="Flow_dept4_to_join" sourceRef="Task_dept4_staff" targetRef="Gateway_parallel_join" />
        <sequenceFlow id="Flow_dept5_to_join" sourceRef="Task_dept5_staff" targetRef="Gateway_parallel_join" />
        <sequenceFlow id="Flow_join_to_review" sourceRef="Gateway_parallel_join" targetRef="Task_office_review" />
        <sequenceFlow id="Flow_review_to_end" sourceRef="Task_office_review" targetRef="EndEvent_1" />
    </process>

    <!-- 流程图定义 -->
    <bpmndi:BPMNDiagram id="BPMNDiagram_document_receive_approval">
        <bpmndi:BPMNPlane bpmnElement="document_receive_approval" id="BPMNPlane_document_receive_approval">

            <!-- 开始事件 -->
            <bpmndi:BPMNShape bpmnElement="StartEvent_1" id="BPMNShape_StartEvent_1">
                <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="200.0"/>
            </bpmndi:BPMNShape>

            <!-- 办公室主任登记 -->
            <bpmndi:BPMNShape bpmnElement="Task_office_register" id="BPMNShape_Task_office_register">
                <omgdc:Bounds height="80.0" width="100.0" x="180.0" y="175.0"/>
            </bpmndi:BPMNShape>

            <!-- 书记审批 -->
            <bpmndi:BPMNShape bpmnElement="Task_secretary_approval" id="BPMNShape_Task_secretary_approval">
                <omgdc:Bounds height="80.0" width="100.0" x="320.0" y="175.0"/>
            </bpmndi:BPMNShape>

            <!-- 包含分发网关 -->
            <bpmndi:BPMNShape bpmnElement="Gateway_parallel_start" id="BPMNShape_Gateway_parallel_start">
                <omgdc:Bounds height="40.0" width="40.0" x="470.0" y="195.0"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导1审阅 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept1_leader" id="BPMNShape_Task_dept1_leader">
                <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="50.0"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人A1确认 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept1_staff" id="BPMNShape_Task_dept1_staff">
                <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="50.0"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导2审阅 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept2_leader" id="BPMNShape_Task_dept2_leader">
                <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="140.0"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人A2确认 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept2_staff" id="BPMNShape_Task_dept2_staff">
                <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="140.0"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导3审阅 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept3_leader" id="BPMNShape_Task_dept3_leader">
                <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="230.0"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人A3确认 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept3_staff" id="BPMNShape_Task_dept3_staff">
                <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="230.0"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导4审阅 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept4_leader" id="BPMNShape_Task_dept4_leader">
                <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="320.0"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人A4确认 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept4_staff" id="BPMNShape_Task_dept4_staff">
                <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="320.0"/>
            </bpmndi:BPMNShape>

            <!-- 分管领导5审阅 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept5_leader" id="BPMNShape_Task_dept5_leader">
                <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="410.0"/>
            </bpmndi:BPMNShape>

            <!-- 科室负责人A5确认 -->
            <bpmndi:BPMNShape bpmnElement="Task_dept5_staff" id="BPMNShape_Task_dept5_staff">
                <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="410.0"/>
            </bpmndi:BPMNShape>

            <!-- 包含汇聚网关 -->
            <bpmndi:BPMNShape bpmnElement="Gateway_parallel_join" id="BPMNShape_Gateway_parallel_join">
                <omgdc:Bounds height="40.0" width="40.0" x="870.0" y="195.0"/>
            </bpmndi:BPMNShape>

            <!-- 办公室工作人员复核 -->
            <bpmndi:BPMNShape bpmnElement="Task_office_review" id="BPMNShape_Task_office_review">
                <omgdc:Bounds height="80.0" width="100.0" x="960.0" y="175.0"/>
            </bpmndi:BPMNShape>

            <!-- 结束事件 -->
            <bpmndi:BPMNShape bpmnElement="EndEvent_1" id="BPMNShape_EndEvent_1">
                <omgdc:Bounds height="30.0" width="30.0" x="1110.0" y="200.0"/>
            </bpmndi:BPMNShape>

            <!-- 连接线定义 -->
            <!-- 开始到办公室主任登记 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_start_to_register" id="BPMNEdge_Flow_start_to_register">
                <omgdi:waypoint x="130.0" y="215.0"/>
                <omgdi:waypoint x="180.0" y="215.0"/>
            </bpmndi:BPMNEdge>

            <!-- 办公室主任登记到书记审批 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_register_to_secretary" id="BPMNEdge_Flow_register_to_secretary">
                <omgdi:waypoint x="280.0" y="215.0"/>
                <omgdi:waypoint x="320.0" y="215.0"/>
            </bpmndi:BPMNEdge>

            <!-- 书记审批到包含分发 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_secretary_to_parallel" id="BPMNEdge_Flow_secretary_to_parallel">
                <omgdi:waypoint x="420.0" y="215.0"/>
                <omgdi:waypoint x="470.0" y="215.0"/>
            </bpmndi:BPMNEdge>

            <!-- 包含分发到各部门领导 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_to_dept1" id="BPMNEdge_Flow_to_dept1">
                <omgdi:waypoint x="490.0" y="195.0"/>
                <omgdi:waypoint x="490.0" y="90.0"/>
                <omgdi:waypoint x="580.0" y="90.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_to_dept2" id="BPMNEdge_Flow_to_dept2">
                <omgdi:waypoint x="490.0" y="195.0"/>
                <omgdi:waypoint x="490.0" y="180.0"/>
                <omgdi:waypoint x="580.0" y="180.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_to_dept3" id="BPMNEdge_Flow_to_dept3">
                <omgdi:waypoint x="510.0" y="215.0"/>
                <omgdi:waypoint x="545.0" y="215.0"/>
                <omgdi:waypoint x="545.0" y="270.0"/>
                <omgdi:waypoint x="580.0" y="270.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_to_dept4" id="BPMNEdge_Flow_to_dept4">
                <omgdi:waypoint x="490.0" y="235.0"/>
                <omgdi:waypoint x="490.0" y="360.0"/>
                <omgdi:waypoint x="580.0" y="360.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_to_dept5" id="BPMNEdge_Flow_to_dept5">
                <omgdi:waypoint x="490.0" y="235.0"/>
                <omgdi:waypoint x="490.0" y="450.0"/>
                <omgdi:waypoint x="580.0" y="450.0"/>
            </bpmndi:BPMNEdge>

            <!-- 各部门领导到科室负责人 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_dept1_to_staff1" id="BPMNEdge_Flow_dept1_to_staff1">
                <omgdi:waypoint x="680.0" y="90.0"/>
                <omgdi:waypoint x="720.0" y="90.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept2_to_staff2" id="BPMNEdge_Flow_dept2_to_staff2">
                <omgdi:waypoint x="680.0" y="180.0"/>
                <omgdi:waypoint x="720.0" y="180.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept3_to_staff3" id="BPMNEdge_Flow_dept3_to_staff3">
                <omgdi:waypoint x="680.0" y="270.0"/>
                <omgdi:waypoint x="720.0" y="270.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept4_to_staff4" id="BPMNEdge_Flow_dept4_to_staff4">
                <omgdi:waypoint x="680.0" y="360.0"/>
                <omgdi:waypoint x="720.0" y="360.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept5_to_staff5" id="BPMNEdge_Flow_dept5_to_staff5">
                <omgdi:waypoint x="680.0" y="450.0"/>
                <omgdi:waypoint x="720.0" y="450.0"/>
            </bpmndi:BPMNEdge>

            <!-- 各科室负责人到包含汇聚 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_dept1_to_join" id="BPMNEdge_Flow_dept1_to_join">
                <omgdi:waypoint x="820.0" y="90.0"/>
                <omgdi:waypoint x="890.0" y="90.0"/>
                <omgdi:waypoint x="890.0" y="195.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept2_to_join" id="BPMNEdge_Flow_dept2_to_join">
                <omgdi:waypoint x="820.0" y="180.0"/>
                <omgdi:waypoint x="870.0" y="180.0"/>
                <omgdi:waypoint x="870.0" y="195.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept3_to_join" id="BPMNEdge_Flow_dept3_to_join">
                <omgdi:waypoint x="820.0" y="270.0"/>
                <omgdi:waypoint x="845.0" y="270.0"/>
                <omgdi:waypoint x="845.0" y="215.0"/>
                <omgdi:waypoint x="870.0" y="215.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept4_to_join" id="BPMNEdge_Flow_dept4_to_join">
                <omgdi:waypoint x="820.0" y="360.0"/>
                <omgdi:waypoint x="890.0" y="360.0"/>
                <omgdi:waypoint x="890.0" y="235.0"/>
            </bpmndi:BPMNEdge>

            <bpmndi:BPMNEdge bpmnElement="Flow_dept5_to_join" id="BPMNEdge_Flow_dept5_to_join">
                <omgdi:waypoint x="820.0" y="450.0"/>
                <omgdi:waypoint x="890.0" y="450.0"/>
                <omgdi:waypoint x="890.0" y="235.0"/>
            </bpmndi:BPMNEdge>

            <!-- 包含汇聚到办公室复核 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_join_to_review" id="BPMNEdge_Flow_join_to_review">
                <omgdi:waypoint x="910.0" y="215.0"/>
                <omgdi:waypoint x="960.0" y="215.0"/>
            </bpmndi:BPMNEdge>

            <!-- 办公室复核到结束 -->
            <bpmndi:BPMNEdge bpmnElement="Flow_review_to_end" id="BPMNEdge_Flow_review_to_end">
                <omgdi:waypoint x="1060.0" y="215.0"/>
                <omgdi:waypoint x="1110.0" y="215.0"/>
            </bpmndi:BPMNEdge>

        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</definitions>','',NULL,'0','admin','2025-07-05 22:39:51','admin','2025-07-17 00:10:07',NULL),
	 ('收文特办流程','document_receive_special',1,'收文特办流程','<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:flowable="http://flowable.org/bpmn" id="Definitions_2" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="document_receive_special" name="收文特办流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_2" name="开始">
      <bpmn:outgoing>Flow_start_to_secretary_receive</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_secretary_receive" name="书记收文" flowable:assignee="wangwu" flowable:candidateUsers="" flowable:candidateGroups="">
      <bpmn:extensionElements>
        <flowable:formProperty id="nextAssignee" name="下一个处理人" type="string" required="true">
          <flowable:value id="liulei" name="刘磊" />
          <flowable:value id="zhangsan" name="张三" />
          <flowable:value id="lisi" name="李四" />
        </flowable:formProperty>
        <flowable:formProperty id="assignReason" name="指派原因" type="string" />
        <flowable:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <flowable:value id="approve" name="同意" />
          <flowable:value id="reject" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审批意见" type="string" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_start_to_secretary_receive</bpmn:incoming>
      <bpmn:outgoing>Flow_secretary_to_assign</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_assign_to_anyone" name="分发给任意人员" flowable:assignee="${nextAssignee}" flowable:candidateUsers="" flowable:candidateGroups="">
      <bpmn:extensionElements>
        <flowable:formProperty id="processResult" name="处理结果" type="enum" required="true">
          <flowable:value id="complete" name="完成" />
          <flowable:value id="return" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="processOpinion" name="处理意见" type="string" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_secretary_to_assign</bpmn:incoming>
      <bpmn:outgoing>Flow_assign_to_end</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_2" name="结束">
      <bpmn:extensionElements>
        <flowable:executionListener event="start" delegateExpression="${processEndListener}" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_assign_to_end</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_start_to_secretary_receive" sourceRef="StartEvent_2" targetRef="Task_secretary_receive" />
    <bpmn:sequenceFlow id="Flow_secretary_to_assign" sourceRef="Task_secretary_receive" targetRef="Task_assign_to_anyone" />
    <bpmn:sequenceFlow id="Flow_assign_to_end" sourceRef="Task_assign_to_anyone" targetRef="EndEvent_2" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_2">
    <bpmndi:BPMNPlane id="BPMNPlane_2" bpmnElement="document_receive_special">
      <bpmndi:BPMNShape id="StartEvent_2_di" bpmnElement="StartEvent_2">
        <dc:Bounds x="152" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="145" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_secretary_receive_di" bpmnElement="Task_secretary_receive">
        <dc:Bounds x="240" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_assign_to_anyone_di" bpmnElement="Task_assign_to_anyone">
        <dc:Bounds x="390" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_2_di" bpmnElement="EndEvent_2">
        <dc:Bounds x="542" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="550" y="145" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_start_to_secretary_receive_di" bpmnElement="Flow_start_to_secretary_receive">
        <di:waypoint x="188" y="120" />
        <di:waypoint x="240" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_secretary_to_assign_di" bpmnElement="Flow_secretary_to_assign">
        <di:waypoint x="340" y="120" />
        <di:waypoint x="390" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_assign_to_end_di" bpmnElement="Flow_assign_to_end">
        <di:waypoint x="490" y="120" />
        <di:waypoint x="542" y="120" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
','81961c31-63ee-11f0-97eb-8293a7707bc6',NULL,'0','admin','2025-07-05 22:47:59','admin','2025-07-18 23:47:27',NULL),
	 ('新建流程','Process_1753059833525',1,'','<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="Process_1753059833525" name="新建流程" isExecutable="true">
    <bpmn:startEvent id="Event_0yxwu37" name="开始事件" />
    <bpmn:userTask id="Activity_19z2ha9" name="科室负责人" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1753059833525">
      <bpmndi:BPMNShape id="Event_0yxwu37_di" bpmnElement="Event_0yxwu37">
        <dc:Bounds x="512" y="42" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="508" y="81" width="44" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_19z2ha9_di" bpmnElement="Activity_19z2ha9">
        <dc:Bounds x="480" y="140" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
','4da2b6a3-65cf-11f0-9fd0-00ff85396404',NULL,'0','admin','2025-07-21 09:09:08','',NULL,NULL),
	 ('发文审批流程','send_document_approval',1,'发文审批流程：科员 → 科室负责人 → 办公室科员 → 办公室负责人 → 分管领导(可选) → 书记','<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:flowable="http://flowable.org/bpmn" targetNamespace="http://www.flowable.org/processdef">
  <process id="send_document_approval" name="发文审批流程" isExecutable="true">
    <documentation>发文审批流程：科员 → 科室负责人 → 办公室科员 → 办公室负责人 → 分管领导(可选) → 书记</documentation>
    <startEvent id="startEvent" name="开始" />
    <userTask id="deptManagerApproval" name="科室负责人审批" flowable:assignee="${deptManager}">
      <documentation>科室负责人对发文进行审批</documentation>
      <extensionElements>
        <flowable:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <flowable:value id="approve" name="同意" />
          <flowable:value id="reject" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审批意见" type="string" />
      </extensionElements>
    </userTask>
    <userTask id="officeClerkReview" name="办公室科员审核" flowable:candidateGroups="clerk">
      <documentation>办公室科员对发文进行审核</documentation>
      <extensionElements>
        <flowable:formProperty id="approvalResult" name="审核结果" type="enum" required="true">
          <flowable:value id="approve" name="通过" />
          <flowable:value id="reject" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审核意见" type="string" />
      </extensionElements>
    </userTask>
    <userTask id="officeManagerReview" name="办公室负责人审核" flowable:assignee="${officeManager}">
      <documentation>办公室负责人对发文进行审核</documentation>
      <extensionElements>
        <flowable:formProperty id="approvalResult" name="审核结果" type="enum" required="true">
          <flowable:value id="approve" name="通过" />
          <flowable:value id="reject" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审核意见" type="string" />
      </extensionElements>
    </userTask>
    <userTask id="supervisingLeaderApproval" name="分管领导审批" flowable:candidateGroups="fgld">
      <documentation>分管领导对发文进行审批</documentation>
      <extensionElements>
        <flowable:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <flowable:value id="approve" name="同意" />
          <flowable:value id="reject" name="退回" />
          <flowable:value id="skip" name="跳过" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审批意见" type="string" />
      </extensionElements>
    </userTask>
    <userTask id="secretaryApproval" name="书记审批" flowable:candidateGroups="sj">
      <documentation>书记对发文进行最终审批</documentation>
      <extensionElements>
        <flowable:formProperty id="approvalResult" name="审批结果" type="enum" required="true">
          <flowable:value id="approve" name="同意" />
          <flowable:value id="reject" name="退回" />
        </flowable:formProperty>
        <flowable:formProperty id="approvalOpinion" name="审批意见" type="string" />
      </extensionElements>
    </userTask>
    <serviceTask id="approvalComplete" name="审批完成" flowable:class="com.base.oa.workflow.listener.DocumentApprovalCompleteListener">
      <documentation>发文审批完成，更新文档状态</documentation>
    </serviceTask>
    <serviceTask id="approvalReject" name="审批退回" flowable:class="com.base.oa.workflow.listener.DocumentApprovalRejectListener">
      <documentation>发文审批退回，更新文档状态</documentation>
    </serviceTask>
    <endEvent id="endEvent" name="结束" />
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="deptManagerApproval" />
    <exclusiveGateway id="deptApprovalGateway" />
    <sequenceFlow id="flow2" sourceRef="deptManagerApproval" targetRef="deptApprovalGateway" />
    <sequenceFlow id="flow3" sourceRef="deptApprovalGateway" targetRef="officeClerkReview">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''approve''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow4" sourceRef="deptApprovalGateway" targetRef="approvalReject">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''reject''}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="officeClerkGateway" />
    <sequenceFlow id="flow5" sourceRef="officeClerkReview" targetRef="officeClerkGateway" />
    <sequenceFlow id="flow6" sourceRef="officeClerkGateway" targetRef="officeManagerReview">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''approve''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow7" sourceRef="officeClerkGateway" targetRef="approvalReject">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''reject''}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="officeManagerGateway" />
    <sequenceFlow id="flow8" sourceRef="officeManagerReview" targetRef="officeManagerGateway" />
    <sequenceFlow id="flow9" sourceRef="officeManagerGateway" targetRef="supervisingLeaderApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''approve''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow10" sourceRef="officeManagerGateway" targetRef="approvalReject">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''reject''}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="supervisingLeaderGateway" />
    <sequenceFlow id="flow11" sourceRef="supervisingLeaderApproval" targetRef="supervisingLeaderGateway" />
    <sequenceFlow id="flow12" sourceRef="supervisingLeaderGateway" targetRef="secretaryApproval">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''approve'' || approvalResult == ''skip''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow13" sourceRef="supervisingLeaderGateway" targetRef="approvalReject">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''reject''}</conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="secretaryGateway" />
    <sequenceFlow id="flow14" sourceRef="secretaryApproval" targetRef="secretaryGateway" />
    <sequenceFlow id="flow15" sourceRef="secretaryGateway" targetRef="approvalComplete">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''approve''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow16" sourceRef="secretaryGateway" targetRef="approvalReject">
      <conditionExpression xsi:type="tFormalExpression">${approvalResult == ''reject''}</conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="flow17" sourceRef="approvalComplete" targetRef="endEvent" />
    <sequenceFlow id="flow18" sourceRef="approvalReject" targetRef="endEvent" />
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_send_document_approval">
    <bpmndi:BPMNPlane id="BPMNPlane_send_document_approval" bpmnElement="send_document_approval">
      <bpmndi:BPMNShape id="BPMNShape_startEvent" bpmnElement="startEvent">
        <omgdc:Bounds x="100" y="163" width="30" height="30" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_deptManagerApproval" bpmnElement="deptManagerApproval">
        <omgdc:Bounds x="180" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_officeClerkReview" bpmnElement="officeClerkReview">
        <omgdc:Bounds x="420" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_officeManagerReview" bpmnElement="officeManagerReview">
        <omgdc:Bounds x="660" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_supervisingLeaderApproval" bpmnElement="supervisingLeaderApproval">
        <omgdc:Bounds x="900" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_secretaryApproval" bpmnElement="secretaryApproval">
        <omgdc:Bounds x="1140" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_approvalComplete" bpmnElement="approvalComplete">
        <omgdc:Bounds x="1380" y="138" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_approvalReject" bpmnElement="approvalReject">
        <omgdc:Bounds x="1380" y="258" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_endEvent" bpmnElement="endEvent">
        <omgdc:Bounds x="1530" y="163" width="30" height="30" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_deptApprovalGateway" bpmnElement="deptApprovalGateway" isMarkerVisible="true">
        <omgdc:Bounds x="330" y="158" width="40" height="40" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_officeClerkGateway" bpmnElement="officeClerkGateway" isMarkerVisible="true">
        <omgdc:Bounds x="570" y="158" width="40" height="40" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_officeManagerGateway" bpmnElement="officeManagerGateway" isMarkerVisible="true">
        <omgdc:Bounds x="810" y="158" width="40" height="40" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_supervisingLeaderGateway" bpmnElement="supervisingLeaderGateway" isMarkerVisible="true">
        <omgdc:Bounds x="1050" y="158" width="40" height="40" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_secretaryGateway" bpmnElement="secretaryGateway" isMarkerVisible="true">
        <omgdc:Bounds x="1290" y="158" width="40" height="40" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="BPMNEdge_flow1" bpmnElement="flow1">
        <omgdi:waypoint x="130" y="178" />
        <omgdi:waypoint x="180" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow2" bpmnElement="flow2">
        <omgdi:waypoint x="280" y="178" />
        <omgdi:waypoint x="330" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow3" bpmnElement="flow3">
        <omgdi:waypoint x="370" y="178" />
        <omgdi:waypoint x="420" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow4" bpmnElement="flow4">
        <omgdi:waypoint x="350" y="198" />
        <omgdi:waypoint x="350" y="298" />
        <omgdi:waypoint x="1380" y="298" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow5" bpmnElement="flow5">
        <omgdi:waypoint x="520" y="178" />
        <omgdi:waypoint x="570" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow6" bpmnElement="flow6">
        <omgdi:waypoint x="610" y="178" />
        <omgdi:waypoint x="660" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow7" bpmnElement="flow7">
        <omgdi:waypoint x="590" y="198" />
        <omgdi:waypoint x="590" y="298" />
        <omgdi:waypoint x="1380" y="298" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow8" bpmnElement="flow8">
        <omgdi:waypoint x="760" y="178" />
        <omgdi:waypoint x="810" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow9" bpmnElement="flow9">
        <omgdi:waypoint x="850" y="178" />
        <omgdi:waypoint x="900" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow10" bpmnElement="flow10">
        <omgdi:waypoint x="830" y="198" />
        <omgdi:waypoint x="830" y="298" />
        <omgdi:waypoint x="1380" y="298" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow11" bpmnElement="flow11">
        <omgdi:waypoint x="1000" y="178" />
        <omgdi:waypoint x="1050" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow12" bpmnElement="flow12">
        <omgdi:waypoint x="1090" y="178" />
        <omgdi:waypoint x="1140" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow13" bpmnElement="flow13">
        <omgdi:waypoint x="1070" y="198" />
        <omgdi:waypoint x="1070" y="298" />
        <omgdi:waypoint x="1380" y="298" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow14" bpmnElement="flow14">
        <omgdi:waypoint x="1240" y="178" />
        <omgdi:waypoint x="1290" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow15" bpmnElement="flow15">
        <omgdi:waypoint x="1330" y="178" />
        <omgdi:waypoint x="1380" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow16" bpmnElement="flow16">
        <omgdi:waypoint x="1310" y="198" />
        <omgdi:waypoint x="1310" y="298" />
        <omgdi:waypoint x="1380" y="298" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow17" bpmnElement="flow17">
        <omgdi:waypoint x="1480" y="178" />
        <omgdi:waypoint x="1530" y="178" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNEdge_flow18" bpmnElement="flow18">
        <omgdi:waypoint x="1480" y="298" />
        <omgdi:waypoint x="1545" y="298" />
        <omgdi:waypoint x="1545" y="193" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
','e7d4d446-6920-11f0-bd41-06d3995aa5bb',NULL,'0','admin','2025-07-21 15:34:14','admin','2025-07-25 14:30:50','发文审批流程，包含科员、科室负责人、办公室科员、办公室负责人、分管领导(可选)、书记等审批环节');
