const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:8080';

// 全局token
let authToken = '';

// 登录获取token
async function login() {
    try {
        console.log('🔐 正在登录获取认证token...');
        
        const loginData = {
            username: 'admin',
            password: 'admin123',
            code: '',
            uuid: ''
        };

        const response = await axios.post(`${BASE_URL}/login`, loginData);
        
        if (response.data.code === 200) {
            authToken = response.data.token;
            console.log('✅ 登录成功，获取到token');
            
            // 设置默认请求头
            axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
            return true;
        } else {
            console.log('❌ 登录失败:', response.data.msg);
            return false;
        }
    } catch (error) {
        console.error('❌ 登录过程中发生错误:', error.message);
        return false;
    }
}

// 测试分管领导审批时选择科室负责人功能
async function testLeaderApprovalManagers() {
    console.log('🚀 开始测试分管领导审批时选择科室负责人功能...\n');

    // 先登录
    const loginSuccess = await login();
    if (!loginSuccess) {
        console.log('❌ 无法继续测试，登录失败');
        return;
    }

    console.log('');

    try {
        // 1. 测试获取分管领导列表
        console.log('🔍 测试获取分管领导列表');
        const leadersResponse = await axios.get(`${BASE_URL}/oa/workflow/personnel/leaders`);
        
        if (leadersResponse.data.code === 200) {
            const leaders = leadersResponse.data.data;
            console.log(`✅ 获取到 ${leaders.length} 个分管领导:`);
            leaders.forEach(leader => {
                console.log(`   - ${leader.nickName} (${leader.userName}) - ${leader.deptName}`);
            });

            console.log('');

            // 2. 测试每个分管领导的科室负责人查询
            console.log('🔍 测试分管领导的科室负责人查询功能');
            
            for (const leader of leaders) {
                console.log(`\n📋 测试分管领导: ${leader.nickName} (${leader.userName})`);
                
                try {
                    const managersResponse = await axios.get(`${BASE_URL}/oa/workflow/personnel/managers/${leader.userName}`);
                    
                    if (managersResponse.data.code === 200) {
                        const managers = managersResponse.data.data;
                        console.log(`   ✅ 获取到 ${managers.length} 个科室负责人:`);
                        
                        if (managers.length > 0) {
                            managers.forEach(manager => {
                                console.log(`       - ${manager.nickName} (${manager.userName}) - ${manager.deptName}`);
                            });
                        } else {
                            console.log('       (该分管领导暂无管理的科室负责人)');
                        }
                    } else {
                        console.log(`   ❌ 获取科室负责人失败: ${managersResponse.data.msg}`);
                    }
                } catch (error) {
                    console.log(`   ❌ 请求科室负责人接口失败: ${error.message}`);
                }
            }

            console.log('\n============================================================');
            console.log('🎉 测试完成！');
            
            console.log('\n📋 功能验证总结:');
            console.log('1. ✅ 分管领导列表获取正常');
            console.log('2. ✅ 科室负责人关联查询功能正常');
            console.log('3. ✅ 前端审批页面已添加分管领导审批时的科室负责人选择功能');
            
            console.log('\n🔧 新增功能说明:');
            console.log('- 在审批页面添加了"指派科室负责人"下拉框');
            console.log('- 当任务名称为"分管领导审批"且流程为收文审批流程时显示');
            console.log('- 支持多选科室负责人');
            console.log('- 自动加载当前分管领导管理的科室负责人列表');
            console.log('- 添加了表单验证，确保至少选择一个科室负责人');
            
            console.log('\n📝 使用场景:');
            console.log('1. 书记审批 → 选择分管领导 → 分管领导审批 → 选择科室负责人');
            console.log('2. 分管领导登录系统，在待办任务中看到"分管领导审批"任务');
            console.log('3. 点击审批，页面显示"指派科室负责人"下拉框');
            console.log('4. 下拉框中只显示该分管领导管理的科室负责人');
            console.log('5. 选择一个或多个科室负责人后提交审批');
            console.log('6. 工作流继续流转到选中的科室负责人');

        } else {
            console.log('❌ 获取分管领导列表失败:', leadersResponse.data.msg);
        }

    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
        if (error.response) {
            console.error('   响应状态:', error.response.status);
            console.error('   响应数据:', error.response.data);
        }
    }
}

// 运行测试
testLeaderApprovalManagers();
