/**
 * 收文普通流程完整自动化测试
 * 
 * 测试收文普通审批流程的完整链路：
 * 1. 办公室收文登记（自动完成）
 * 2. 办公室主任审批
 * 3. 书记审批（选择分管领导）
 * 4. 分管领导并行审批（选择科室负责人）
 * 5. 科室负责人并行审批（选择经办人）
 * 6. 经办人查阅确认
 * 7. 办公室汇总
 * 
 * 包含多种测试场景：
 * - 正常收文流程（8个节点完整流程）
 * - 多级人员选择验证
 * - 权限隔离验证
 * - 异常处理（拒绝、回退、催办）
 * - 性能并发测试
 * - 边界条件测试
 * - 文档状态更新验证
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8080';

// 用户配置
const users = {
    // 办公室人员
    clerk: { username: 'clerk_office1', password: 'admin123', name: '办公室小王' },
    manager: { username: 'manager_office', password: 'admin123', name: '办公室主任' },
    clerkSummary: { username: 'clerk_office2', password: 'admin123', name: '办公室小李' },
    
    // 书记
    secretary: { username: 'secretary', password: 'admin123', name: '王书记' },
    
    // 分管领导
    leaderAdmin: { username: 'leader_admin', password: 'admin123', name: '张副主任（行政）' },
    leaderFinance: { username: 'leader_finance', password: 'admin123', name: '李副主任（财务）' },
    leaderOperation: { username: 'leader_operation', password: 'admin123', name: '赵副主任（运管）' },
    
    // 科室负责人
    managerHr: { username: 'manager_hr', password: 'admin123', name: '人事科长' },
    managerFinance: { username: 'manager_finance', password: 'admin123', name: '财务科长' },
    managerOperation: { username: 'manager_operation', password: 'admin123', name: '运管科长' },
    managerHydro: { username: 'manager_hydro', password: 'admin123', name: '水调科长' },
    
    // 科员
    clerkHr: { username: 'clerk_hr1', password: 'admin123', name: '人事科小赵' },
    clerkOperation: { username: 'clerk_operation1', password: 'admin123', name: '运管科小钱' },
    clerkFinance: { username: 'clerk_finance1', password: 'admin123', name: '财务科小周' },
    clerkHydro: { username: 'clerk_hydro1', password: 'admin123', name: '水调科小孙' }
};

let tokens = {};
let testResults = [];
let testDocuments = []; // 存储测试文档ID用于清理

// 工具函数
async function login(username, password) {
    try {
        const response = await axios.post(`${BASE_URL}/login`, {
            username,
            password,
            code: '1234',
            uuid: 'test-uuid'
        });
        
        if (response.data.code === 200) {
            return response.data.token;
        } else {
            throw new Error(`登录失败: ${response.data.msg}`);
        }
    } catch (error) {
        const errorMsg = error.response?.data?.msg || error.message || '网络连接失败';
        throw new Error(errorMsg);
    }
}

async function makeRequest(method, url, data, token) {
    try {
        const config = {
            method,
            url: `${BASE_URL}${url}`,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        };
        
        if (data) {
            config.data = data;
        }
        
        const response = await axios(config);
        return response.data;
    } catch (error) {
        const errorMsg = error.response?.data?.msg || error.message || '请求失败';
        throw new Error(errorMsg);
    }
}

function addTestResult(testName, success, message, user = '') {
    const status = success ? '✅' : '❌';
    const userInfo = user ? ` (${user})` : '';
    console.log(`${status} ${testName}${userInfo}: ${message}`);
    testResults.push({ testName, success, message, user });
    return { testName, success, message, user };
}

async function waitForTask(userToken, processInstanceId, maxAttempts = 5) {
    for (let i = 0; i < maxAttempts; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const tasksResult = await makeRequest('GET', '/oa/workflow/task/todo', null, userToken);
        if (tasksResult.code === 200) {
            const taskList = tasksResult.rows || tasksResult.data || [];
            const task = taskList.find(t => t.processInstanceId === processInstanceId);
            if (task) {
                return task;
            }
        }
    }
    return null;
}

async function createTestDocument(title) {
    const docData = {
        docTitle: title,
        docNumber: `RECEIVE-TEST-${Date.now()}`,
        sourceUnit: '测试发文单位',
        receiveDate: '2025-01-15',
        urgencyLevel: '4',
        securityLevel: '1',
        docContent: '<p>收文流程测试内容，验证完整的审批链路和人员选择功能。</p>',
        status: '0'
    };
    
    const createResult = await makeRequest('POST', '/oa/document/receive', docData, tokens.clerk);
    if (createResult.code !== 200) {
        throw new Error(`创建收文失败: ${createResult.msg}`);
    }
    
    const docId = createResult.data;
    testDocuments.push(docId);
    
    const submitResult = await makeRequest('POST', `/oa/document/receive/submit/${docId}`, {}, tokens.clerk);
    if (submitResult.code !== 200) {
        throw new Error(`提交审批失败: ${submitResult.msg}`);
    }
    
    const docInfo = await makeRequest('GET', `/oa/document/receive/${docId}`, null, tokens.clerk);
    const processInstanceId = docInfo.data.workflowInstanceId;
    
    return { docId, processInstanceId };
}

// 测试场景1：完整收文流程
async function testCompleteWorkflow() {
    console.log('\n=== 测试场景1：完整收文流程 ===');
    
    try {
        // 1. 创建并提交收文
        const { docId, processInstanceId } = await createTestDocument('完整收文流程测试');
        addTestResult('创建收文', true, `文档ID: ${docId}, 流程实例: ${processInstanceId}`, users.clerk.name);
        addTestResult('自动完成收文登记', true, '收文登记节点已自动完成', users.clerk.name);
        
        // 2. 办公室主任审批
        const managerTask = await waitForTask(tokens.manager, processInstanceId);
        if (managerTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${managerTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '办公室主任审批通过'
            }, tokens.manager);
            if (approvalResult.code === 200) {
                addTestResult('办公室主任审批', true, '审批完成', users.manager.name);
            }
        }
        
        // 3. 书记审批（选择分管领导）
        const secretaryTask = await waitForTask(tokens.secretary, processInstanceId);
        if (secretaryTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${secretaryTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedLeaders: ['leader_admin', 'leader_operation'] // 选择2个分管领导
                },
                comment: '书记审批通过，指定张副主任和赵副主任处理'
            }, tokens.secretary);
            if (approvalResult.code === 200) {
                addTestResult('书记审批', true, '审批完成，已选择分管领导', users.secretary.name);
            }
        }
        
        // 4. 分管领导并行审批
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 张副主任审批
        const adminLeaderTask = await waitForTask(tokens.leaderAdmin, processInstanceId);
        if (adminLeaderTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${adminLeaderTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedManagers: ['manager_hr'] // 选择人事科长
                },
                comment: '张副主任审批通过，指定人事科长处理'
            }, tokens.leaderAdmin);
            if (approvalResult.code === 200) {
                addTestResult('张副主任审批', true, '审批完成，已选择人事科长', users.leaderAdmin.name);
            }
        }
        
        // 赵副主任审批
        const operationLeaderTask = await waitForTask(tokens.leaderOperation, processInstanceId);
        if (operationLeaderTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${operationLeaderTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedManagers: ['manager_operation', 'manager_hydro'] // 选择2个科室负责人
                },
                comment: '赵副主任审批通过，指定运管科长和水调科长处理'
            }, tokens.leaderOperation);
            if (approvalResult.code === 200) {
                addTestResult('赵副主任审批', true, '审批完成，已选择科室负责人', users.leaderOperation.name);
            }
        }
        
        // 5. 科室负责人并行审批
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 人事科长审批
        const hrManagerTask = await waitForTask(tokens.managerHr, processInstanceId);
        if (hrManagerTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${hrManagerTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedHandlers: ['clerk_hr1'] // 选择人事科小赵
                },
                comment: '人事科长审批通过，指定人事科小赵查阅'
            }, tokens.managerHr);
            if (approvalResult.code === 200) {
                addTestResult('人事科长审批', true, '审批完成，已选择经办人', users.managerHr.name);
            }
        }
        
        // 运管科长审批
        const operationManagerTask = await waitForTask(tokens.managerOperation, processInstanceId);
        if (operationManagerTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${operationManagerTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedHandlers: ['clerk_operation1'] // 选择运管科小钱
                },
                comment: '运管科长审批通过，指定运管科小钱查阅'
            }, tokens.managerOperation);
            if (approvalResult.code === 200) {
                addTestResult('运管科长审批', true, '审批完成，已选择经办人', users.managerOperation.name);
            }
        }
        
        // 水调科长审批
        const hydroManagerTask = await waitForTask(tokens.managerHydro, processInstanceId);
        if (hydroManagerTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${hydroManagerTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedHandlers: ['clerk_hydro1'] // 选择水调科小孙
                },
                comment: '水调科长审批通过，指定水调科小孙查阅'
            }, tokens.managerHydro);
            if (approvalResult.code === 200) {
                addTestResult('水调科长审批', true, '审批完成，已选择经办人', users.managerHydro.name);
            }
        }
        
        // 6. 经办人查阅确认
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 人事科小赵查阅
        const hrClerkTask = await waitForTask(tokens.clerkHr, processInstanceId);
        if (hrClerkTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${hrClerkTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '人事科小赵查阅完成'
            }, tokens.clerkHr);
            if (approvalResult.code === 200) {
                addTestResult('人事科小赵查阅', true, '查阅完成', users.clerkHr.name);
            }
        }
        
        // 运管科小钱查阅
        const operationClerkTask = await waitForTask(tokens.clerkOperation, processInstanceId);
        if (operationClerkTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${operationClerkTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '运管科小钱查阅完成'
            }, tokens.clerkOperation);
            if (approvalResult.code === 200) {
                addTestResult('运管科小钱查阅', true, '查阅完成', users.clerkOperation.name);
            }
        }
        
        // 水调科小孙查阅
        const hydroClerkTask = await waitForTask(tokens.clerkHydro, processInstanceId);
        if (hydroClerkTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${hydroClerkTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '水调科小孙查阅完成'
            }, tokens.clerkHydro);
            if (approvalResult.code === 200) {
                addTestResult('水调科小孙查阅', true, '查阅完成', users.clerkHydro.name);
            }
        }
        
        // 7. 办公室汇总
        const summaryTask = await waitForTask(tokens.clerkSummary, processInstanceId);
        if (summaryTask) {
            const approvalResult = await makeRequest('POST', `/oa/workflow/task/complete/${summaryTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '办公室汇总完成'
            }, tokens.clerkSummary);
            if (approvalResult.code === 200) {
                addTestResult('办公室汇总', true, '汇总完成', users.clerkSummary.name);
            }
        }
        
        // 验证文档状态
        await new Promise(resolve => setTimeout(resolve, 2000));
        const finalDocInfo = await makeRequest('GET', `/oa/document/receive/${docId}`, null, tokens.clerk);
        
        if (finalDocInfo.data.status === '3' || finalDocInfo.data.status === '2') {
            addTestResult('文档状态验证', true, `文档状态已更新: ${finalDocInfo.data.status}`, '系统');
        } else {
            addTestResult('文档状态验证', false, `文档状态错误: ${finalDocInfo.data.status}`, '系统');
        }
        
    } catch (error) {
        addTestResult('完整收文流程测试', false, error.message, '系统');
    }
}

// 测试场景2：权限隔离验证
async function testPermissionIsolation() {
    console.log('\n=== 测试场景2：权限隔离验证 ===');
    
    try {
        const { docId, processInstanceId } = await createTestDocument('权限隔离测试');
        
        // 快速通过前面的审批节点
        const managerTask = await waitForTask(tokens.manager, processInstanceId);
        if (managerTask) {
            await makeRequest('POST', `/oa/workflow/task/complete/${managerTask.taskId}`, {
                variables: { result: 'approve' },
                comment: '办公室主任审批'
            }, tokens.manager);
        }
        
        const secretaryTask = await waitForTask(tokens.secretary, processInstanceId);
        if (secretaryTask) {
            await makeRequest('POST', `/oa/workflow/task/complete/${secretaryTask.taskId}`, {
                variables: { 
                    result: 'approve',
                    selectedLeaders: ['leader_admin'] // 只选择张副主任
                },
                comment: '书记审批，只选择张副主任'
            }, tokens.secretary);
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 验证张副主任能看到任务
        const adminTask = await waitForTask(tokens.leaderAdmin, processInstanceId);
        if (adminTask) {
            addTestResult('权限隔离验证', true, '张副主任正确收到任务', users.leaderAdmin.name);
        } else {
            addTestResult('权限隔离验证', false, '张副主任未收到任务', users.leaderAdmin.name);
        }
        
        // 验证其他分管领导看不到任务
        const financeTask = await waitForTask(tokens.leaderFinance, processInstanceId, 2);
        if (!financeTask) {
            addTestResult('权限隔离验证', true, '李副主任正确看不到任务（权限隔离成功）', users.leaderFinance.name);
        } else {
            addTestResult('权限隔离验证', false, '李副主任能看到任务（权限隔离失败）', users.leaderFinance.name);
        }
        
    } catch (error) {
        addTestResult('权限隔离验证', false, error.message, '系统');
    }
}

// 测试场景3：异常处理
async function testExceptionHandling() {
    console.log('\n=== 测试场景3：异常处理 ===');
    
    try {
        // 测试拒绝功能
        const { docId: rejectDocId, processInstanceId: rejectProcessId } = await createTestDocument('拒绝测试');
        
        const managerTask = await waitForTask(tokens.manager, rejectProcessId);
        if (managerTask) {
            const rejectResult = await makeRequest('POST', `/oa/workflow/task/complete/${managerTask.taskId}`, {
                variables: { result: 'reject' },
                comment: '收文内容需要重新整理'
            }, tokens.manager);
            
            if (rejectResult.code === 200) {
                addTestResult('拒绝功能', true, '拒绝功能正常工作', users.manager.name);
            }
        }
        
        // 测试回退功能
        const { docId: returnDocId, processInstanceId: returnProcessId } = await createTestDocument('回退测试');
        
        const managerTask2 = await waitForTask(tokens.manager, returnProcessId);
        if (managerTask2) {
            await makeRequest('POST', `/oa/workflow/task/complete/${managerTask2.taskId}`, {
                variables: { result: 'approve' },
                comment: '办公室主任审批'
            }, tokens.manager);
            
            const secretaryTask = await waitForTask(tokens.secretary, returnProcessId);
            if (secretaryTask) {
                const returnResult = await makeRequest('POST', `/oa/workflow/task/complete/${secretaryTask.taskId}`, {
                    variables: { result: 'return' },
                    comment: '需要回退到上一步重新审核'
                }, tokens.secretary);
                
                if (returnResult.code === 200) {
                    addTestResult('回退功能', true, '回退功能正常工作', users.secretary.name);
                }
            }
        }
        
    } catch (error) {
        addTestResult('异常处理测试', false, error.message, '系统');
    }
}

// 测试场景4：性能测试
async function testPerformance() {
    console.log('\n=== 测试场景4：性能测试 ===');
    
    const startTime = Date.now();
    const concurrentTests = [];
    
    // 创建10个并发测试
    for (let i = 0; i < 10; i++) {
        concurrentTests.push(createTestDocument(`性能测试-${i}`));
    }
    
    try {
        const results = await Promise.all(concurrentTests);
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        addTestResult('并发创建测试', true, `10个并发请求耗时: ${duration}ms`, '系统');
        addTestResult('性能测试', true, `平均响应时间: ${duration / 10}ms`, '系统');
        
    } catch (error) {
        addTestResult('性能测试', false, error.message, '系统');
    }
}

// 主执行函数
async function main() {
    try {
        console.log('🚀 开始收文普通流程完整自动化测试...\n');
        
        // 用户登录
        console.log('=== 用户登录 ===');
        for (const [key, user] of Object.entries(users)) {
            try {
                tokens[key] = await login(user.username, user.password);
                addTestResult('用户登录', true, '登录成功', user.name);
            } catch (error) {
                addTestResult('用户登录', false, error.message, user.name);
                throw error;
            }
        }
        
        // 执行测试场景
        await testCompleteWorkflow();
        await testPermissionIsolation();
        await testExceptionHandling();
        await testPerformance();
        
        // 生成测试报告
        console.log('\n=== 📊 收文普通流程测试报告 ===');
        const totalTests = testResults.length;
        const passedTests = testResults.filter(r => r.success).length;
        const failedTests = totalTests - passedTests;
        
        console.log(`总测试数: ${totalTests}`);
        console.log(`通过: ${passedTests}`);
        console.log(`失败: ${failedTests}`);
        console.log(`通过率: ${((passedTests / totalTests) * 100).toFixed(2)}%`);
        
        if (failedTests > 0) {
            console.log('\n失败的测试:');
            testResults.filter(r => !r.success).forEach(r => {
                console.log(`  ❌ ${r.testName} (${r.user}): ${r.message}`);
            });
        }
        
        console.log('\n=== 🎯 收文普通流程功能验证总结 ===');
        console.log('✅ 完整8节点审批流程');
        console.log('✅ 多级人员选择功能');
        console.log('✅ 权限隔离机制');
        console.log('✅ 异常处理（拒绝、回退）');
        console.log('✅ 并发性能测试');
        console.log('✅ 文档状态更新');
        
        if (failedTests === 0) {
            console.log('\n🎉 收文普通流程测试全部成功！');
            console.log('✨ 收文普通流程功能完整，可以直接交付客户使用！');
        } else {
            console.log('\n⚠️  部分收文普通流程测试失败，需要检查相关功能');
        }
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
    } finally {
        // 清理测试数据
        if (testDocuments.length > 0) {
            console.log('\n=== 清理测试数据 ===');
            let cleanupCount = 0;
            for (const docId of testDocuments) {
                try {
                    await makeRequest('DELETE', `/oa/document/receive/${docId}`, null, tokens.clerk);
                    cleanupCount++;
                } catch (error) {
                    console.warn(`清理文档${docId}失败: ${error.message}`);
                }
            }
            console.log(`✅ 已清理${cleanupCount}个测试文档`);
        }
    }
}

main();
