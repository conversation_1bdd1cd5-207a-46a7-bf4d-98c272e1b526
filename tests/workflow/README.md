# OA工作流程自动化测试套件

## 📋 概述

本测试套件包含OA系统三个核心工作流程的完整自动化测试：

1. **发文流程测试** - 7节点完整审批链路
2. **收文普通流程测试** - 8节点链路和多级人员选择
3. **收文特批流程测试** - 2节点简化链路和灵活人员指定

## 🗂️ 文件结构

```
tests/workflow/
├── README.md                          # 测试说明文档
├── run_all_tests.js                   # 测试运行器
├── test_send_workflow.js              # 发文流程测试
├── test_receive_workflow.js           # 收文普通流程测试
└── test_receive_special_workflow.js   # 收文特批流程测试
```

## 🚀 快速开始

### 运行所有测试
```bash
# 在项目根目录下运行
node tests/workflow/run_all_tests.js
```

### 运行单个测试
```bash
# 发文流程测试
node tests/workflow/test_send_workflow.js

# 收文普通流程测试
node tests/workflow/test_receive_workflow.js

# 收文特批流程测试
node tests/workflow/test_receive_special_workflow.js
```

### 运行指定测试
```bash
# 通过测试运行器运行指定测试
node tests/workflow/run_all_tests.js send        # 发文流程
node tests/workflow/run_all_tests.js receive     # 收文普通流程
node tests/workflow/run_all_tests.js special     # 收文特批流程
```

## 📊 测试内容

### 1. 发文流程测试 (test_send_workflow.js)

**测试场景：**
- ✅ 完整7节点审批流程
- ✅ 动态分管领导分配验证
- ✅ 异常处理（拒绝、回退）
- ✅ 性能并发测试
- ✅ 文档状态更新验证

**流程节点：**
1. 科员创建发文 → 2. 科室负责人审批 → 3. 办公室审批 → 4. 办公室主任审批 → 5. 分管领导审批 → 6. 书记审批 → 7. 办公室汇总

**验证功能：**
- 动态分管领导分配（人事科→张副主任，运管科→赵副主任，水调科→赵副主任）
- 拒绝和回退功能
- 并发处理能力
- 文档状态自动更新

### 2. 收文普通流程测试 (test_receive_workflow.js)

**测试场景：**
- ✅ 完整8节点审批流程
- ✅ 多级人员选择功能
- ✅ 权限隔离机制验证
- ✅ 异常处理（拒绝、回退）
- ✅ 性能并发测试
- ✅ 文档状态更新验证

**流程节点：**
1. 办公室收文登记（自动完成） → 2. 办公室主任审批 → 3. 书记审批（选择分管领导） → 4. 分管领导并行审批（选择科室负责人） → 5. 科室负责人并行审批（选择经办人） → 6. 经办人查阅确认 → 7. 办公室汇总

**验证功能：**
- 书记选择多个分管领导
- 分管领导选择多个科室负责人
- 科室负责人选择多个经办人
- 权限隔离（只有选择的人员能看到任务）
- 并行审批处理

### 3. 收文特批流程测试 (test_receive_special_workflow.js)

**测试场景：**
- ✅ 简化2节点特批流程
- ✅ 灵活人员指定功能
- ✅ 权限验证（书记专用）
- ✅ 多级别人员指定
- ✅ 性能测试
- ✅ 文档状态更新验证

**流程节点：**
1. 书记收文处理（指定处理人员） → 2. 指定人员处理

**验证功能：**
- 书记专用流程（只有书记能发起）
- 指定副主任处理
- 指定科室负责人处理
- 指定普通科员处理
- 流程选择权限验证

## 🎯 测试覆盖范围

### 核心功能测试
- ✅ **完整流程链路** - 所有节点正常流转
- ✅ **动态任务分配** - 根据规则自动分配任务
- ✅ **多级人员选择** - 支持灵活的人员选择机制
- ✅ **权限隔离控制** - 确保任务权限正确隔离
- ✅ **异常处理机制** - 拒绝、回退、催办功能
- ✅ **文档状态管理** - 自动更新文档状态

### 性能测试
- ✅ **并发处理能力** - 多个并发请求处理
- ✅ **响应时间测试** - 系统响应性能验证
- ✅ **稳定性测试** - 长时间运行稳定性

### 边界条件测试
- ✅ **数据验证** - 输入数据格式验证
- ✅ **权限边界** - 权限控制边界测试
- ✅ **异常场景** - 各种异常情况处理

## 📈 测试报告

测试运行后会生成详细的测试报告，包括：

- **总体统计** - 总测试数、通过数、失败数、通过率
- **各测试套件结果** - 每个流程的详细测试结果
- **功能验证总结** - 各项功能的验证状态
- **性能指标** - 响应时间和并发处理能力
- **问题诊断** - 失败测试的详细信息

## 🔧 环境要求

### 系统要求
- Node.js 14+ 
- 后端服务运行在 http://localhost:8080
- MySQL数据库正常连接
- 所有测试用户账号已创建并配置正确角色

### 测试用户配置
测试需要以下用户账号：

**办公室人员：**
- clerk_office1 (办公室小王)
- clerk_office2 (办公室小李)  
- manager_office (办公室主任)

**科员：**
- clerk_hr1 (人事科小赵)
- clerk_operation1 (运管科小钱)
- clerk_hydro1 (水调科小孙)

**科室负责人：**
- manager_hr (人事科长)
- manager_operation (运管科长)
- manager_hydro (水调科长)

**分管领导：**
- leader_admin (张副主任)
- leader_operation (赵副主任)
- leader_finance (李副主任)

**书记：**
- secretary (王书记)

## 🛠️ 故障排除

### 常见问题

1. **登录失败**
   - 检查用户账号是否存在
   - 检查密码是否正确
   - 检查后端服务是否运行

2. **任务未找到**
   - 检查流程是否正确启动
   - 检查任务分配逻辑
   - 检查用户权限配置

3. **权限验证失败**
   - 检查用户角色配置
   - 检查部门分配
   - 检查监听器配置

4. **文档状态未更新**
   - 检查流程是否完整结束
   - 检查状态更新逻辑
   - 检查数据库连接

### 调试建议

1. **查看后端日志** - 检查服务器端的详细日志
2. **单步调试** - 逐个运行测试场景
3. **数据库检查** - 直接查询数据库状态
4. **网络检查** - 确认网络连接正常

## 📝 维护说明

### 添加新测试
1. 在对应的测试文件中添加新的测试场景函数
2. 在main函数中调用新的测试场景
3. 更新测试说明文档

### 修改测试数据
1. 修改用户配置部分
2. 更新测试文档数据
3. 调整验证逻辑

### 扩展测试覆盖
1. 添加新的边界条件测试
2. 增加性能测试场景
3. 完善异常处理测试

## 🎉 测试目标

通过完整的自动化测试，确保：

- **功能完整性** - 所有工作流程功能正常
- **性能可靠性** - 系统性能满足要求
- **稳定性** - 长期运行稳定可靠
- **用户体验** - 操作流程顺畅自然
- **交付质量** - 达到生产环境标准

**目标通过率：≥ 95%**

---

*最后更新：2025-01-27*
